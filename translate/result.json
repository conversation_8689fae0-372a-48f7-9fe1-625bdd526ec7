{"全部收起": "put away", "全部展开": "expand all", "将所选达人分配给下属员工": "Assign selected talents to subordinate employees", "（员工可在“分配给我的”达人中查看）": "(Employees can be viewed in the \"Assigned to Me\" list)", "将所选达人取消分配": "Unassign the selected talent", "（达人将回归至公海）": "(Talent will return to the high seas)", "您希望对这些{{label}}：": "You would like to provide these {{label}}:", "您希望对前{{total}}个{{label}}：": "You want to check the first {{total}}{{label}}:", "当前条件下，共检索出{{total}}个{{label}}": "Under current conditions, a total of {{total}}{{label}}} were retrieved", "全量操作一次最多操作{{limit}}个{{label}}": "Maximum of {{limit}}{{label}}} can be operated at one time", "一次最多导出{{total}}个{{label}}": "Export up to {{total}}{{label}} at a time", "请确认是否继续导出前{{total}}个{{label}}": "Please confirm whether to continue exporting the first {{total}}{{label}}", "对筛选出的{{label}}的信息进行更新": "Update the information of the filtered {{label}}", "系统会对所选的{{total}}个{{label}}进行信息更新：": "The system will update the information of the selected {{total}}{{label}}:", "1.会更新{{label}}的头像、粉丝数、视频数、视频点赞数": "1.{{label}}'s avatar, fan count, video count, and video likes count will be updated", "2.如果{{label}}正在直播，会更新该场直播的观看人数、流水": "2. If {{label}} is currently streaming, the number of viewers and flow of the live broadcast will be updated", "确定屏蔽搜索出的 {{count}} 个{{label}}吗？": "Are you sure you will block the {{count}}{{label}}} found?", "刷榜大哥": "Big Brother Brush the List", "指定关注数": "Specify number of concerns", "不限关注数": "Unlimited number of followers", "最小关注数": "Minimum number of followers", "最大关注数": "Maximum number of followers", "手机账号关注": "Mobile account attention", "手机账号视频分享": "Mobile account video sharing", "检测手机账号可用性": "Check mobile account availability", "全部账号": "All accounts", "依据时区关键词检索": "Search based on time zone keywords", "普通用户": "ordinary users", "视频达人": "video master", "直播达人": "famous streamer", "尚未创建": "not yet created", "标签可用于对": "Labels can be used to identify", "进行标记、分类、筛选等用途": "For labeling, classification, screening, etc.", "标签中的数字代表关联的": "Numbers in the label represent associated", "数量": "number", "没有找到 “{{bizCode}}” 对应的流程": "No process corresponding to \"{{bizCode}}\" was found", "流程 “{{name}}” 不需要额外指定输入变量": "Process '{{name}}' does not require additional input variables to be specified", "执行流程": "execution flow", "执行 KOL 内置的一些流程": "Implement some processes built into KOL", "选择流程": "selection process", "现阶段为您提供以下自动化流程": "At this stage, we provide you with the following automated processes", "浏览器自动化流程": "Browser automation process", "直播间加热": "Heating in the direct broadcast room", "通过若干浏览器账号对直播间进行加热": "Heating the live broadcast room through several browser accounts", "视频加热": "video heating", "通过若干浏览器账号对指定的视频进行加热": "Heating the specified videos through several browser accounts", "手机 App 自动化流程": "Mobile App Automation Process", "通过若干手机账号对直播间进行加热": "Heating the live broadcast room through several mobile accounts", "通过若干手机账号对指定的视频进行加热": "Heating specified videos through several mobile accounts", "选择执行体": "Select executor", "请选择您要执行流程的分身": "Please select the avatar you want to perform the process", "请选择您要执行流程的手机账号": "Please select the mobile account you want to perform the process on", "输入变量": "input variables", "任务调度已传递给手机": "Task schedule has been passed to mobile phone", "任务调度已传递给本地客户端": "Task schedule passed to local client", "任务准确就绪": "The task is exactly ready", "任务准确就绪，点击\"完成“将为您执行任务": "The task is exactly ready, and clicking \"Finish\" will execute the task for you", "任务池可容纳的最大任务数量": "Maximum number of tasks that can be accommodated in the task pool", "任务池当前已有任务": "Task pool Currently existing tasks", "本次操作拟创建任务": "Task to be created in this operation", "实际创建任务": "Actual creation task", "任务池": "task pool", "正在为您创建“{{type}}”任务": "Creating a \"{{type}\" task for you", "已为您创建“{{type}}”任务": "A \"{{type}\" task has been created for you", "关闭（": "Close (", "确定要删除此代金券吗？": "Are you sure you want to delete this voucher?", "贴子数": "Number of posts", "最近同步时间": "latest synchronization time", "打开Instagram账号": "Open Instagram account", "复制用户名称": "Copy user name", "Instagram账号基本信息": "Instagram account basic information", "打开此达人的Instagram首页": "Open this celebrity's Instagram page", "达人名称": "Talent name", "帖子数量": "number of posts", "达人范围": "Talent range", "公海的达人": "Talent of the high seas", "团队关注的达人": "People the team pays attention to", "已建联": "<PERSON>", "暂无数据，请确认管理员是否有给您分配达人": "No data yet. Please confirm whether the administrator has assigned you a talent.", "唤醒Instagram": "Wake up Instagram", "Instagram唤醒方式：": "How to wake up on Instagram:", "Instagram协议唤醒": "Instagram protocol wake-up", "直接唤醒Instagram": "Wake up Instagram directly", "唤醒Instagram时自动将达人设置为已触达": "Automatically set the talent as reached when you wake up Instagram", "TikTok唤醒页面": "TikTok wake-up page", "达人首页": "Top page", "达人直播间": "Talent Live Broadcast Room", "（取前{{count}}个）": "(Take the first {{count}})", "分配给我的": "assigned to me", "当前条件可查询到 {{count}} 个达人": "You can find {{count}} people for the current conditions", "取筛选过的前 {{count}} 个": "Take the first {{count}} filtered", "共{{count}}个筛选条件": "There are {{count}} filters in total", "普通员工可以创建”我的自动计划“，但创建的自动计划只能针对“分配给我的达人”": "Ordinary employees can create \"my automated plans\", but the automated plans you create can only be targeted at \"people assigned to me\"", "系统自动计划只能由超管或BOSS创建，可针对全部的公海达人，以及全部的经纪人账号与手机账号；普通员工可以创建自己的自动计划": "System automatic plans can only be created by supermanagers or bosses, and can target all high-seas talents, as well as all broker accounts and mobile accounts; ordinary employees can create their own automatic plans", "建联手段": "Jianlian means", "养号目标": "Goal of raising number", "账号权重": "Account weight", "单账号养号时长": "Duration of maintaining a single account", "评论话术": "commentary", "自定义话术": "Custom speech", "AI生成": "AI-generated", "观看视频数量": "Number of videos viewed", "分享视频位置": "Share video location", "第{{index}}个": "{{index}}", "守候手段": "means of waiting", "更新手段": "update means", "待检测账号": "Account to be tested", "{{total}} 个": "{{total}}", "重复周期": "repetition period", "创建新的自动计划": "Create a new automated schedule", "官方{{addOn}}账号": "Official {{addOn}} account", "经纪人{{addOn}}账号": "Broker {{addOn}} account", "普通{{addOn}}账号": "Ordinary {{addOn}} account", "手机视频分享": "Mobile video sharing", "修改{{who}}自动计划": "Modify the {{who}} automatic schedule", "我的": "my", "创建{{who}}自动计划": "Create {{who}} automatic schedule", "手机私信账号": "Mobile phone private message account", "您可以针对不同的手机账号使用不同的邀约话术": "You can use different solicitation techniques for different mobile accounts", "您可以针对不同的经纪人账号使用不同的邀约话术（请注意，仅针对打有标签“{{tag}}”的浏览器分身才有作用）": "You can use different solicitation techniques for different broker accounts (please note that it only works for browser clones marked with \"{{tag}}\")", "指定话术分组  ：": "Specify speech groups:", "请指定分组": "Please specify grouping", "每台手机账号数量": "Number of accounts per mobile phone", "手机数量": "number of mobile phones", "您只需输入{{label}}和所选日期，系统会根据左边的配置参数，自动计算出来一天最多可发送的私信数量": "You only need to enter {{label}} and the selected date, and the system will automatically calculate the maximum number of private messages that can be sent in one day based on the configuration parameters on the left", "全天候": "all-weather", "私信发送过快时的标签": "Tag when a private message is sent too fast", "账号被封控时的标签": "The label when the account is blocked", "账号被封禁时的标签": "The label when the account is blocked", "账号未登录时的标签": "Tag when the account is not logged in", "需要调整隐私设置时的标签": "Labels when you need to adjust privacy settings", "只有在蓝色方块的时间内才允许执行邀约建联相关流程": "The relevant procedures for inviting <PERSON><PERSON><PERSON> are allowed only during the time specified in the blue square", "如果有多个经纪人账号可以发送私信，最多同时允许多少个经纪人账号同时发送，建议设置为1，避免出现私信过快等情况，如果设置为0则不限制": "If there are multiple broker accounts that can send private messages, the maximum number of broker accounts allowed to send private messages at the same time is recommended to set it to 1 to avoid situations such as too fast private messages. If it is set to 0, there is no limit.", "每批最多可以发送多少私信，发送完后需要间隔一定时间才允许继续发送": "The maximum number of private messages can be sent in each batch, and a certain interval will be needed after sending before sending can be continued", "发送完一批私信后，需要间隔多长时间才允许下一批次的发送": "After sending a batch of private messages, how long does it take to allow the next batch to be sent?", "每一个经纪人账号每天最多可以发送多少私信": "How many private messages can each broker account send per day?", "每次执行经纪人账号后台私信的邀约建联流程时，会从含有此标签的浏览器分身中选取": "Every time you execute the solicitation process of a private message from your broker account background, you will select it from the browser avatar containing this tag.", "当发送私信过快时，会给浏览器分身打上此标签，可自行解除此标签": "When sending a private message too fast, this label will be marked to the browser avatar, and you can remove this label yourself", "当经纪人账号被封控时，会给相应的浏览器分身打上此标签，可自行解除此标签": "When a broker account is blocked, the corresponding browser clone will be marked with this label, and this label can be removed by itself", "当经纪人账号被封禁时，会给相应的浏览器分身打上此标签，可自行解除此标签": "When a broker account is blocked, the corresponding browser avatar will be labeled with this label, and you can cancel this label yourself", "如果经纪人账号未登录，会给相应的浏览器分身打上此标签，需要您自行登录": "If the broker account is not logged in, the corresponding browser avatar will be marked with this label and you will need to log in yourself", "每一个手机账号每天最多可以发送多少私信": "How many private messages can each mobile account send per day?", "每次执行手机号相关流程时，如发送私信、分享视频、账号关注等，会从含有此标签的手机账号中选取 ": "Every time you perform mobile phone-related processes, such as sending private messages, sharing videos, following accounts, etc., you will select them from the mobile phone account that contains this tag. ", "当发送私信过快时，会给手机账号打上此标签，可自行解除此标签，或通过“检测账号可用性”流程自动解除": "When a private message is sent too fast, this label will be marked to the mobile phone account, which can be removed by yourself or automatically cancelled through the \"Check Account Availability\" process", "当账号被封控时，会给手机账号打上此标签，可自行解除此标签，或通过“检测账号可用性”流程自动解除": "When an account is blocked, the mobile account will be marked with this label, which can be removed by itself or automatically through the \"Check Account Availability\" process", "当账号被封禁时，会给手机账号打上此标签，可自行解除此标签，或通过“检测账号可用性”流程自动解除": "When an account is blocked, the mobile account will be labeled with this label, which can be removed by yourself or automatically through the \"Check Account Availability\" process", "如果TikTok未登录，会给手机账号打上此标签，需要您自行登录": "If <PERSON>ikTok is not logged in, this label will be marked to your mobile account, and you will need to log in yourself", "当账号未设置成“允许所有人都可以私信我”时，会给手机账号打上此标签，需要您自行设置手机账号的隐私设置": "When the account is not set to \"Allow everyone to trust me privately\", the mobile account will be marked with this label, and you need to set the privacy settings of the mobile account yourself.", "“手机账号私信”流程与“手机账号视频分享”流程均会参考下述设置，": "Both the \"Mobile Account Private Message\" process and the \"Mobile Account Video Sharing\" process will refer to the following settings,", "私信普通账号_浏览器": "Private Message Ordinary Account_Browser", "私信官方账号_浏览器": "Private Message Official Account_Browser", "私信经纪人账号_浏览器": "Private message broker account_browser", "私信普通账号_手机": "Private Message Ordinary Account_Mobile Phone", "最近开播时间": "Recent launch time", "请设置最近开播时间": "Please set the latest launch time", "最近开播时间晚于上述时间的主播才允许抓取到您的团队，最小值为1天，最大值为30天，请注意，最近天数要求的越小，抓取的主播数量越少": "Only anchors whose recent launch time is later than the above time are allowed to crawl to your team. The minimum value is 1 day and the maximum value is 30 days. Please note that the smaller the most recent number of days, the fewer anchors will be crawled", "请同步更换抓取主播ID流程所使用的浏览器分身的节点IP地址": "Please simultaneously change the node IP address of the browser avatar used in the process of grabbing anchor ID", "请同步更换用来筛选主播达人是否能够被邀约的经纪人账号": "Please simultaneously change the agent account used to screen whether anchors can be invited", "请同步更换用来抓取主播ID流程所使用的TikTok账号": "Please simultaneously change the TikTok account used to grab the anchor ID process", "请注意，一个月只能更改{{count}}次": "Please note that you can only change {{count}} times a month", "使用经纪人后台的话术模板": "Use the broker's backstage speaking template", "AI生成评论": "AI generates comments", "从视频的评论内容中抓取10条评论，喂给 ChatGPT 生成相应评论，如果评论内容少于10条，则不生成任何评论": "Grab 10 comments from the comment content of the video and feed them to ChatGPT to generate corresponding comments. If the comment content is less than 10, no comments will be generated", "使用在花漾中设置的话术模板": "Use the speech template set up in Hua Yang", "优先使用经纪人账号设置的话术，否则使用下述话术：": "Use the words set by the broker account first, otherwise use the following words:", "优先使用手机账号设置的话术，否则使用下述话术：": "Give priority to the words set by your mobile account, otherwise use the following words:", "话术分组": "speech grouping", "请选择分组": "Please select grouping", "可选话术": "Choose your words", "请选择话术": "Please choose your speaking technique", "在发送私信时": "When sending a private message", "通过经纪人账号发送私信时": "When sending a private message through a broker account", "在发送手机私信时": "When sending a private message on your mobile phone", "通过普通账号发送私信时": "When sending a private message through a normal account", "通过官方账号发送私信时": "When sending a private message through an official account", "{{prefix}}，通过对达人进行浏览、点赞、收藏等行为，降低封号风险：": "{{prefix}}, reduce the risk of being banned by browsing, liking, and collecting talents:", "除了关注达人以外，您还可以决定是否进行以下辅助行为：": "In addition to paying attention to people, you can also decide whether to perform the following auxiliary behaviors:", "请注意，只有关注达人后，才能够分享视频给他/她：": "Please note that you can only share videos with someone after following him/her:", "取值范围1 - 6": "Value range 1 - 6", "首页的第几个视频：": "How many videos on the front page:", "达人关注概率": "Talent attention probability", "每视频观看时长": "Duration per video viewing", "{{count}}秒": "{{count}} seconds", "请指定观看时长": "Please specify the viewing duration", "视频点赞概率": "Video likes probability", "视频收藏概率": "Video collection probability", "视频评论概率": "Video comment probability", "评论辅助表情": "Comment auxiliary emoji", "在内容中添加一些辅助表情（使内容产生差异化）的概率": "The probability of adding auxiliary emoticons to the content (making the content differentiated)", "视频评论内容": "Video comment content", "指定话术分组": "designated speech grouping", "从视频的评论内容中抓取10条评论，喂给ChatGPT生成相应评论，如果评论内容少于10条，则不生成任何评论": "Grab 10 comments from the comment content of the video and feed them to ChatGPT to generate corresponding comments. If the comment content is less than 10, no comments will be generated", "原则上，官方账号有且只有一个，您需要为相应的浏览器分身打上标签“{{tag}}”": "In principle, there is only one official account, and you need to tag the corresponding browser avatar \"{{tag}}\"", "使用的账号": "Account used", "使用与该达人最近沟通用到的账号": "Use the account used to communicate with the person recently", "如果从未有账号联系过或者最近一次用到的账号包含有标签{{tags}}，则分配新的账号": "If no account has ever been contacted or the last account used contains tags {{tags}}, assign a new account", "强制分配新的账号": "Force the allocation of new accounts", "强制指定账号": "Mandatory account designation", "如果强制使用某个账号，请不要选择过多的达人，否则，受到私信发送频率的限制，会导致发送私信的间隔过长": "If you are forced to use a certain account, please do not select too many people. Otherwise, due to the restriction on the frequency of sending private messages, the interval between sending private messages will be too long", "分配原则": "distribution principle", "带有标签“{{tag}}”的账号": "Account with tag \"{{tag}\"", "请注意：会忽略带有标签{{tags}}的账号": "Please note: Accounts with tags {{tags}} will be ignored", "带有标签“{{tag}}”和以下标签的账号": "Accounts with the tag \"{{tag}}\" and the following tags", "如果该达人已经与某个账号联系过，则使用联系过的账号，如果该达人从未联系过，则使用下述原则：": "If the person has already contacted an account, the contacted account will be used. If the person has never contacted, the following principles will be used:", "从带有标签“{{tag}}”的账号中随机选择": "Select randomly from accounts with the tag \"{{tag}\"", "强制使用指定账号：": "Mandatory use of specified account:", "经纪人账号分配原则": "Broker account allocation principles", "官方账号分配原则": "Official account allocation principles", "普通账号分配原则": "Common account allocation principles", "关注行为": "concerning behavior", "分享内容": "share content", "辅助行为": "assistant behavior", "Instagram达人邀约话术": "Instagram celebrity invitation skills", "TikTok评论话术": "TikTok commentary", "话术分组名称": "Speech group name", "请输入话术分组名称": "Please enter the speech group name", "确定要删除此话术分组吗？": "Are you sure you want to delete this speech group?", "删除话术分组会同步删除此分组下的所有话术": "Deleting a speech group will simultaneously delete all speeches under this group", "新分组": "new packet", "创建新的话术分组": "Create new speech groups", "新话术": "Xinshu", "达人信息更新": "Talent information update", "每分身每次更新达人数": "Number of updates per avatar", "KOL私域管理系统": "KOL Private Domain Management System", "系统自动计划": "System automatic planning", "Instagram配置": "Instagram configuration", "Instagram达人": "Instagram Talent", "无法打开未设置IP隔离的浏览器分身": "Unable to open a browser avatar without IP isolation set", "请在花漾客户端内完成对浏览器分身的IP隔离设置": "Please complete the IP isolation settings for the browser avatar in Huayang client", "手机私信数量": "Number of private messages on mobile phones", "浏览器私信数量": "Number of browser private messages", "您也无需担心同一时间同时执行若干个不同类型的流程任务而产生的干扰问题，此时会由任务池根据不同流程任务的优先级自行调度。": "You also don't need to worry about the interference caused by executing several different types of process tasks at the same time. At this time, the task pool will schedule itself according to the priorities of different process tasks.", "更进一步信息，请您阅读 {{link}} 一文": "For more information, please read the article {{link}}", "任务池与历史任务": "Task pool and historical tasks", "会更新该达人的头像、粉丝数、关注数、帖子数": "The avatar, number of fans, number of followers, and number of posts will be updated", "自动计划最多同时处理 10000 个达人": "Automatic planning can handle up to 10000 talents at the same time", "最近开播": "Recently launched", "最近3天开过播": "It has been broadcast in the last three days", "最近7天开过播": "It has been broadcast in the last 7 days", "最近30天开过播": "It has been broadcast in the past 30 days", "手机账号私信": "Mobile account private message", "通过手机中的 {{name}} App 向达人发送私信": "Send private messages to talented people through the {{name}} App on your mobile phone", "经纪人账号后台私信": "Broker account background private message", "通过经纪人账号登录公会后台向达人发送邀约请求": "Log in to the guild backstage through your broker account and send invitation requests to the talent", "官方账号浏览器私信": "Official account browser private message", "官方账号是指公会授权的 TikTok 账号，代表公会品牌标识": "The official account refers to the TikTok account authorized by the guild and represents the guild brand identity", "普通账号浏览器私信": "Ordinary account browser private message", "用浏览器发送私信（不推荐）": "Send private messages using your browser (not recommended)", "通过手机中的 {{name}} App 关注达人并打招呼": "Follow and say hello through the {{name}} App on your mobile phone", "将您的视频分享给达人，以引起达人的注意": "Share your video with talented people to attract their attention", "与所选达人建立联系，引起达人的注意与回复": "Establish contact with selected talent and attract their attention and response", "与所选达人邀约建联": "<PERSON><PERSON><PERSON> with selected talents", "建联方式": "Jianlian Way", "请选择适合的建联方式": "Please choose the right method for <PERSON><PERSON><PERSON>", "流程任务已创建": "Process task created", "您可到任务池中查看详细信息": "You can check the details in the task pool", "拟对 {{count}} 个达人创建“邀约建联”的流程任务": "The process task of creating an \"invitation to <PERSON><PERSON><PERSON>\" for {{count}} individuals", "点击“完成”按钮后，系统会在 {{link}} 中创建 {{count}} 个流程任务，这些任务会按照流程的优先级、在允许执行的时间段内分批调度、依次执行": "After clicking the \"Finish\" button, the system will create {{count}} process tasks in {{link}}. These tasks will be scheduled in batches and executed in sequence according to the priority of the process and within the allowed execution time period", "消息发送成功将这批达人的状态更改为": "The message was successfully sent, changing the status of this group of talents to", "将这批达人的状态更改为": "Change the status of this group of talents to", "为这批达人打上标签，方便日后查找": "Label this group of talents for easy search in the future", "请输入标签": "Please enter the label", "关键词列表": "keyword list", "请输入多个和您希望养成类别相关的标签，如您希望养成和宠物相关的账号，则请输入：cute cats,cats,dog,pet 等多个关键词": "Please enter multiple tags related to the category you want to develop. If you want to develop an account related to pets, please enter multiple keywords such as cute cats,cats,dog,pet, etc.", "请指定关键词": "Please specify keywords", "多个关键词以逗号分割": "Multiple keywords are separated by commas", "养号流程已触发": "The number maintenance process has been triggered", "可在TikTok账号属性开启自动养号计划": "You can open the automatic account maintenance plan in TikTok account attributes", "TikTok账号属性": "TikTok account properties", "账号标签：": "Account label:", "让 TK 平台给指定的手机账号打上特定标签": "Let the TK platform label specific mobile accounts", "多个关键词以英文逗号分割": "Multiple keywords are separated by English commas", "账号权重：": "Account weight:", "提升账号权重，尽可能避免账号封控的风险": "Increase account weight and avoid the risk of account lockdown as much as possible", "未找到可用的养号流程": "No available number maintenance process found", "账号名称：": "Account name:", "最近联系的手机账号": "Recently contacted mobile accounts", "分配我的刷榜大哥": "Assign my big brother to brush the list", "修改私信设置": "Modify private message settings", "主播私信设置已修改": "Anchor private message settings have been modified", "针对当前搜索条件下的所有刷榜大哥进行操作": "Operate for all big brothers who swipe the list under the current search conditions", "最近联系的经纪人账号": "Recently contacted broker accounts", "最近更新时间：": "Last update time:", "主播私信已修改": "The anchor's private message has been modified", "账号清理流程已触发": "Account cleanup process has been triggered", "手机 TikTok 账号清理": "Mobile TikTok account cleanup", "当手机 TikTok 账号关注过多达人时会影响邀约效率，此时可对手机账号进行清理": "When your mobile TikTok account pays too much attention to celebrities, it will affect the efficiency of the invitation. You can clean up your mobile account at this time.", "取消所有非粉丝达人的关注": "Cancel attention from all non-fan celebrities", "取消所有粉丝的关注": "Cancel all fan attention", "清空所有的聊天记录": "Empty all chats", "未找到可用的清理流程": "No available cleanup process found", "TikTok 账号标签有着具体的含义，可在 {{link}} 中进行定义": "The TikTok account label has specific meaning, which can be defined in {{link}}", "系统设置": "system settings", "当开启自动养号后，会在指定的时间内自动执行养号流程": "When automatic number maintenance is enabled, the number maintenance process will be automatically executed within a specified time", "对 TikTok 账号进行批量设置，会影响所选账号的具体设置": "Batch settings for TikTok accounts will affect the specific settings of the selected account", "当开启自动养号后，在手机允许的养号时间段内，会自动执行养号流程": "When automatic number maintenance is enabled, the number maintenance process will be automatically executed during the number maintenance time allowed by the mobile phone", "自动养号": "Automatic number maintenance", "您需要根据账号的权重，自行调整下述参数": "You need to adjust the following parameters according to the weight of your account", "每日最多私信条数": "Maximum number of private messages per day", "每一个手机账号每天最多可以发送多少私信（包括手机视频分享）": "The maximum number of private messages per mobile account can send per day (including mobile video sharing)", "{{count}}条": "{{count}} items", "每批次最多私信条数": "Maximum number of private messages per batch", "每批最多可以发送多少私信（包括手机视频分享），发送完后需要间隔一定时间才允许继续发送，间隔时间在“手机属性-->工作间隔”中指定，请注意，间隔时间是以手机为单位的，换言之，如果a账号和b账号位于同一部手机，当a账号发送完一批私信后，即便需要b账号发送私信，也需要等待指定的时间间隔后才允许发送": "The maximum number of private messages (including mobile phone video sharing) can be sent in each batch. After sending, a certain interval is required before continuing sending. The interval is specified in \"Mobile Phone Properties--> Work Interval\". Please note that the interval is based on mobile phones. In other words, if account a and account b are located on the same mobile phone, when account a has sent a batch of private messages, even if account b needs to send private messages, it will need to wait for the specified interval before sending them.", "可为 TikTok 账号指定不同的话术": "Different speaking skills can be specified for your TikTok account", "邀约话术分组": "Invitational speaking group", "TikTok 账号设置": "TikTok account settings", "TikTok 账号批量设置": "Batch setup of TikTok accounts", "养号设置": "Account setting", "发送私信设置": "Send private message settings", "分享名片设置": "Share business card settings", "每日最多分享条数": "Maximum number of shares per day", "每一个手机账号每天最多可以分享多少达人名片": "How many business cards can each mobile account share every day?", "每批次最多分享条数": "Maximum number of shares per batch", "每批最多可以分享多少名片，分享完后需要间隔一定时间才允许继续分享，间隔时间在“手机属性-->工作间隔”中指定，请注意，间隔时间是以手机为单位的，换言之，如果a账号和b账号位于同一部手机，当a账号分享完一批名片后，即便需要b账号分享名片，也需要等待指定的间隔时间后才允许继续分享": "The maximum number of business cards can be shared in each batch. A certain interval is required after sharing before continuing sharing. The interval is specified in \"Mobile Phone Properties--> Work Interval\". Please note that the interval is based on mobile phones. In other words, if account a and account b are located on the same mobile phone, when account a shares a batch of business cards, even if account b needs to share business cards, it will need to wait for the specified interval before continuing sharing is allowed.", "对手机进行批量设置，会影响所选手机的具体设置": "Batch settings on mobile phones will affect the specific settings of the selected mobile phone", "可用性检查": "availability check", "检查时间": "inspection time", "请设置检查时间": "Please set the inspection time", "每 {{min}} 分钟": "Every {{min}} minute", "不检查": "not check", "机器人会给指定的目标达人发送私信，如果发送成功则清理相关标签，反之则打上相关标签": "The robot will send a private message to the designated target person. If the message is successful, the relevant tags will be cleared; otherwise, the relevant tags will be stamped", "当前Tiktok账号的第一个好友": "First friend on your current Tiktok account", "在当前团队公会主播中随机找一个主播": "Find an anchor randomly among the current team guild anchors", "指定达人": "Designated talent", "请指定目标达人": "Please specify a target", "手机ID": "mobile phone ID", "请输入手机名称": "Please enter your mobile phone name", "微信绑定状态": "WeChat binding status", "（只有在指定时间范围内才会检查是否有新消息）": "(Check for new messages only within the specified time frame)", "请选择接收人": "Please select the recipient", "工作间隔": "work interval", "手机相关设置": "Mobile phone-related settings", "手机批量设置": "Mobile phone batch settings", "设置手机执行对应流程的间隔": "Set the interval at which the phone executes the corresponding process", "发送私信每批次间隔": "Send private messages each batch interval", "请输入发送私信间隔": "Please enter the interval between sending private messages", "{{count}}分钟": "{{count}} minutes", "请输入分享卡片间隔": "Please enter the card sharing interval", "分享名片每批次间隔": "Share business cards at intervals of each batch", "分享完一批名片后，需要间隔多长时间才允许下一批次的名片分享": "After sharing a batch of business cards, how long does it take to allow the next batch of business cards to be shared?", "工作时间：": "Working hours:", "允许手机发送私信、手机账号关注、手机视频分享、手机名片分享等流程的执行时间": "Execution time for processes such as allowing mobile phones to send private messages, mobile phone account attention, mobile phone video sharing, and mobile phone business card sharing", "养号时间：": "Number maintenance time:", "执行手机养号流程的时间（需要开启自动养号）": "Time to execute the mobile phone number maintenance process (automatic number maintenance needs to be enabled)", "空闲时间：": "Free time:", "在此时间内不执行上述自动化流程，一般设置为员工工作时间段": "The above automated processes are not executed during this time period, and are generally set as employee working hours", "账号可用性检查流程已触发": "Account availability check process has been triggered", "机器人会给指定的目标达人发送私信，如果发送成功则清理相关标签，反之则打上相关标签；如果在此处指定目标达人，要求该手机中登录的所有TikTok号都要和该达人互为好友；如果留空，机器人会寻找一个好友发送私信": "The robot will send a private message to the designated target person. If the message is successful, the relevant tags will be cleared; otherwise, the relevant tags will be stamped; if the target person is specified here, all TikTok accounts logged in on the mobile phone are required to be friends with the target person. If you leave it blank, the robot will find a friend to send a private message", "（最小10分钟，最大20000分钟）": "(Minimum 10 minutes, maximum 20000 minutes)", "确定删除选中的 {{total}} 个账号吗？": "Are you sure you want to delete the selected {{total}} accounts?", "手机账号删除后，可通过同步手机账号流程予以恢复": "After the mobile account is deleted, it can be restored through the process of synchronizing the mobile account", "手机名称: {{mobileName}}": "Mobile phone name: {{mobileName}}", "手机属性": "Mobile phone attributes", "手机微信通知": "Mobile WeChat notification", "每日最多{{count}}": "Maximum {{count}} per day", "正在执行的流程": "Ongoing process", "账号清理": "Account cleanup", "分配给我的在线主播": "Assigned to my online anchors", "运行“直播广场主播在线守候流程“": "Run the \"Live Square Anchor Online Waiting Process\"", "最多1000": "up to 1000", "忽略当前团队已有的主播": "Ignore existing anchors on the current team", "运行“团队关注主播在线守候流程“": "Run the \"Team Focus on Anchor Online Waiting Process\"", "运行时长": "running time", "给主播打上标签": "Tag the anchor", "抓取到的正在开播的主播会自动进入到团队的公海达人，给这些主播打上标签方便您事后筛选": "The captured anchors that are currently broadcasting will automatically enter the team's high-seas talents, and label these anchors for later screening.", "只有超管与BOSS才允许执行此操作": "Only Superintendents and Bosses are allowed to perform this operation", "针对当前搜索条件下的所有用户进行操作": "Operate for all users under the current search conditions", "正在执行其它流程，这会导致检测账号可用性流程执行失败，请确保在该手机中结束其它所有流程后再重新执行＂": "Other processes are being executed, which will cause the process of checking account availability to fail. Please ensure that all other processes are completed on this phone before executing again.\"", "正在刷新审批结果": "Refreshing approval results", "查看申请的审批结果": "View the approval results of the application", "未知错误": "unknown error"}
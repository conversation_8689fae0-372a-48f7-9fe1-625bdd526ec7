/**
 * 将代码中的中文转换成I18N.t("")格式；
 *
 *
 * @param targetPath 默认扫描../../web-src/js/components/下的文件。有需要可以输入路径；
 *
 */

const fs = require('fs');
const path = require('path');
const babelTypes = require('@babel/types');
const traverse = require('@babel/traverse').default;
const generator = require('@babel/generator').default;
const { parse } = require('@babel/parser');
const { inExclude, writeFile, workWithFile } = require('./utils');
const exec = require('child_process').exec;

const dirs = ['../src/'];

const reg = /[\u4E00-\u9FA5]/;

// 语法解析可视化 https://astexplorer.net/

/**
 * 在i18n内不处理
 * @param node
 */
function isInI18N(node) {
  if (node && node.callee) {
    const { object } = node.callee;
    return ['I18N'].some((item) => object && object.name && item === object.name);
  }
  return false;
}
/**
 * 在i18n内不处理
 * @param node
 */
function isInT(node) {
  return node && node.callee && node.callee.name === 't';
}

/**
 * 在console内不处理
 * @param node
 */
function isInConsole(node) {
  if (node && node.callee) {
    const { object } = node.callee;
    return ['console'].some((item) => object && object.name && item === object.name);
  }
  return false;
}

/**
 * 去掉字符串两端的空格及换行
 * @param value
 */
function trim(value) {
  return value.replace(/\n/g, '').replace(/\s/g, '');
}

/**
 * 创建一个I18N.t语句
 * @param value
 * @return {CallExpression}
 */
function createOneI18NExpress(value) {
  const i18n = babelTypes.memberExpression(
    babelTypes.identifier('I18N'),
    babelTypes.identifier('t'),
  );
  const mapKey = trim(value);
  const cnKey = babelTypes.stringLiteral(mapKey);
  // if (mapKey.length >= 15) {
  //   console.warn(`超长字符串：${mapKey}`);
  // }
  console.log(mapKey, '替换');
  return babelTypes.callExpression(i18n, [cnKey]);
}

const importI18N = babelTypes.importDefaultSpecifier(babelTypes.identifier('I18N'));
const importI18NNode = babelTypes.importDeclaration(
  [importI18N],
  babelTypes.stringLiteral('@/i18n'),
);

function transform(filepath) {
  const file = fs.readFileSync(filepath, 'utf8').trimStart();
  if (!reg.test(file)) {
    console.log(filepath + '：没有检测到中文字符');
    return;
  }
  if (file.includes('module.exports')) {
    console.log(filepath + ':CommonJS 不处理');
    return;
  }
  // 去除注释后检测是否有中文
  try {
    const ast = parse(file, {
      sourceType: 'module',
      attachComment: false,
      plugins: ['jsx', 'typescript', 'classProperties'],
    });
    const { code } = generator(ast, {
      retainFunctionParens: true,
      jsescOption: { minimal: true, quotes: 'single' },
    });
    if (!reg.test(code)) {
      console.log(filepath + '：去除注释后没有检测到中文字符');
      return;
    }
  } catch (e) {
    console.log('parse file error: ' + filepath);
    return;
  }
  let ast = null;
  try {
    ast = parse(file, {
      sourceType: 'module',
      plugins: ['jsx', 'typescript', 'classProperties'],
    });
  } catch (e) {
    console.log('parse file error: ' + filepath);
    return;
  }
  if (ast.comments.length) {
    const comment = ast.comments[0];
    if (comment.loc.start.line === 1 && comment.value.trim() === '@i18n-ignore') {
      console.log(filepath + '：忽略国际化');
      return;
    }
  }
  let insertI18N = false;
  let hasUnTrans = false;

  traverse(ast, {
    ImportDeclaration: {
      enter(pathNode) {
        let hasI18NImport = false;
        const { node } = pathNode;
        const { specifiers } = node;
        for (let i = 0; i < specifiers.length; i++) {
          const specifier = specifiers[i];
          hasI18NImport = specifier.local.name === 'I18N';
        }
        if (!hasI18NImport && !insertI18N) {
          // 只执行一次
          pathNode.insertBefore(importI18NNode);
          insertI18N = true;
          pathNode.skip();
          return;
        }
        if (insertI18N && hasI18NImport) {
          if (pathNode.getPathLocation() !== 'program.body[0]') {
            // 删除I18N以后就没必要再遍历ImportDefaultSpecifier了
            pathNode.remove();
          }
        }
        pathNode.skip();
      },
    },
    CallExpression: {
      enter(pathNode) {
        if (pathNode.node.callee && pathNode.node.callee.name === 't') {
          // 将t()转换成I18N.t()
          pathNode.node.callee.name = 'I18N.t';
          pathNode.skip();
          hasUnTrans = true;
        }
      },
    },
    StringLiteral: {
      enter(pathNode) {
        const { node, parent } = pathNode;
        const { value } = node;
        if (reg.test(value)) {
          if (!isInI18N(parent) && !isInConsole(parent) && !isInT(parent)) {
            hasUnTrans = true;
            const express = createOneI18NExpress(value);
            const newNode = babelTypes.jsxExpressionContainer(express);
            if (babelTypes.isJSXAttribute(parent)) {
              // parent  则要给parent 的value包一层JSXExpressionContainer，再替换
              parent.value = newNode;
            } else if (babelTypes.isJSXExpressionContainer(parent)) {
              // parent  则替换自己I18N.t
              parent.expression = express;
            } else if (babelTypes.isObjectProperty(parent) || babelTypes.isClassProperty(parent)) {
              parent.value = express;
            } else if (babelTypes.isCallExpression(parent) || babelTypes.isNewExpression(parent)) {
              for (let i = 0; i < parent.arguments.length; i++) {
                if (parent.arguments[i] === node) {
                  parent.arguments[i] = express;
                  break;
                }
              }
            } else if (babelTypes.isArrayExpression(parent)) {
              for (let i = 0; i < parent.elements.length; i++) {
                if (parent.elements[i] === node) {
                  parent.elements[i] = express;
                  break;
                }
              }
            } else if (babelTypes.isAssignmentExpression(parent)) {
              parent.right = express;
            } else if (babelTypes.isVariableDeclarator(parent)) {
              parent.init = express;
            } else if (babelTypes.isConditionalExpression(parent)) {
              // consequent alternate
              if (parent.consequent === node) {
                parent.consequent = express;
              } else if (parent.alternate === node) {
                parent.alternate = express;
              }
            } else if (
              babelTypes.isReturnStatement(parent) ||
              babelTypes.isThrowStatement(parent)
            ) {
              parent.argument = express;
            } else if (
              babelTypes.isBinaryExpression(parent) ||
              babelTypes.isLogicalExpression(parent) ||
              babelTypes.isAssignmentPattern(parent)
            ) {
              // left right
              if (parent.right === node) {
                parent.right = express;
              } else if (parent.left === node) {
                parent.left = express;
              }
            } else if (babelTypes.isArrowFunctionExpression(parent)) {
              parent.body = express;
            } else if (babelTypes.isSwitchCase(parent)) {
              parent.test = express;
            }
            pathNode.skip();
          }
        }
      },
    },
    JSXText: {
      enter(pathNode) {
        const { node, parent } = pathNode;
        const { value } = node;
        if (reg.test(value)) {
          hasUnTrans = true;
          const express = createOneI18NExpress(value);
          const newNode = babelTypes.jsxExpressionContainer(express);
          for (let i = 0; i < parent.children.length; i++) {
            if (parent.children[i] === node) {
              parent.children[i] = newNode;
              break;
            }
          }
          pathNode.skip();
        }
      },
    },
    // TemplateLiteral: {
    // //模板字符串
    //   enter(pathNode) {
    //     // 模板字符串
    //     const { node, parent } = pathNode;
    //     console.log(node);
    //     console.log(pathNode);
    //   },
    // },
    TemplateElement: {
      enter(pathNode) {
        const { node, parent } = pathNode;
        const { value } = node;
        if (reg.test(value.raw) && !pathNode.findParent((p) => p.isTaggedTemplateExpression())) {
          // 过滤了styled-components的场景
          hasUnTrans = true;
          const express = createOneI18NExpress(value.raw);
          for (let i = 0; i < parent.quasis.length; i++) {
            if (parent.quasis[i] === node) {
              parent.quasis[i] = babelTypes.templateElement({ raw: '' });
              parent.quasis.splice(i, 0, babelTypes.templateElement({ raw: '' }));
              parent.expressions.splice(i, 0, express);
              break;
            }
          }
          pathNode.skip();
        }
      },
    },
  });
  const { code } = generator(ast, {
    retainLines: true,
    retainFunctionParens: true,
    jsescOption: { minimal: true, quotes: 'single' },
  });
  if (hasUnTrans) {
    writeFile(filepath, code);
    //prettier -w --config ../../.prettierrc ../../web-src/js/components/page/buy/BuyStep1.react.jsx
    //用prettier 对文件进行格式化
    exec(`prettier -w --config ../.prettierrc.js ${filepath}`, (error, stdout, stderr) => {
      if (error) {
        console.error(error);
      } else {
        console.log('prettier success ', filepath);
      }
    });
  } else {
    // console.warn('done, all clear ~!');
  }
}

function work(filePath) {
  const stats = fs.statSync(filePath);
  const isFile = stats.isFile(); // 是文件
  const isDir = stats.isDirectory(); // 是文件夹
  if (isDir) {
    fs.readdirSync(filePath).forEach((fileName) => {
      if (
        !fileName.startsWith('.umi') &&
        !fileName.startsWith('iconfont') &&
        !fileName.startsWith('services')
      ) {
        // 遍历读取到的文件列表
        // 获取当前文件的绝对路径
        const file = path.join(filePath, fileName);
        // 根据文件路径获取文件信息，返回一个fs.Stats对象
        work(file);
      }
    });
  } else if (isFile && workWithFile(filePath)) {
    transform(filePath);
  }
}
dirs.forEach((dir) => {
  const _path = path.resolve(__dirname, dir);
  work(_path);
});

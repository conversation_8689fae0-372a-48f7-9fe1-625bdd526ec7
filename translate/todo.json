{"全部收起": "", "全部展开": "", "将所选达人分配给下属员工": "", "（员工可在“分配给我的”达人中查看）": "", "将所选达人取消分配": "", "（达人将回归至公海）": "", "您希望对这些{{label}}：": "", "您希望对前{{total}}个{{label}}：": "", "当前条件下，共检索出{{total}}个{{label}}": "", "全量操作一次最多操作{{limit}}个{{label}}": "", "一次最多导出{{total}}个{{label}}": "", "请确认是否继续导出前{{total}}个{{label}}": "", "对筛选出的{{label}}的信息进行更新": "", "系统会对所选的{{total}}个{{label}}进行信息更新：": "", "1.会更新{{label}}的头像、粉丝数、视频数、视频点赞数": "", "2.如果{{label}}正在直播，会更新该场直播的观看人数、流水": "", "确定屏蔽搜索出的 {{count}} 个{{label}}吗？": "", "刷榜大哥": "", "指定关注数": "", "不限关注数": "", "最小关注数": "", "最大关注数": "", "手机账号关注": "", "手机账号视频分享": "", "检测手机账号可用性": "", "全部账号": "", "依据时区关键词检索": "", "普通用户": "", "视频达人": "", "直播达人": "", "尚未创建": "", "标签可用于对": "", "进行标记、分类、筛选等用途": "", "标签中的数字代表关联的": "", "数量": "", "没有找到 “{{bizCode}}” 对应的流程": "", "流程 “{{name}}” 不需要额外指定输入变量": "", "执行流程": "", "执行 KOL 内置的一些流程": "", "选择流程": "", "现阶段为您提供以下自动化流程": "", "浏览器自动化流程": "", "直播间加热": "", "通过若干浏览器账号对直播间进行加热": "", "视频加热": "", "通过若干浏览器账号对指定的视频进行加热": "", "手机 App 自动化流程": "", "通过若干手机账号对直播间进行加热": "", "通过若干手机账号对指定的视频进行加热": "", "选择执行体": "", "请选择您要执行流程的分身": "", "请选择您要执行流程的手机账号": "", "输入变量": "", "任务调度已传递给手机": "", "任务调度已传递给本地客户端": "", "任务准确就绪": "", "任务准确就绪，点击\"完成“将为您执行任务": "", "任务池可容纳的最大任务数量": "", "任务池当前已有任务": "", "本次操作拟创建任务": "", "实际创建任务": "", "任务池": "", "正在为您创建“{{type}}”任务": "", "已为您创建“{{type}}”任务": "", "关闭（": "", "确定要删除此代金券吗？": "", "贴子数": "", "最近同步时间": "", "打开Instagram账号": "", "复制用户名称": "", "Instagram账号基本信息": "", "打开此达人的Instagram首页": "", "达人名称": "", "帖子数量": "", "达人范围": "", "公海的达人": "", "团队关注的达人": "", "已建联": "", "暂无数据，请确认管理员是否有给您分配达人": "", "唤醒Instagram": "", "Instagram唤醒方式：": "", "Instagram协议唤醒": "", "直接唤醒Instagram": "", "唤醒Instagram时自动将达人设置为已触达": "", "TikTok唤醒页面": "", "达人首页": "", "达人直播间": "", "（取前{{count}}个）": "", "分配给我的": "", "当前条件可查询到 {{count}} 个达人": "", "取筛选过的前 {{count}} 个": "", "共{{count}}个筛选条件": "", "普通员工可以创建”我的自动计划“，但创建的自动计划只能针对“分配给我的达人”": "", "系统自动计划只能由超管或BOSS创建，可针对全部的公海达人，以及全部的经纪人账号与手机账号；普通员工可以创建自己的自动计划": "", "建联手段": "", "养号目标": "", "账号权重": "", "单账号养号时长": "", "评论话术": "", "自定义话术": "", "AI生成": "", "观看视频数量": "", "分享视频位置": "", "第{{index}}个": "", "守候手段": "", "更新手段": "", "待检测账号": "", "{{total}} 个": "", "重复周期": "", "创建新的自动计划": "", "官方{{addOn}}账号": "", "经纪人{{addOn}}账号": "", "普通{{addOn}}账号": "", "手机视频分享": "", "修改{{who}}自动计划": "", "我的": "", "创建{{who}}自动计划": "", "手机私信账号": "", "您可以针对不同的手机账号使用不同的邀约话术": "", "您可以针对不同的经纪人账号使用不同的邀约话术（请注意，仅针对打有标签“{{tag}}”的浏览器分身才有作用）": "", "指定话术分组  ：": "", "请指定分组": "", "每台手机账号数量": "", "手机数量": "", "您只需输入{{label}}和所选日期，系统会根据左边的配置参数，自动计算出来一天最多可发送的私信数量": "", "全天候": "", "私信发送过快时的标签": "", "账号被封控时的标签": "", "账号被封禁时的标签": "", "账号未登录时的标签": "", "需要调整隐私设置时的标签": "", "只有在蓝色方块的时间内才允许执行邀约建联相关流程": "", "如果有多个经纪人账号可以发送私信，最多同时允许多少个经纪人账号同时发送，建议设置为1，避免出现私信过快等情况，如果设置为0则不限制": "", "每批最多可以发送多少私信，发送完后需要间隔一定时间才允许继续发送": "", "发送完一批私信后，需要间隔多长时间才允许下一批次的发送": "", "每一个经纪人账号每天最多可以发送多少私信": "", "每次执行经纪人账号后台私信的邀约建联流程时，会从含有此标签的浏览器分身中选取": "", "当发送私信过快时，会给浏览器分身打上此标签，可自行解除此标签": "", "当经纪人账号被封控时，会给相应的浏览器分身打上此标签，可自行解除此标签": "", "当经纪人账号被封禁时，会给相应的浏览器分身打上此标签，可自行解除此标签": "", "如果经纪人账号未登录，会给相应的浏览器分身打上此标签，需要您自行登录": "", "每一个手机账号每天最多可以发送多少私信": "", "每次执行手机号相关流程时，如发送私信、分享视频、账号关注等，会从含有此标签的手机账号中选取 ": "", "当发送私信过快时，会给手机账号打上此标签，可自行解除此标签，或通过“检测账号可用性”流程自动解除": "", "当账号被封控时，会给手机账号打上此标签，可自行解除此标签，或通过“检测账号可用性”流程自动解除": "", "当账号被封禁时，会给手机账号打上此标签，可自行解除此标签，或通过“检测账号可用性”流程自动解除": "", "如果TikTok未登录，会给手机账号打上此标签，需要您自行登录": "", "当账号未设置成“允许所有人都可以私信我”时，会给手机账号打上此标签，需要您自行设置手机账号的隐私设置": "", "“手机账号私信”流程与“手机账号视频分享”流程均会参考下述设置，": "", "私信普通账号_浏览器": "", "私信官方账号_浏览器": "", "私信经纪人账号_浏览器": "", "私信普通账号_手机": "", "最近开播时间": "", "请设置最近开播时间": "", "最近开播时间晚于上述时间的主播才允许抓取到您的团队，最小值为1天，最大值为30天，请注意，最近天数要求的越小，抓取的主播数量越少": "", "请同步更换抓取主播ID流程所使用的浏览器分身的节点IP地址": "", "请同步更换用来筛选主播达人是否能够被邀约的经纪人账号": "", "请同步更换用来抓取主播ID流程所使用的TikTok账号": "", "请注意，一个月只能更改{{count}}次": "", "使用经纪人后台的话术模板": "", "AI生成评论": "", "从视频的评论内容中抓取10条评论，喂给 ChatGPT 生成相应评论，如果评论内容少于10条，则不生成任何评论": "", "使用在花漾中设置的话术模板": "", "优先使用经纪人账号设置的话术，否则使用下述话术：": "", "优先使用手机账号设置的话术，否则使用下述话术：": "", "话术分组": "", "请选择分组": "", "可选话术": "", "请选择话术": "", "在发送私信时": "", "通过经纪人账号发送私信时": "", "在发送手机私信时": "", "通过普通账号发送私信时": "", "通过官方账号发送私信时": "", "{{prefix}}，通过对达人进行浏览、点赞、收藏等行为，降低封号风险：": "", "除了关注达人以外，您还可以决定是否进行以下辅助行为：": "", "请注意，只有关注达人后，才能够分享视频给他/她：": "", "取值范围1 - 6": "", "首页的第几个视频：": "", "达人关注概率": "", "每视频观看时长": "", "{{count}}秒": "", "请指定观看时长": "", "视频点赞概率": "", "视频收藏概率": "", "视频评论概率": "", "评论辅助表情": "", "在内容中添加一些辅助表情（使内容产生差异化）的概率": "", "视频评论内容": "", "指定话术分组": "", "从视频的评论内容中抓取10条评论，喂给ChatGPT生成相应评论，如果评论内容少于10条，则不生成任何评论": "", "原则上，官方账号有且只有一个，您需要为相应的浏览器分身打上标签“{{tag}}”": "", "使用的账号": "", "使用与该达人最近沟通用到的账号": "", "如果从未有账号联系过或者最近一次用到的账号包含有标签{{tags}}，则分配新的账号": "", "强制分配新的账号": "", "强制指定账号": "", "如果强制使用某个账号，请不要选择过多的达人，否则，受到私信发送频率的限制，会导致发送私信的间隔过长": "", "分配原则": "", "带有标签“{{tag}}”的账号": "", "请注意：会忽略带有标签{{tags}}的账号": "", "带有标签“{{tag}}”和以下标签的账号": "", "如果该达人已经与某个账号联系过，则使用联系过的账号，如果该达人从未联系过，则使用下述原则：": "", "从带有标签“{{tag}}”的账号中随机选择": "", "强制使用指定账号：": "", "经纪人账号分配原则": "", "官方账号分配原则": "", "普通账号分配原则": "", "关注行为": "", "分享内容": "", "辅助行为": "", "Instagram达人邀约话术": "", "TikTok评论话术": "", "话术分组名称": "", "请输入话术分组名称": "", "确定要删除此话术分组吗？": "", "删除话术分组会同步删除此分组下的所有话术": "", "新分组": "", "创建新的话术分组": "", "新话术": "", "达人信息更新": "", "每分身每次更新达人数": "", "KOL私域管理系统": "", "系统自动计划": "", "Instagram配置": "", "Instagram达人": "", "无法打开未设置IP隔离的浏览器分身": "", "请在花漾客户端内完成对浏览器分身的IP隔离设置": "", "手机私信数量": "", "浏览器私信数量": "", "您也无需担心同一时间同时执行若干个不同类型的流程任务而产生的干扰问题，此时会由任务池根据不同流程任务的优先级自行调度。": "", "更进一步信息，请您阅读 {{link}} 一文": "", "任务池与历史任务": "", "会更新该达人的头像、粉丝数、关注数、帖子数": "", "自动计划最多同时处理 10000 个达人": "", "最近开播": "", "最近3天开过播": "", "最近7天开过播": "", "最近30天开过播": "", "手机账号私信": "", "通过手机中的 {{name}} App 向达人发送私信": "", "经纪人账号后台私信": "", "通过经纪人账号登录公会后台向达人发送邀约请求": "", "官方账号浏览器私信": "", "官方账号是指公会授权的 TikTok 账号，代表公会品牌标识": "", "普通账号浏览器私信": "", "用浏览器发送私信（不推荐）": "", "通过手机中的 {{name}} App 关注达人并打招呼": "", "将您的视频分享给达人，以引起达人的注意": "", "与所选达人建立联系，引起达人的注意与回复": "", "与所选达人邀约建联": "", "建联方式": "", "请选择适合的建联方式": "", "流程任务已创建": "", "您可到任务池中查看详细信息": "", "拟对 {{count}} 个达人创建“邀约建联”的流程任务": "", "点击“完成”按钮后，系统会在 {{link}} 中创建 {{count}} 个流程任务，这些任务会按照流程的优先级、在允许执行的时间段内分批调度、依次执行": "", "消息发送成功将这批达人的状态更改为": "", "将这批达人的状态更改为": "", "为这批达人打上标签，方便日后查找": "", "请输入标签": "", "关键词列表": "", "请输入多个和您希望养成类别相关的标签，如您希望养成和宠物相关的账号，则请输入：cute cats,cats,dog,pet 等多个关键词": "", "请指定关键词": "", "多个关键词以逗号分割": "", "养号流程已触发": "", "可在TikTok账号属性开启自动养号计划": "", "TikTok账号属性": "", "账号标签：": "", "让 TK 平台给指定的手机账号打上特定标签": "", "多个关键词以英文逗号分割": "", "账号权重：": "", "提升账号权重，尽可能避免账号封控的风险": "", "未找到可用的养号流程": "", "账号名称：": "", "最近联系的手机账号": "", "分配我的刷榜大哥": "", "修改私信设置": "", "主播私信设置已修改": "", "针对当前搜索条件下的所有刷榜大哥进行操作": "", "最近联系的经纪人账号": "", "最近更新时间：": "", "主播私信已修改": "", "账号清理流程已触发": "", "手机 TikTok 账号清理": "", "当手机 TikTok 账号关注过多达人时会影响邀约效率，此时可对手机账号进行清理": "", "取消所有非粉丝达人的关注": "", "取消所有粉丝的关注": "", "清空所有的聊天记录": "", "未找到可用的清理流程": "", "TikTok 账号标签有着具体的含义，可在 {{link}} 中进行定义": "", "系统设置": "", "当开启自动养号后，会在指定的时间内自动执行养号流程": "", "对 TikTok 账号进行批量设置，会影响所选账号的具体设置": "", "当开启自动养号后，在手机允许的养号时间段内，会自动执行养号流程": "", "自动养号": "", "您需要根据账号的权重，自行调整下述参数": "", "每日最多私信条数": "", "每一个手机账号每天最多可以发送多少私信（包括手机视频分享）": "", "{{count}}条": "", "每批次最多私信条数": "", "每批最多可以发送多少私信（包括手机视频分享），发送完后需要间隔一定时间才允许继续发送，间隔时间在“手机属性-->工作间隔”中指定，请注意，间隔时间是以手机为单位的，换言之，如果a账号和b账号位于同一部手机，当a账号发送完一批私信后，即便需要b账号发送私信，也需要等待指定的时间间隔后才允许发送": "", "可为 TikTok 账号指定不同的话术": "", "邀约话术分组": "", "TikTok 账号设置": "", "TikTok 账号批量设置": "", "养号设置": "", "发送私信设置": "", "分享名片设置": "", "每日最多分享条数": "", "每一个手机账号每天最多可以分享多少达人名片": "", "每批次最多分享条数": "", "每批最多可以分享多少名片，分享完后需要间隔一定时间才允许继续分享，间隔时间在“手机属性-->工作间隔”中指定，请注意，间隔时间是以手机为单位的，换言之，如果a账号和b账号位于同一部手机，当a账号分享完一批名片后，即便需要b账号分享名片，也需要等待指定的间隔时间后才允许继续分享": "", "对手机进行批量设置，会影响所选手机的具体设置": "", "可用性检查": "", "检查时间": "", "请设置检查时间": "", "每 {{min}} 分钟": "", "不检查": "", "机器人会给指定的目标达人发送私信，如果发送成功则清理相关标签，反之则打上相关标签": "", "当前Tiktok账号的第一个好友": "", "在当前团队公会主播中随机找一个主播": "", "指定达人": "", "请指定目标达人": "", "手机ID": "", "请输入手机名称": "", "微信绑定状态": "", "（只有在指定时间范围内才会检查是否有新消息）": "", "请选择接收人": "", "工作间隔": "", "手机相关设置": "", "手机批量设置": "", "设置手机执行对应流程的间隔": "", "发送私信每批次间隔": "", "请输入发送私信间隔": "", "{{count}}分钟": "", "请输入分享卡片间隔": "", "分享名片每批次间隔": "", "分享完一批名片后，需要间隔多长时间才允许下一批次的名片分享": "", "工作时间：": "", "允许手机发送私信、手机账号关注、手机视频分享、手机名片分享等流程的执行时间": "", "养号时间：": "", "执行手机养号流程的时间（需要开启自动养号）": "", "空闲时间：": "", "在此时间内不执行上述自动化流程，一般设置为员工工作时间段": "", "账号可用性检查流程已触发": "", "机器人会给指定的目标达人发送私信，如果发送成功则清理相关标签，反之则打上相关标签；如果在此处指定目标达人，要求该手机中登录的所有TikTok号都要和该达人互为好友；如果留空，机器人会寻找一个好友发送私信": "", "（最小10分钟，最大20000分钟）": "", "确定删除选中的 {{total}} 个账号吗？": "", "手机账号删除后，可通过同步手机账号流程予以恢复": "", "手机名称: {{mobileName}}": "", "手机属性": "", "手机微信通知": "", "每日最多{{count}}": "", "正在执行的流程": "", "账号清理": "", "分配给我的在线主播": "", "运行“直播广场主播在线守候流程“": "", "最多1000": "", "忽略当前团队已有的主播": "", "运行“团队关注主播在线守候流程“": "", "运行时长": "", "给主播打上标签": "", "抓取到的正在开播的主播会自动进入到团队的公海达人，给这些主播打上标签方便您事后筛选": "", "只有超管与BOSS才允许执行此操作": "", "针对当前搜索条件下的所有用户进行操作": "", "正在执行其它流程，这会导致检测账号可用性流程执行失败，请确保在该手机中结束其它所有流程后再重新执行＂": "", "正在刷新审批结果": "", "查看申请的审批结果": "", "未知错误": ""}
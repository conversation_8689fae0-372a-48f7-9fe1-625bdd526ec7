export default {
  '您的网络发生异常，无法连接服务器':
    'An exception occurred on your network and cannot connect to the server. ',
  您在当前团队中被禁用: 'You are disabled in the current team',
  您已从当前团队中被踢出: 'You have been kicked out of your current team',
  '请联络当前团队管理员，以获取进一步的信息':
    'Please contact your current team administrator for further information',
  请求失败: 'Request failed',
  展开: 'Expand',
  收起: 'Collapse ',
  '您希望对这些达人：': 'You want to talk to these people:',
  您希望对前: 'Do you want to go forward',
  '个达人：': 'creators:',
  '当前条件下，共检索出': 'Under current conditions, a total of retrieved',
  '笔达人，': 'creators,',
  分配成功: 'Assignment success',
  一次最多导出: 'Export at most once',
  达人: 'Creator',
  请确认是否继续导出前: 'Please confirm whether to continue exporting',
  名达人: 'creators',
  对所选达人的信息进行更新: 'Update information on selected creator',
  系统会对所选的: 'The system will check the selected',
  '个达人进行信息更新：': 'Individuals update information:',
  '1.会更新该达人的头像、粉丝数、视频数、视频点赞数':
    "1. The creator's browser, number of fans, number of videos, and number of video likes will be updated",
  '2.如果该达人正在直播，会更新该场直播的观看人数、流水':
    '2. If the creator is in live, the number of viewers and coins will be updated',
  '已为您创建了一笔“信息更新”的任务': 'An "information update" task has been created for you',
  确定屏蔽搜索出的: 'Confirm blocking and searching',
  '个达人吗？': ' creators?',
  达人屏蔽成功: 'Creator blocked successfully',
  邀约建联: 'Invite',
  分配达人: 'Assign',
  信息更新: 'Update',
  批量导出: 'Export',
  批量屏蔽: 'Block',
  无权限执行此类操作: 'Do not have permission to perform such operations',
  '您当前的角色身份没有权限执行此类操作，请联络团队管理员以获取进一步的信息':
    'Your current role identity does not have permission to perform such operations. Please contact your team administrator for further information',
  清除分身已有的用户与组织单元授权关系:
    'Clear the existing authorization relationship between users and organizational units',
  经理只支持对自己所属组织单元内的员工进行授权:
    'Managers only support the authorization of employees within their organizational unit',
  '清除已有的用户与组织单元授权关系？':
    'Clear the existing authorization relationship between users and organizational units?',
  '将清除旧有的授权关系，请确认是否要继续':
    'The old authorization relationship will be cleared. Please confirm whether you want to continue',
  确定: 'Confirm',
  取消: 'Cancel',
  经理角色不支持对组织单元授权:
    'Manager roles do not support authorization of organizational units',
  '已选：': 'Selected:',
  个成员: 'Member',
  个部门: 'Departments',
  选择成员对象: 'Select member objects',
  用户授权: 'User authorization',
  组织单元授权: 'Organizational unit authorization',
  '无权限查看{{model}}': 'View {{model}} without permission',
  团队成员: 'Staff',
  秒后重新获取: 'Re-acquire in seconds',
  获取验证码: 'Get the Captcha',
  验证码已成功发送至: 'Captcha was successfully sent to',
  手机号已存在: 'Mobile phone number already exists',
  手机号已经存在: 'Mobile phone number already exists',
  '请确认您已经输入了正确的手机号，如果确认手机号无误，您可以选择找回密码':
    'Please confirm that you have entered the correct mobile phone number. If you confirm that the mobile phone number is correct, you can choose to retrieve the password.',
  找回密码: 'Reset password',
  邮箱已存在: 'Mail already exists',
  邮箱已经存在: 'The mailbox already exists',
  '请确认您已经输入了正确的邮箱，如果确认邮箱无误，您可以选择找回密码':
    'Please confirm that you have entered the correct email. If the email is correct, you can choose to retrieve your password.',
  请输入图形验证码: 'Please enter the Captcha',
  图形验证码: 'Captcha',
  请输入验证码: 'Please enter captcha',
  未标记任何标签: 'No tags',
  标签: 'Tag',
  用户: 'User',
  '正在为您批量取消关注，请稍候...':
    'Batch cancellations are being canceled for you, please wait...',
  取消关注成功: 'Unfavorite successfully',
  '正在为您批量关注，请稍候...': 'Unfavorite in batches, please wait...',
  关注成功: 'Favorite success',
  '批量关注/取消关注': 'Favorite/Unfavorite',
  '批量关注所选的{{label}}': 'Favorite the selected {{label}} in batches',
  '批量取消关注所选的{{label}}': 'Batch cancel favorite selected {{label}}',
  已复制到剪切板: 'Copied to clipboard',
  已复制到剪切板_空格前缀: ' Copied to clipboard',
  知道了: 'OK',
  关闭: 'Close',
  关闭_未开启: 'Off',
  暂无数据: 'No data',
  邮箱验证码已成功发送至: 'Email code has been successfully sent to',
  请输入邮箱验证码: 'Please enter your email code',
  '请确认您的输入是否正确。如有疑问请联系客服':
    'Please confirm that your input is correct. If you have any questions, please contact customer service',
  重新加载: 'Reload',
  出错了: 'Wrong',
  回到首页: 'Return to home page',
  共: 'Total ',
  项: '',
  基本信息: 'Basic information',
  浏览器参数: 'Browser parameters',
  硬件指纹: 'Hardware fingerprint',
  媒体设备指纹: 'Media device fingerprints',
  插件指纹: 'Plug-in fingerprint',
  端口防护: 'Port protection',
  WebRTC指纹: 'WebRTC fingerprint',
  保存: 'Save',
  浏览器指纹特征码: 'Browser fingerprint signature',
  '浏览器指纹特征码是指针对全部指纹信息进行消息摘要算法计算出来的唯一编码，指纹中任何一个属性的变化都会影响到特征码，可通过特征码是否重复判断指纹的唯一性':
    'The browser fingerprint signature refers to the unique code calculated by the message summary algorithm for all fingerprint information. Changes in any attribute in the fingerprint will affect the signature code. The uniqueness of the fingerprint can be determined by whether the signature code is repeated',
  重新生成指纹: 'Regenerate fingerprints',
  编辑: 'Edit',
  查看浏览器指纹: 'Check browser fingerprints',
  'huayoung.tk.creator.OpenPlan带货达人智能整理':
    'Huayyoung.tk.creator.OpenPlan intelligent sorting',
  'huayoung.tk.tools.未付款订单定时催付': 'Huayoung.tk.tools. Regular reminder for unpaid orders',
  刷榜大哥不存在: 'The gifter does not exist',
  该刷榜大哥已被删除: 'The gifter has been deleted',
  达人不存在: 'Creator not exist',
  该达人已被删除: 'This creator has been deleted',
  请选择归属地: 'Please select your home',
  选择接收人: 'Selected the recipients',
  '请确保收件人绑定了手机/邮箱/微信，系统并不做强制性检查':
    'Please ensure that the recipient is bound to his mobile phone/email/WeChat and the system does not perform mandatory checks',
  根据用户名称进行检索: 'Search by name',
  手机: 'Mobile phone',
  微信: 'WeChat',
  邮箱: 'Email',
  该手机账号已删除或不存在: 'This mobile phone has been deleted or does not exist',
  '指定的手机账号已删除或不存在，无法访问':
    'The specified mobile phone has been deleted or does not exist',
  您无权访问该手机账号: 'You do not have permission to access this mobile account',
  请和团队管理员联系: 'Please contact your team administrator',
  您尚未开通此模块: 'You have not activated this module yet',
  请您联系在线客服开通使用: 'Please contact online customer service to activate',
  无数据: 'No data',
  我已阅读和同意: 'I have read and agree',
  '用户协议》': 'User Agreement》',
  账户余额扣减: 'Account balance deduction',
  '从账户余额（可用￥': 'From account balance (Available ¥',
  '）中扣减': ') deduction',
  元: 'Yuan',
  我们的工作人员正在确认银行转账信息: 'Our staff is confirming the bank transfer information',
  请在付款后将: 'Please send the',
  付款凭证截图: 'Screenshot of payment voucher',
  和: 'And',
  订单编号: 'Order number',
  '）提供给': ') Provided to',
  在线客服: 'Online customer service',
  '，加速审核流程': ', accelerate the review process',
  等待您向我们的银行账号付款: 'Waiting for you to pay our bank account',
  户名: 'Account name',
  账号: 'Account',
  开户行: 'Opening bank',
  转账备注: 'Transfer Notes',
  '订单编号：': 'Order Number:',
  '1月': '1 month',
  '2月': '2 months',
  '3月': '3 months',
  半年: 'Half',
  一年: 'A year',
  续费时长: 'The purchased usage period',
  支付宝: 'Alipay',
  微信支付: 'Weixin Pay',
  订单: 'Orders',
  请联系: 'Please contact',
  '，并提供': ', and provide',
  '请支付：': 'Please pay:',
  扫码付款: 'Scan code payment',
  打开: 'Open',
  扫一扫: 'Scan the',
  已完成转账: 'Transfer completed',
  稍后支付: 'Pay later',
  支付: 'Payment',
  现金支付方式: 'Cash payment method',
  '恭喜，支付成功': 'Congratulations, the payment was successful',
  订单类型: 'Order type',
  应付金额: 'Amount payable',
  实付金额: 'Actual payment amount',
  订单状态: 'Order status',
  '实付金额：': 'Actual amount paid:',
  '支付方式：': 'Payment method:',
  '3天': '3 days',
  '60分钟': '60 minutes',
  订单已生成: 'Order generated',
  '您的订单已生成，请': 'Your order has been generated, please',
  '在内完成支付，逾期订单将取消':
    'Payment will be completed within, and overdue orders will be cancelled',
  '关于《花漾指纹浏览器用户协议》的提示':
    'Tips about the "Huayang Fingerprint Browser User Agreement"',
  '您需要充分理解，云上悦动提供的产品或服务（含IP资源）受国家法律法规的严格监管，因此，您在使用云上悦动提供的产品或服务（含IP资源）时，严禁用于任何涉黄、涉赌或者其它国家法律法规所禁止的行为，一旦发觉有上述行为，云上悦动将有权立即停止相关服务，包括且不限于收回IP资源、封禁用户账户、冻结团队账户余额等':
    'You need to fully understand that the products or services (including IP resources) provided by Yunshang Yueyong are strictly regulated by national laws and regulations. Therefore, when you use the products or services (including IP resources) provided by Yunshang Yueyong, it is strictly prohibited to use them for any pornography, gambling or prohibited by other national laws and regulations. Once you discover the above behaviors, Yunshang Yueyong will have the right to immediately stop relevant services, including but not limited to withdrawing IP resources, blocking user accounts, freezing team account balances, etc.',
  请先同意: 'Please agree first',
  用户协议: 'User Agreement',
  '确定要选择银行对公转账方式支付订单款项吗？':
    'Are you sure you want to choose a bank-to-corporate transfer method to pay the order?',
  '如果选择银行对公转账方式支付，请在提交订单后48小时内，向我们提供的银行账号中转入':
    'If you choose bank-to-corporate transfer method for payment, please transfer it to the bank account provided by us within 48 hours after submitting the order.',
  '元，银行账号信息将在您点击“确认”按钮后展示':
    'Yuan, bank account information will be displayed after you click the "Confirm" button',
  请确认您已完成银行转账: 'Please confirm that you have completed the bank transfer',
  '当您确认已完成银行转账后，付款流程将流转到我们的后台工作人员，一旦他们确认款项已到账，您的订单将自动完成':
    'After you confirm that the bank transfer has been completed, the payment process will be transferred to our back office staff. Once they confirm that the money has been received, your order will be automatically completed',
  确认已付款: 'Confirm payment',
  立即支付: 'Paid immediately',
  '正在校验优惠码...': 'Checking coupon code...',
  帮您节省: 'Help you save',
  '无效的优惠码，请检查后重新输入': 'Invalid coupon code, please check and re-enter',
  '如果您有优惠码可以填入，将以更优惠价格购买TK套餐':
    'If you have a coupon code to fill in, you will buy the TK package at a better price',
  优惠码: 'Coupon code',
  优秀: 'Excellent',
  一般: 'General',
  较差: 'Poor',
  未知: 'Unknown',
  不可用: 'Unavailable',
  '测速...': 'Speed measurement...',
  全部标签: 'All tags',
  全部用户: 'All users',
  全部品类: 'All categories',
  全部平台: 'All platforms',
  全部归属地: 'All locations',
  全部IP池: 'All IP pools',
  全部站点: 'All sites',
  全部来源: 'All source',
  全部交互店铺: 'All stores',
  全部交互轨迹: 'All trajectories',
  全部流程: 'All scripts',
  不限粉丝分布: 'Unlimited fan distribution',
  不限设备: 'Any device',
  了解更多: 'Learn more',
  资源: 'Resources',
  该: 'The',
  不存在: 'Not exist',
  无法访问: 'Cannot access',
  '不存在，可能已经被删除，无法查看其详情信息':
    'It does not exist. It may have been deleted. Its details cannot be viewed.',
  '无法访问，无法查看其详情信息': 'Unable to access, its details cannot be viewed',
  浏览: 'Browse',
  选择: 'Select',
  个平台: 'Platforms',
  阿富汗: 'Afghanistan',
  阿拉斯加: 'Alaska',
  阿尔巴尼亚: 'Albania',
  阿尔及利亚: 'Algeria',
  美属萨摩亚: 'American Samoa',
  安道尔: 'Andorra',
  安哥拉: 'Angola',
  安圭拉: 'Anguilla',
  安提瓜和巴布达: 'Antigua and barbuda',
  阿根廷: 'Argentina',
  亚美尼亚: 'Armenia',
  阿鲁巴: 'Aruba',
  阿森松: 'On Ascension',
  澳大利亚: 'Australia',
  奥地利: 'Austria',
  阿塞拜疆: 'Azerbaijan',
  巴哈马: 'Bahamas',
  巴林: 'Bahrain',
  孟加拉国: 'Bangladesh',
  巴巴多斯: 'Barbados',
  白俄罗斯: 'Belarus',
  比利时: 'Belgium',
  伯利兹: 'Belize',
  贝宁: 'Benin',
  百慕大群岛: 'Bermuda',
  不丹: 'Bhutan',
  玻利维亚: 'Bolivia',
  波斯尼亚和黑塞哥维那: 'Bosnia and Herzegovina',
  博茨瓦纳: 'Botswana',
  巴西: 'Brazil',
  文莱: 'Brunei',
  保加利亚: 'Bulgaria',
  布基纳法索: 'Burkina Faso',
  布隆迪: 'Burundi',
  柬埔寨: 'Cambodia',
  喀麦隆: 'Cameroon',
  加拿大: 'Canada',
  加那利群岛: 'Canary Islands',
  开普: 'Cape',
  开曼群岛: 'Cayman Islands',
  中非共和国: 'Central African Republic',
  乍得: 'Chad',
  中国: 'China',
  智利: 'Chile',
  圣诞岛: 'Christmas Island',
  科科斯岛: 'Cocos Island',
  哥伦比亚: 'Colombia',
  多米尼加共和国: 'Dominican Republic',
  科摩罗: 'Comoros',
  刚果共和国: 'Republic of the Congo',
  库克群岛: 'Island',
  哥斯达黎加: 'Costa Rica',
  克罗地亚: 'Croatia',
  古巴: 'Cuba',
  库拉索: 'Curacao',
  塞浦路斯: 'Cyprus',
  捷克: 'Czech',
  丹麦: 'Denmark',
  吉布提: 'Djibouti',
  多米尼加: 'Dominican',
  厄瓜多尔: 'Ecuador',
  埃及: 'Egypt',
  萨尔瓦多: 'El Salvador',
  赤道几内亚: 'Equatorial Guinea',
  厄立特里亚: 'Eritrea',
  爱沙尼亚: 'Estonia',
  埃塞俄比亚: 'Ethiopia',
  福克兰群岛: 'Falkland Islands',
  法罗群岛: 'Faroe Islands',
  斐济: 'Fiji',
  芬兰: 'Finland',
  法国: 'France',
  法属圭亚那: 'Guyane française',
  法属波利尼西亚: 'French Polynesia',
  加蓬: 'Gabon',
  冈比亚: 'Gambia',
  格鲁吉亚: 'Georgia',
  德国: 'Germany',
  加纳: 'Ghana',
  直布罗陀: 'Of Gibraltar',
  希腊: 'Greece',
  格陵兰岛: 'Greenland',
  格林纳达: 'Grenada',
  瓜德罗普岛: 'Guadeloupe',
  关岛: 'Guam',
  瓜地马拉: 'In Guatemala',
  几内亚: 'Guinea',
  几内亚比绍共和国: 'Guinea-Bissau',
  圭亚那: 'Guyana',
  海地: 'Haiti',
  夏威夷: 'Hawaii',
  洪都拉斯: 'Honduras',
  中国香港: 'China Hong Kong',
  匈牙利: 'Hungary',
  冰岛: 'Iceland',
  印度: 'India',
  印度尼西亚: 'Indonesia',
  伊朗: 'Iran',
  伊拉克: 'Iraq',
  爱尔兰: 'Ireland',
  以色列: 'Israel',
  意大利: 'Italy',
  象牙海岸: 'Ivory Coast',
  牙买加: 'Jamaica',
  日本: 'Japan',
  约旦: 'Jordan',
  哈萨克斯坦: 'Kazakhstan',
  肯尼亚: 'Kenya',
  基里巴斯: 'Kiribati',
  朝鲜: 'North Korea',
  韩国: 'South Korea',
  科威特: 'Kuwait',
  吉尔吉斯斯坦: 'Kyrgyzstan',
  老挝: 'Lao',
  拉脱维亚: 'Latvia',
  黎巴嫩: 'Lebanon',
  莱索托: 'Lesotho',
  利比里亚: 'Liberia',
  利比亚: 'Libya',
  列支敦士登: 'Liechtenstein',
  立陶宛: 'Lithuania',
  卢森堡: 'Luxembourg',
  中国澳门: 'China Macao',
  马其顿: 'Macedonia',
  马达加斯加: 'Madagascar',
  马拉维: 'Malawi',
  马来西亚: 'Malaysia',
  马尔代夫: 'Maldives',
  马里: 'Mali',
  马耳他: 'Malta',
  马绍尔群岛: 'Marshall Islands',
  马提尼克: 'Martinique',
  毛里塔尼亚: 'Mauritania',
  毛里求斯: 'Mauritius',
  马约特: 'Mayotte',
  墨西哥: 'Mexico',
  密克罗尼西亚: 'Micronesia',
  摩尔多瓦: 'Moldova',
  摩纳哥: 'Monaco',
  蒙古: 'Mongolia',
  黑山: 'Montenegro',
  蒙特塞拉特岛: 'Montserrat',
  摩洛哥: 'Morocco',
  莫桑比克: 'Mozambique',
  缅甸: 'Myanmar',
  纳米比亚: 'Namibia',
  拿鲁岛: 'Nalu Island',
  尼泊尔: 'Nepal',
  荷兰: 'Netherlands',
  新喀里多尼亚: 'New Caledonia',
  新西兰: 'New Zealand',
  尼加拉瓜: 'Nicaragua',
  尼日尔: 'Niger',
  尼日利亚: 'Nigeria',
  '纽埃岛(新)': 'Niue (New)',
  '诺福克岛(澳)': 'Norfolk Island (Australia)',
  挪威: 'Norway',
  阿曼: 'Oman',
  帕劳: 'Palau',
  巴拿马: 'Panama',
  巴布亚新几内亚: 'Papua New Guinea',
  巴拉圭: 'Paraguay',
  秘鲁: 'Peru',
  菲律宾: 'Philippines',
  波兰: 'Poland',
  葡萄牙: 'Portugal',
  巴基斯坦: 'Pakistan',
  波多黎各: 'Puerto Rico',
  卡塔尔: 'Qatar',
  留尼汪: 'Reunion',
  罗马尼亚: 'Romania',
  俄罗斯: 'Russia',
  卢旺达: 'Rwanda',
  '东萨摩亚(美)': 'Eastern Samoa',
  萨摩亚: 'Samoa',
  圣马力诺: 'San Marino',
  圣彼埃尔和密克隆岛: 'Saint Pierre and Miquelon',
  圣多美和普林西比: 'Sao Tome and Principe',
  沙特阿拉伯: 'Saudi Arabia',
  塞内加尔: 'Senegal',
  塞尔维亚: 'Serbia',
  塞舌尔: 'Seychelles',
  塞拉利昂: 'Sierra Leone',
  新加坡: 'Singapore',
  '圣马丁岛（荷兰部分）': 'Sint Maarten (Dutch part)',
  斯洛伐克: 'Slovakia',
  斯洛文尼亚: 'Slovenia',
  所罗门群岛: 'Solomon Islands',
  索马里: 'Somalia',
  南非: 'South Africa',
  西班牙: 'Spain',
  斯里兰卡: 'Sri Lanka',
  圣赫勒拿: 'St. Helena',
  圣露西亚: 'Santa Lucia',
  圣文森特和格林纳丁斯: 'Saint Vincent and the Grenadines',
  苏丹: 'Sudan',
  苏里南: 'Suriname',
  斯威士兰: 'Eswatini',
  瑞典: 'Sweden',
  瑞士: 'Switzerland',
  叙利亚: 'Syria',
  中国台湾: 'China Taiwan',
  塔吉克斯坦: 'Tajikistan',
  坦桑尼亚: 'Tanzania',
  泰国: 'Thailand',
  东帝汶: 'East Timor',
  阿拉伯联合酋长国: 'United Arab Emirates',
  多哥: 'Togo',
  '托克劳群岛(新)': 'Tokelau Islands (New)',
  汤加: 'Tonga',
  特立尼达和多巴哥: 'Trinidad and Tobago',
  突尼斯: 'Tunisia',
  土耳其: 'Turkey',
  土库曼斯坦: 'Turkmenistan',
  特克斯和凯科斯群岛: 'Turks and Caicos Islands',
  图瓦卢: 'Tuvalu',
  美国: 'US',
  乌干达: 'Uganda',
  乌克兰: 'Ukraine',
  英国: 'British',
  乌拉圭: 'Uruguay',
  乌兹别克斯坦: 'Uzbekistan',
  瓦努阿图: 'Vanuatu',
  委内瑞拉: 'Venezuela',
  越南: 'Vietnam',
  英属处女群岛: 'British Virgin Islands',
  美属维尔京群岛: 'United States Virgin Islands',
  '威克岛(美)': 'Wake Island (USA)',
  也门: 'Yemen',
  赞比亚: 'Zambia',
  桑给巴尔: 'Zanzibar',
  津巴布韦: 'Zimbabwe',
  不限国家: 'Unlimited country',
  注册地: 'region',
  注册地_大写: 'Region',
  不限: 'All ',
  指定性别年龄: 'Specify gender age',
  指定粉丝性别: 'Specify fan gender',
  指定粉丝年龄: 'Specify fan age',
  性别分布: 'Gender distribution',
  请选择: 'Please select',
  女性粉丝: 'Female fans',
  男性粉丝: 'Male fans',
  '大于：': 'Greater than:',
  请输入: 'Please enter ',
  年龄分布: 'Age distribution',
  指定粉丝数: 'Specify number of fans',
  不限粉丝数: 'All followers',
  最小粉丝数: 'Min followers',
  最大粉丝数: 'Max followers',
  '顶流达人(>50W)': 'Top Creator (> 50W)',
  '头部达人(30W-50W)': 'Head Creator (30W-50W)',
  '腰部达人(5W-30W)': 'Waist Creator (5W-30W)',
  '初级达人(1W-5W)': 'Junior Creator (1W-5W)',
  '素人(1K-1W)': 'Amateur (1 K-1 W)',
  不限粉丝数量: 'Unlimited number of fans',
  全部状态: 'All status',
  直播间守候: 'Waiting the live room',
  分享名片: 'Share user cards',
  手机账号养号: 'Mobile account nurturing',
  手机消息微信通知: 'WeChat Notification',
  全部类型: 'All types',
  导入达人: 'Import',
  全部分身: 'All browsers',
  全部手机账号: 'All mobile accounts',
  全部手机: 'All phones',
  未分配: 'Not assigned',
  已分配: 'Assigned',
  不限认领人: 'All assigned',
  个站点: 'Sites',
  '多标签间的关系：': 'Relationship between multiple tags:',
  并且: 'And',
  或者: 'Or',
  没有标签数据: 'No tag data',
  个标签: 'Tags',
  请选择区域: 'Please select the area',
  '（支持多选）': '(Multiple choices are supported)',
  修改成功: 'Modification is successful',
  根据名称进行检索: 'Search by name',
  选择分身: 'Choose a browser',
  账号封禁: 'Account blocked',
  分身名称: 'Browser name',
  已绑IP: 'IP is tied',
  平台: 'Plat',
  请选择分身经营品类: 'Please select a separate business category',
  '（可多选）': '(Multiple choices can be used)',
  分身: 'Browser',
  指纹模板: 'Fingerprint template',
  '确定要删除已选中的标签吗?': 'Are you sure you want to delete the selected tags?',
  '在此处删除标签意味着会对所有打过此标签的对象移除此标签，请确认是否继续':
    'Removing the tag here means that the tag will be removed from all objects that have been marked with this tag. Please confirm whether to continue',
  '已选中：': 'Selected:',
  个: '',
  删除选中的标签: 'Delete selected tags',
  依据标签名称进行检索: 'Search by tag name',
  标签管理: 'Tags',
  标签长度应小于60个字符: 'Tag length should be less than 60 characters',
  新标签: 'New tag',
  标签至少两个字符: 'The tag has at least two characters',
  '确定要删除此标签吗？': 'Are you sure you want to delete this tag?',
  '正在为您删除此标签，请稍候...': 'Please wait while this tag is being deleted for you...',
  为所选: 'For the selected ',
  批量增加标签: ' add tags',
  '正在为您批量增加标签，请稍候...': 'Tags are being added for you in batches. Please wait...',
  批量增加标签成功: 'Batch addition of tags successfully',
  '确定要批量清空选定{{type}}的标签吗？':
    'Are you sure you want to batch empty the tags of the selected {{type}}?',
  '批量清空后，可以通过批量打标签重新关联新的标签':
    'After batch clearing, you can re-associate new tags by batch taging',
  '正在为您批量清空标签，请稍候...': 'Tags are being cleared for you in batches. Please wait...',
  '所选{{type}}标签已清空': 'The selected {{type}} tag has been cleared',
  '增加/清空标签': 'Add/clear tags',
  '为所选{{type}}批量增加标签': 'Add tags to the selected {{type}} in batches',
  '为所选{{type}}批量清空所有标签': 'Empty all tags in batches for the selected {{type}}',
  系统: 'System',
  帮助文档: 'Help documentation',
  切换至手机版: 'Mobile view',
  支持帮助: 'Help',
  '有任何关于技术支持、商务、合作等各类需求请与我们的在线客服进行联系':
    'If you have any technical support, business, cooperation and other needs, please contact our online customer service',
  微信客服: 'WeChat customer service',
  '在线时间，08:00-24:00': 'Online time, 08:00-24:00',
  其余时间可留言: 'Leave a message for the rest of the time',
  '或请参考我们的帮助文档，': 'Or please refer to our help document to',
  进行自助式查询: 'Conduct self-service inquiries',
  产品技术支持: 'Product technical support',
  '（08:00-20:00在线）': '(Online from 08:00 to 20:00)',
  技术支持客服: 'Technical support customer service',
  商务合作: 'Business cooperation',
  商务合作洽谈: 'Business cooperation negotiation',
  热线电话: 'Tel',
  '（08:00-24:00接听）': '(Answer from 08:00 to 24:00)',
  邮件咨询: 'Email',
  技术支持邮件: 'Technical support email',
  '（2小时内响应）': '(Response within 2 hours)',
  每页显示条数应该在: 'The number of items displayed per page should be in',
  之间: 'Between',
  请输入您要跳转的页码: 'Please enter the page number you want to jump to',
  '条记录，': ' records,',
  每页显示: 'Displayed ',
  条: ' per page',
  条_空: '',
  '条/页': '/page',
  页: '',
  跳转到: 'Jump to',
  跳转: 'Jump',
  立即交付: 'Immediate delivery',
  '如何交付？': 'How to deliver?',
  您的订单正在等待财务确认: 'Your order is awaiting financial confirmation',
  '这可能需要一点时间，如果您确认款项已经支付，请您联络在线客服以加速确认过程':
    'This may take a while. If you confirm that the payment has been paid, please contact online customer service to speed up the confirmation process.',
  联系客服: 'Contact customer service',
  待财务确认: 'Pending financial confirmation',
  '（尝鲜）': '(Early adopters)',
  月: 'Months',
  个月: 'Months',
  购买时长: 'Purchased usage period',
  正在为您导入浏览器指纹模板: 'Importing browser fingerprint template for you',
  正在为您创建浏览器指纹实例: 'Creating a browser fingerprint instance for you',
  正在为您导入TikTok流程定义: 'Importing TikTok process definitions for you',
  浏览器指纹模板已成功导入: 'The browser fingerprint template has been successfully imported',
  浏览器指纹实例已成功创建: 'The browser fingerprint instance has been successfully created',
  TikTok流程定义已成功导入: 'TikTok process definition has been successfully imported',
  '好的名称，有利于增强您团队的辨识度': "A good name will help enhance your team's recognition",
  '请输入团队名称！': 'Please enter the team name!',
  '团队名称长度为1-100个字符！': 'The team name is 1-100 characters long!',
  请输入您的团队名称: 'Please enter your team name',
  下一步: 'Next',
  交付中: 'In the delivery',
  TK套餐交付: 'TK Package Delivery',
  请和在线客服联系: 'Please contact online customer service',
  请和在线客服联系进行续费事宜: 'Please contact online customer service for renewal matters',
  KOL套餐已过期: 'KOL plan has expired',
  KOL套餐即将过期: 'KOL package is about to expire',
  当前团队所购KOL套餐即将在: 'The current KOL packages purchased by the team will be available',
  '天后过期，为了保证能够正常使用，请您及时续费':
    'Expires in a few days. In order to ensure normal use, please renew your subscription in time.',
  'KOL套餐已过期，续费后可继续使用': 'The KOL plan has expired and can be used after renewal',
  立即续费: 'Renew now',
  继续使用: 'By continuing to browse',
  小红书: 'Little Red Book',
  模块: '',
  '确认退出？': 'Confirm withdrawal?',
  '请牢记您的登录密码，并建议绑定手机号，以备不时之需':
    'Please keep your login password in mind and suggest binding your mobile phone number in case of emergency',
  确认: 'Confirmed',
  请用微信扫一扫完成绑定: 'Please use WeChat to scan to complete the binding',
  该微信号已经绑定其它账号: 'This WeChat signal has been bound to other accounts',
  '一个微信账号只能绑定一个花漾账号，请更换其它的微信号完成绑定':
    'A WeChat account can only be bound to one Huayang account. Please change another WeChat account to complete the binding.',
  微信绑定成功: 'WeChat binding successfully',
  '在系统通用配置中将您的微信纳入到“默认消息接收者”，任何流程在执行过程中遇到异常都会通过微信通知到您':
    'Include your WeChat as the "default message recipient" in the general configuration of the system. Any exceptions encountered during the execution of the process will be notified to you through WeChat',
  查看系统配置: 'View system configuration',
  绑定微信: 'Bind WeChat',
  '建议您绑定微信，流程在执行中遇到任何异常信息可以通过微信通知到您':
    'It is recommended that you bind to WeChat. Any abnormal information encountered during the execution of the process can be notified to you through WeChat',
  唤醒花漾客户端: 'Launch Huayang client',
  退出登录: 'Log out',
  登录: 'Login',
  注册: 'Registered',
  '正在为您切换团队，请稍后': 'Teams are being switched for you, please wait',
  已过期: 'Expired',
  切换团队: 'Switch team',
  视图: 'View',
  '调整{{type}}视图的显示顺序': 'Adjust the display order of the {{type}} views',
  '您确定要删除“': 'Are you sure you want to delete "',
  '”视图吗？': '"View?',
  您本人创建的视图: 'The view you created yourself',
  '由＂{{name}}＂创建并分享给您': 'Created by "{{name}}" and shared with you',
  可拖动改变位置: 'Drag to change position',
  '（不可移除，不可移动）': '(Non-removable, non-movable)',
  '（不可移动）': '(Non-movable)',
  '（不可移除）': '(Non-removable)',
  分身视图排序调整: 'Split view sorting adjustment',
  当前显示视图: 'Current display view',
  待显示视图: 'View to be displayed',
  团队成员视图同步: 'Team member view synchronization',
  '一旦开启此选项，意味着当前团队所有成员都会采用相同的视图设置（只有超管与BOSS才能够开启/关闭此选项）':
    'Once this option is turned on, it means that all current team members will use the same view settings (only Supervisors and Bosses can turn this option on/off)',
  '确定要开启同步视图的选项吗？':
    'Are you sure you want to turn on the option to synchronize views?',
  '一旦开启意味着当前是团队其它成员的分身视图都会以当前的视图设置为准，请确认是否继续':
    'Once enabled, it means that the current browser views of other team members will be subject to the current view settings. Please confirm whether to continue',
  全部IP: 'All IP',
  静态IP: 'Static IP',
  动态IP: 'Dynamic IP',
  空闲的IP: 'Free IP',
  购买的平台IP: 'Purchased platform IP',
  临期的IP: 'Pending IP',
  已失效的IP: 'Invalid IP',
  IP池: 'IP pool',
  流程定义: 'RPA flow',
  流程计划: 'Flow plan',
  流程任务: 'RPA tasks',
  流程任务卡: 'RPA task card',
  内置到浏览器的流程: 'Processes built into the browser',
  我的云盘: 'My cloud disk',
  团队云盘: 'Team cloud disk',
  我的收藏: 'My collection',
  存储空间分析: 'Storage space analysis',
  您有权限访问的所有浏览器分身: 'All browsers you have permission to access',
  电商平台: 'E-commerce platform',
  '您有权限访问的所有平台类别为”电商平台“的浏览器分身，如Amazon/eBay/TikTok小店等':
    'All platforms you have permission to access are browser browsers of "e-commerce platforms", such as Amazon/eBay/TikTok stores, etc.',
  社交媒体: 'Social media',
  '您有权限访问的所有平台类别为”社交媒体“的浏览器分身，如抖音/知乎/TikTok等':
    'All platforms you have permission to access are browser browsers of "social media", such as Douyin/Zhihu/TikTok, etc.',
  未绑定IP的分身: 'Browser without IP binding',
  没有绑定IP地址的浏览器分身: 'Browser without binding IP address',
  待授权的分身: 'The browser to be authorized',
  没有明确授权的浏览器分身: 'Browser without explicit authorization',
  我收藏的分身: 'My collection of browsers',
  以您名下收藏的浏览器分身: 'Use the browser you have collected',
  窗口同步: 'Window synchronization',
  可对打开的浏览器分身窗口进行排列布局与群控操作:
    'You can arrange, layout and group control the opened browser windows',
  分身回收站: 'Split recycling station',
  删除的浏览器分身会在回收站中保留7天:
    'Deleted browser browsers will remain in the recycle bin for 7 days',
  请选择区号: 'Please select an area code',
  请输入您的手机号码: 'Please enter your mobile number',
  代金券抵扣: 'Voucher deduction',
  已经选择代金券抵扣: 'Voucher deduction has been selected',
  重新选择: 'Reselection',
  不使用代金券: 'Do not use vouchers',
  未选择代金券: 'No voucher selected',
  创建时间: 'Creation time',
  支付成功: 'Payment success',
  商品明细: 'Goods subsidiary',
  个IP: 'Of IP',
  '如果您的订单中包含购买IP地址的操作，大概需要5分钟左右的时间，创建成功后您将获得消息通知，请稍候...':
    'If your order includes the operation of purchasing an IP address, it will take about 5 minutes. You will be notified after the creation is successful. Please wait...',
  扫码失败: 'Scan code failed',
  二维码生成失败: 'QR code generation failed',
  二维码已失效: 'QR code has expired',
  重试: 'Retry',
  扫描成功: 'Scan was successful',
  重新获取: 'Reacquire',
  你已取消此次登录: 'You have canceled this login',
  请选择日期时间: 'Please select a date and time',
  流程预览: 'Process preview',
  '您将获得和“{{flow}}”相关的微信消息通知，如需取消请点击查看详情':
    'You will receive a WeChat message notification related to "{{flow}}". If you want to cancel, please click to view details',
  二维码: 'QR code',
  重置二维码: 'Reset QR code',
  '（清空已有的消息接收者）': '(Clear existing message recipients)',
  '（已有{{count}}人扫码）': '({{count}} people have scanned the code)',
  查看详情: 'View details',
  复制成功: 'Copied',
  复制: 'Copy',
  微信消息接收者的: "WeChat message recipient's",
  '（请扫码以接收流程消息，2小时有效）':
    '(Please scan the code to receive process messages, valid for 2 hours)',
  查看二维码: 'View QR code',
  必须要有一人扫码以接收流程消息: 'One person must scan the code to receive process messages',
  自动化流程已触发: 'Automated process triggered',
  '正在获取自动化流程配置...': 'Getting automated process configuration...',
  没有找到可用流程: 'No available processes found',
  未找到可用的流程: 'No available process found',
  选择账号: 'Select Account',
  根据名称检索: 'Search by name',
  该账号已封禁: 'This account has been blocked',
  账号名称: 'Account',
  站点: 'Site',
  '为这批达人打一个标签，方便员工进行达人筛选：':
    'Put a tag on this group of creators to facilitate employees to screen creators:',
  请输入标签内容: 'Please enter the tag content',
  最多10个字符: 'Up to 10 characters',
  '将达人分配给团队成员，则团队成员可在“分配给我的”中对这些达人进行私信与管理，如果已有认领人则为覆盖':
    'Assign creators to team members, and team members can privately message and manage them in "Assigned to Me". If there are already claimants, they will be overwritten',
  '已为您创建了一笔“邀约建联”的任务': ' task of "inviting" has been created for you',
  邀约话术: 'Invitational script',
  '拟使用的账号：': 'Account number to be used:',
  '带有标签“': 'With tag "',
  '”的普通账号': '" browser account',
  高级设置: 'Advanced settings',
  '”的手机账号': '"mobile account',
  '”的官方账号': '"official account',
  '”的经纪人账号': '"backstage account',
  已发送过消息的不再重复发送: 'Messages that have been sent will not be sent again',
  '当账号消息列表中包含有与此达人的聊天记录，则不再继续发送消息':
    'When the account message list contains chats with this person, messages will not be sent anymore',
  '系统会对挑选出的主播进行邀约建联：': 'The system will invite to the selected creators:',
  '1.邀约内容会从上述所选话术中进行随机选择':
    '1. The invitation script will be randomly selected from the above selected words',
  '2.会对该主播形成一笔“邀约建联”的交互轨迹，方便您检索':
    '2. An interactive trajectory of "inviting" will be formed for the creator, making it convenient for you to retrieve',
  排序调整: 'Sorting adjustment',
  上移: 'Move up',
  下移: 'Move down',
  排序: 'Sort',
  表格字段配置: 'Fields',
  当前显示字段: 'Current display fields',
  待显示字段: 'Fields to be displayed',
  此项为必填项: 'This item is required',
  上一步: 'Previous',
  提交: 'Submitted',
  完成: 'Completed',
  花漾指纹浏览器: 'Huayang fingerprint browser',
  KOL私域管理: 'KOL',
  深圳市云上悦动科技有限公司: 'Shenzhen Yunshang Yuedong Technology Co., Ltd.',
  平台提供: 'Platform',
  '自有IP（VPS）': 'Own IP (VPS)',
  花漾IPGO代理: 'Huayang IPGO Agent',
  用户自有代理: 'User-owned agent',
  有效: 'Effective',
  失效: 'Expired',
  准备: 'Ready',
  中国大陆: 'The mainland of China',
  阿联酋: 'United Arab Emirates',
  危地马拉: 'Guatemala',
  全球: 'Global',
  保密: 'Confidentiality',
  男: 'Male',
  女: 'Female',
  亚马逊: 'Amazon',
  阿里巴巴: 'Alibaba',
  抖音: 'Vibrato',
  哔哩哔哩: 'Beilai beilai',
  知乎: 'Zhihu',
  其他: 'Other',
  未激活: 'Inactive',
  正常: 'Normal',
  已禁用: 'Is disabled',
  标记为删除: 'Marked for deletion',
  加入团队: 'Join the team',
  转让账号: 'Transfer account',
  接收账号: 'Receiving account',
  联营账号: 'Associate account number',
  您不在此团队: 'You are not on this team',
  超级管理员: 'Super Admin',
  经理: 'Manager',
  员工: 'Employees',
  账号管理: 'Account management',
  IP管理: 'IP management',
  费用管理: 'Expense management',
  操作日志: 'Operation log',
  团队管理: 'Team management',
  指纹库管理: 'Fingerprint database management',
  云盘: 'Cloud disk',
  支付平台: 'Payment platform',
  其他类别: 'Other categories',
  '中文(简体，新加坡)': 'Chinese (Simplified, Singapore)',
  '中文(简体)': 'Chinese (Simplified)',
  '中文(新加坡)': 'Chinese (Singapore)',
  中文: 'Chinese',
  '中文(繁体，中国香港特别行政区)':
    'Chinese (Traditional, China Hong Kong Special Administrative Region)',
  '中文(台湾)': 'Chinese (Taiwan)',
  '中文(繁体)': 'Chinese (Traditional)',
  '中文(简体，中国香港特别行政区)':
    'Chinese (Simplified, China Hong Kong Special Administrative Region)',
  '中文(中国)': 'Chinese (China)',
  '中文(简体，中国澳门特别行政区)':
    'Chinese (Simplified, China Macao Special Administrative Region)',
  '中文(繁体，中国澳门特别行政区)':
    'Chinese (Traditional, China Macao Special Administrative Region)',
  '中文(简体，中国)': 'Chinese (Simplified, China)',
  '中文(繁体，台湾)': 'Chinese (Traditional, Taiwan)',
  '中文(中国香港特别行政区)': 'Chinese (China Hong Kong Special Administrative Region)',
  '英语(纽埃)': 'English (Niue)',
  '英语(蒙特塞拉特)': 'English (Montserrat)',
  '英语(根西岛)': 'English (Guernsey)',
  '英语(牙买加)': 'English (Jamaica)',
  '英语(赞比亚)': 'English (Zambia)',
  '英语(马耳他)': 'English (Malta)',
  '英语(利比里亚)': 'English (Liberia)',
  '英语(加纳)': 'English (Ghana)',
  '英语(以色列)': 'English (Israel)',
  '英语(帕劳)': 'English (Palau)',
  '英语(圣文森特和格林纳丁斯)': 'English (Saint Vincent and the Grenadines)',
  '英语(美国，电脑)': 'English (US, Computer)',
  '英语(欧洲)': 'English (Europe)',
  '英语(圣基茨和尼维斯)': 'English (Saint Kitts and Nevis)',
  '英语(中国澳门特别行政区)': 'English (China Macao Special Administrative Region)',
  '英语(伯利兹)': 'English (Belize)',
  '英语(瑙鲁)': 'English (Nauru)',
  '英语(北马里亚纳群岛)': 'English (Northern Mariana Islands)',
  '英语(格林纳达)': 'English (Grenada)',
  '英语(博茨瓦纳)': 'English (Botswana)',
  '英语(澳大利亚)': 'English (Australia)',
  '英语(塞浦路斯)': 'English (Cyprus)',
  '英语(卢旺达)': 'English (Rwanda)',
  '英语(爱尔兰)': 'English (Ireland)',
  '英语(基里巴斯)': 'English (Kiribati)',
  '英语(斯威士兰)': 'English (Eswatini)',
  '英语(英国)': 'English (UK)',
  '英语(美属萨摩亚)': 'English (American Samoa)',
  '英语(泽西岛)': 'English (Jersey)',
  '英语(圣诞岛)': 'English (Christmas Island)',
  '英语(奥地利)': 'English (Austria)',
  '英语(荷属圣马丁)': 'English (Sint Maarten)',
  '英语(坦桑尼亚)': 'English (Tanzania)',
  '英语(波多黎各)': 'English (Puerto Rico)',
  '英语(巴哈马)': 'English (Bahamas)',
  '英语(肯尼亚)': 'English (Kenya)',
  '英语(荷兰)': 'English (Netherlands)',
  '英语(南苏丹)': 'English (South Sudan)',
  '英语(马达加斯加)': 'English (Madagascar)',
  '英语(南非)': 'English (South Africa)',
  '英语(图瓦卢)': 'English (Tuvalu)',
  '英语(皮特凯恩群岛)': 'English (Pitcairn Islands)',
  '英语(马绍尔群岛)': 'English (Marshall Islands)',
  '英语(圭亚那)': 'English (Guyana)',
  '英语(尼日利亚)': 'English (Nigeria)',
  '英语(巴基斯坦)': 'English (Pakistan)',
  '英语(圣卢西亚)': 'English (Saint Lucia)',
  '英语(特立尼达和多巴哥)': 'English (Trinidad and Tobago)',
  '英语(百慕大)': 'English (Bermuda)',
  '英语(瓦努阿图)': 'English (Vanuatu)',
  '英语(美国)': 'English (US)',
  '英语(诺福克岛)': 'English (Norfolk Island)',
  '英语(关岛)': 'English (Guam)',
  '英语(安圭拉)': 'English (Anguilla)',
  '英语(喀麦隆)': 'English (Cameroon)',
  '英语(汤加)': 'English (Tonga)',
  '英语(巴布亚新几内亚)': 'English (Papua New Guinea)',
  '英语(厄立特里亚)': 'English (Eritrea)',
  '英语(菲律宾)': 'English (Philippines)',
  '英语(多米尼克)': 'English (Dominica)',
  '英语(库克群岛)': 'English (Island)',
  '英语(布隆迪)': 'English (Burundi)',
  '英语(安提瓜和巴布达)': 'English (Antigua and Barbuda)',
  '英语(萨摩亚)': 'English (Samoa)',
  '英语(纳米比亚)': 'English (Namibia)',
  '英语(塞拉利昂)': 'English (Sierra Leone)',
  '英语(圣赫勒拿)': 'English (St. Helena)',
  '英语(开曼群岛)': 'English (Cayman Islands)',
  '英语(丹麦)': 'English (Denmark)',
  '英语(津巴布韦)': 'English (Zimbabwe)',
  '英语(美国本土外小岛屿)': 'English (small extraterritorial islands)',
  '英语(托克劳)': 'English (Tokelau)',
  '英语(斯洛文尼亚)': 'English (Slovenia)',
  '英语(密克罗尼西亚)': 'English (Micronesia)',
  '英语(比利时)': 'English (Belgium)',
  '英语(新加坡)': 'English (Singapore)',
  '英语(瑞士)': 'English (Switzerland)',
  '英语(苏丹)': 'English (Sudan)',
  '英语(马来西亚)': 'English (Malaysia)',
  '英语(福克兰群岛)': 'English (Falkland Islands)',
  '英语(冈比亚)': 'English (Gambia)',
  '英语(迪戈加西亚岛)': 'English (Diego Garcia)',
  '英语(瑞典)': 'English (Sweden)',
  '英语(世界)': 'English (World)',
  英语: 'English',
  '英语(所罗门群岛)': 'English (Solomon Islands)',
  '英语(马拉维)': 'English (Malawi)',
  '英语(英属印度洋领地)': 'English (British Indian Ocean Territory)',
  '英语(德国)': 'English (Germany)',
  '英语(科科斯（基林）群岛)': 'English (Cocos (Keeling) Islands)',
  '英语(芬兰)': 'English (Finland)',
  '英语(塞舌尔)': 'English (Seychelles)',
  '英语(美属维尔京群岛)': 'English (United States Virgin Islands)',
  '英语(乌干达)': 'English (Uganda)',
  '英语(新西兰)': 'English (New Zealand)',
  '英语(斐济)': 'English (Fiji)',
  '英语(巴巴多斯)': 'English (Barbados)',
  '英语(毛里求斯)': 'English (Mauritius)',
  '英语(马恩岛)': 'English (Isle of Man)',
  '英语(莱索托)': 'English (Lesotho)',
  '英语(中国香港特别行政区)': 'English (China Hong Kong Special Administrative Region)',
  '英语(直布罗陀)': 'English (Gibraltar)',
  '英语(加拿大)': 'English (Canada)',
  '英语(英属维尔京群岛)': 'English (The British Virgin Islands)',
  '英语(特克斯和凯科斯群岛)': 'English (Turks and Caicos Islands)',
  '英语(印度)': 'English (India)',
  南非荷兰语: 'Of Afrikaans',
  '南非荷兰语(纳米比亚)': 'Afrikaans (Namibia)',
  '南非荷兰语(南非)': 'Afrikaans (South Africa)',
  亚罕语: 'Ahan language',
  '亚罕语(喀麦隆)': 'Yahan (Cameroon)',
  阿肯语: 'Akan language',
  '阿肯语(加纳)': 'Akan (Ghana)',
  阿姆哈拉语: 'Amharic language',
  '阿姆哈拉语(埃塞俄比亚)': 'Amharic (Ethiopia)',
  阿拉伯语: 'Arabic',
  '阿拉伯语(世界)': 'Arabic (World)',
  '阿拉伯语(阿拉伯联合酋长国)': 'Arabic (United Arab Emirates)',
  '阿拉伯语(巴林)': 'Arabic (Bahrain)',
  '阿拉伯语(吉布提)': 'Arabic (Djibouti)',
  '阿拉伯语(阿尔及利亚)': 'Arabic (Algeria)',
  '阿拉伯语(埃及)': 'Arabic (Egypt)',
  '阿拉伯语(西撒哈拉)': 'Arabic (Western Sahara)',
  '阿拉伯语(厄立特里亚)': 'Arabic (Eritrea)',
  '阿拉伯语(以色列)': 'Arabic (Israel)',
  '阿拉伯语(伊拉克)': 'Arabic (Iraq)',
  '阿拉伯语(约旦)': 'Arabic (Jordan)',
  '阿拉伯语(科摩罗)': 'Arabic (Comoros)',
  '阿拉伯语(科威特)': 'Arabic (Kuwait)',
  '阿拉伯语(黎巴嫩)': 'Arabic (Lebanon)',
  '阿拉伯语(利比亚)': 'Arabic (Libya)',
  '阿拉伯语(摩洛哥)': 'Arabic (Morocco)',
  '阿拉伯语(毛里塔尼亚)': 'Arabic (Mauritania)',
  '阿拉伯语(阿曼)': 'Arabic (Oman)',
  '阿拉伯语(巴勒斯坦领土)': 'Arabic (Palestinian Territory)',
  '阿拉伯语(卡塔尔)': 'Arabic (Qatar)',
  '阿拉伯语(沙特阿拉伯)': 'Arabic (Saudi Arabia)',
  '阿拉伯语(苏丹)': 'Arabic (Sudan)',
  '阿拉伯语(索马里)': 'Arabic (Somali)',
  '阿拉伯语(南苏丹)': 'Arabic (South Sudan)',
  '阿拉伯语(叙利亚)': 'Arabic (Syria)',
  '阿拉伯语(乍得)': 'Arabic (Chad)',
  '阿拉伯语(突尼斯)': 'Arabic (Tunisia)',
  '阿拉伯语(也门)': 'Arabic (Yemen)',
  阿萨姆语: 'Assamese',
  '阿萨姆语(印度)': 'Assam (India)',
  帕雷语: 'Pare language',
  '帕雷语(坦桑尼亚)': 'Pare (Tanzania)',
  阿斯图里亚斯语: 'Asturian language',
  '阿斯图里亚斯语(西班牙)': 'Asturian (Spain)',
  阿塞拜疆语: 'Azerbaijan',
  '阿塞拜疆语(西里尔文)': 'Azerbaijan (Cyrillic)',
  '阿塞拜疆语(西里尔文，阿塞拜疆)': 'Azerbaijan (Cyrillic, Azerbaijan)',
  '阿塞拜疆语(拉丁文)': 'Azerbaijan (Latin)',
  '阿塞拜疆语(拉丁文，阿塞拜疆)': 'Azerbaijan (Latin, Azerbaijan)',
  巴萨语: 'Basa language',
  '巴萨语(喀麦隆)': 'Basa (Cameroon)',
  白俄罗斯语: 'Belarusian',
  '白俄罗斯语(白俄罗斯)': 'Belarusian (Belarus)',
  本巴语: 'Bemba',
  '本巴语(赞比亚)': 'Bemba (Zambia)',
  贝纳语: 'Bena language',
  '贝纳语(坦桑尼亚)': 'Bena (Tanzania)',
  保加利亚语: 'Bulgarian',
  '保加利亚语(保加利亚)': 'Bulgarian (Bulgaria)',
  班巴拉语: 'Bambala language',
  '班巴拉语(马里)': 'Bambara (Mali)',
  孟加拉语: 'Bengali',
  '孟加拉语(孟加拉国)': 'Bengali (Bangladesh)',
  '孟加拉语(印度)': 'Bengali (India)',
  藏语: 'Tibetan',
  '藏语(中国)': 'Tibetan (China)',
  '藏语(印度)': 'Tibetan (India)',
  布列塔尼语: 'Breton',
  '布列塔尼语(法国)': 'Breton (France)',
  博多语: 'Bodo language',
  '博多语(印度)': 'Bodo (India)',
  波斯尼亚语: 'Bosnian',
  '波斯尼亚语(西里尔文)': 'Bosnian (Cyrillic)',
  '波斯尼亚语(西里尔文，波斯尼亚和黑塞哥维那)': 'Bosnian (Cyrillic, Bosnia and Herzegovina)',
  '波斯尼亚语(拉丁文)': 'Bosnian (Latin)',
  '波斯尼亚语(拉丁文，波斯尼亚和黑塞哥维那)': 'Bosnian (Latin, Bosnia and Herzegovina)',
  加泰罗尼亚语: 'Catalan',
  '加泰罗尼亚语(安道尔)': 'Catalan (Andorra)',
  '加泰罗尼亚语(西班牙)': 'Catalan (Spain)',
  '加泰罗尼亚语(西班牙，瓦伦西亚文)': 'Catalan (Spain, Valencia)',
  '加泰罗尼亚语(法国)': 'Catalan (France)',
  '加泰罗尼亚语(意大利)': 'Catalan (Italy)',
  'ccp(孟加拉国)': 'Ccp(Bangladesh)',
  'ccp(印度)': 'Ccp(India)',
  车臣语: 'Chechen',
  '车臣语(俄罗斯)': 'Chechen (Russia)',
  奇加语: 'Kiga language',
  '奇加语(乌干达)': 'Chiga (Uganda)',
  切罗基语: 'Cherokee language',
  '切罗基语(美国)': 'Cherokee (United States)',
  中库尔德语: 'Central Kurdish',
  '中库尔德语(伊拉克)': 'Central Kurdish (Iraq)',
  '中库尔德语(伊朗)': 'Central Kurdish (Iran)',
  捷克语: 'Czech',
  '捷克语(捷克)': 'Czech (Czech)',
  教会斯拉夫语: 'Church Slavonic',
  '教会斯拉夫语(俄罗斯)': 'Church Slavic (Russia)',
  威尔士语: 'Welsh',
  '威尔士语(英国)': 'Welsh (UK)',
  丹麦语: 'Danish',
  '丹麦语(丹麦)': 'Danish (Denmark)',
  '丹麦语(格陵兰)': 'Danish (Greenland)',
  台塔语: 'Taeta language',
  '台塔语(肯尼亚)': 'Teta (Kenya)',
  德语: 'German',
  '德语(奥地利)': 'German (Austria)',
  '德语(比利时)': 'German (Belgium)',
  '德语(瑞士)': 'German (Switzerland)',
  '德语(德国)': 'German (Germany)',
  '德语(意大利)': 'German (Italy)',
  '德语(列支敦士登)': 'German (Liechtenstein)',
  '德语(卢森堡)': 'German (Luxembourg)',
  哲尔马语: 'Jerma language',
  '哲尔马语(尼日尔)': 'Jerma (Niger)',
  下索布语: 'Lower Sorbian',
  '下索布语(德国)': 'Lower Sorbian (Germany)',
  都阿拉语: 'Duala language',
  '都阿拉语(喀麦隆)': 'Douala (Cameroon)',
  朱拉语: 'Chula language',
  '朱拉语(塞内加尔)': 'Chula (Senegal)',
  宗卡语: 'Zongka language',
  '宗卡语(不丹)': 'Dongka (Bhutan)',
  恩布语: 'Embu language',
  '恩布语(肯尼亚)': 'Embu (Kenya)',
  埃维语: 'Ewe',
  '埃维语(加纳)': 'Ewe (Ghana)',
  '埃维语(多哥)': 'Ewe (Togo)',
  希腊语: 'Greek',
  '希腊语(塞浦路斯)': 'Greek (Cyprus)',
  '希腊语(希腊)': 'Greek (Greece)',
  世界语: 'Esperanto',
  '世界语(世界)': 'Esperanto (World)',
  西班牙语: 'Spanish',
  '西班牙语(拉丁美洲)': 'Spanish (Latin America)',
  '西班牙语(阿根廷)': 'Spanish (Argentina)',
  '西班牙语(玻利维亚)': 'Spanish (Bolivia)',
  '西班牙语(巴西)': 'Spanish (Brazil)',
  '西班牙语(伯利兹)': 'Spanish (Belize)',
  '西班牙语(智利)': 'Spanish (Chile)',
  '西班牙语(哥伦比亚)': 'Spanish (Colombia)',
  '西班牙语(哥斯达黎加)': 'Spanish (Costa Rica)',
  '西班牙语(古巴)': 'Spanish (Cuba)',
  '西班牙语(多米尼加共和国)': 'Spanish (Dominican Republic)',
  '西班牙语(休达及梅利利亚)': 'Spanish (Ceuta and Melilla)',
  '西班牙语(厄瓜多尔)': 'Spanish (Ecuador)',
  '西班牙语(西班牙)': 'Spanish (Spain)',
  '西班牙语(赤道几内亚)': 'Spanish (Equatorial Guinea)',
  '西班牙语(危地马拉)': 'Spanish (Guatemala)',
  '西班牙语(洪都拉斯)': 'Spanish (Honduras)',
  '西班牙语(加纳利群岛)': 'Spanish (Canary Islands)',
  '西班牙语(墨西哥)': 'Spanish (Mexico)',
  '西班牙语(尼加拉瓜)': 'Spanish (Nicaragua)',
  '西班牙语(巴拿马)': 'Spanish (Panama)',
  '西班牙语(秘鲁)': 'Spanish (Peru)',
  '西班牙语(菲律宾)': 'Spanish (Philippines)',
  '西班牙语(波多黎各)': 'Spanish (Puerto Rico)',
  '西班牙语(巴拉圭)': 'Spanish (Paraguay)',
  '西班牙语(萨尔瓦多)': 'Spanish (El Salvador)',
  '西班牙语(美国)': 'Spanish (US)',
  '西班牙语(乌拉圭)': 'Spanish (Uruguay)',
  '西班牙语(委内瑞拉)': 'Spanish (Venezuela)',
  爱沙尼亚语: 'Estonia',
  '爱沙尼亚语(爱沙尼亚)': 'Estonia (Estonia)',
  巴斯克语: 'Basque',
  '巴斯克语(西班牙)': 'Basque (Spain)',
  旺杜语: 'Wangdu language',
  '旺杜语(喀麦隆)': 'Wangdu (Cameroon)',
  波斯语: 'Persian',
  '波斯语(阿富汗)': 'Persian (Afghanistan)',
  '波斯语(伊朗)': 'Persian (Iran)',
  富拉语: 'Fula language',
  '富拉语(喀麦隆)': 'Foula (Cameroon)',
  '富拉语(几内亚)': 'Foula (Guinea)',
  '富拉语(毛里塔尼亚)': 'Foula (Mauritania)',
  '富拉语(塞内加尔)': 'Foula (Senegal)',
  芬兰语: 'Finnish',
  '芬兰语(芬兰)': 'Finnish (Finland)',
  菲律宾语: 'Filipino',
  '菲律宾语(菲律宾)': 'Filipino (Philippines)',
  法罗语: 'Faroese',
  '法罗语(丹麦)': 'Faroese (Denmark)',
  '法罗语(法罗群岛)': 'Faroese (Faroe Islands)',
  法语: 'French',
  '法语(比利时)': 'French (Belgium)',
  '法语(布基纳法索)': 'French (Burkina Faso)',
  '法语(布隆迪)': 'French (Burundi)',
  '法语(贝宁)': 'French (Benin)',
  '法语(圣巴泰勒米)': 'French (Saint Barthélemy)',
  '法语(加拿大)': 'French (Canada)',
  '法语(刚果（金）)': 'French (DRC)',
  '法语(中非共和国)': 'French (Central African Republic)',
  '法语(刚果（布）)': 'French (Congo (Brazzaville))',
  '法语(瑞士)': 'French (Switzerland)',
  '法语(科特迪瓦)': "French (Cote d'Ivoire)",
  '法语(喀麦隆)': 'French (Cameroon)',
  '法语(吉布提)': 'French (Djibouti)',
  '法语(阿尔及利亚)': 'French (Algeria)',
  '法语(法国)': 'French (France)',
  '法语(加蓬)': 'French (Gabon)',
  '法语(法属圭亚那)': 'French (Guyane française)',
  '法语(几内亚)': 'French (Guinea)',
  '法语(瓜德罗普)': 'French (Guadeloupe)',
  '法语(赤道几内亚)': 'French (Equatorial Guinea)',
  '法语(海地)': 'French (Haiti)',
  '法语(科摩罗)': 'French (Comoros)',
  '法语(卢森堡)': 'French (Luxembourg)',
  '法语(摩洛哥)': 'French (Morocco)',
  '法语(摩纳哥)': 'French (Monaco)',
  '法语(法属圣马丁)': 'French (French Saint Martin)',
  '法语(马达加斯加)': 'French (Madagascar)',
  '法语(马里)': 'French (Mali)',
  '法语(马提尼克)': 'French (Martinique)',
  '法语(毛里塔尼亚)': 'French (Mauritania)',
  '法语(毛里求斯)': 'French (Mauritius)',
  '法语(新喀里多尼亚)': 'French (New Caledonia)',
  '法语(尼日尔)': 'French (Niger)',
  '法语(法属波利尼西亚)': 'French (French Polynesia)',
  '法语(圣皮埃尔和密克隆群岛)': 'French (Saint Pierre and Miquelon)',
  '法语(留尼汪)': 'French (Reunion)',
  '法语(卢旺达)': 'French (Rwanda)',
  '法语(塞舌尔)': 'French (Seychelles)',
  '法语(塞内加尔)': 'French (Senegal)',
  '法语(叙利亚)': 'French (Syria)',
  '法语(乍得)': 'French (Chad)',
  '法语(多哥)': 'French (Togo)',
  '法语(突尼斯)': 'French (Tunisia)',
  '法语(瓦努阿图)': 'French (Vanuatu)',
  '法语(瓦利斯和富图纳)': 'French (Wallis and Futuna)',
  '法语(马约特)': 'French (Mayotte)',
  弗留利语: 'Friuli language',
  '弗留利语(意大利)': 'Friuli (Italy)',
  西弗里西亚语: 'West Frisian language',
  '西弗里西亚语(荷兰)': 'West Frisian (Netherlands)',
  爱尔兰语: 'Irish',
  '爱尔兰语(爱尔兰)': 'Irish (Ireland)',
  苏格兰盖尔语: 'Scottish Gaelic',
  '苏格兰盖尔语(英国)': 'Scottish Gaelic (UK)',
  加利西亚语: 'Galician',
  '加利西亚语(西班牙)': 'Galician (Spain)',
  瑞士德语: 'Swiss German',
  '瑞士德语(瑞士)': 'Swiss German (Switzerland)',
  '瑞士德语(法国)': 'Swiss German (France)',
  '瑞士德语(列支敦士登)': 'Swiss German (Liechtenstein)',
  古吉拉特语: 'Gujarati',
  '古吉拉特语(印度)': 'Gujarati (India)',
  古西语: 'Old Spanish',
  '古西语(肯尼亚)': 'Ancient Spanish (Kenya)',
  马恩语: 'Manx',
  '马恩语(马恩岛)': 'Mann (Isle of Man)',
  豪萨语: 'Hausa',
  '豪萨语(加纳)': 'Hausa (Ghana)',
  '豪萨语(尼日尔)': 'Hausa (Niger)',
  '豪萨语(尼日利亚)': 'Hausa (Nigeria)',
  夏威夷语: 'Hawaiian',
  '夏威夷语(美国)': 'Hawaiian (US)',
  希伯来文: 'Hebrew',
  '希伯来文(以色列)': 'Hebrew (Israel)',
  印地语: 'Hindi',
  '印地语(印度)': 'Hindi (India)',
  克罗地亚语: 'Croatian',
  '克罗地亚语(波斯尼亚和黑塞哥维那)': 'Croatian (Bosnia and Herzegovina)',
  '克罗地亚语(克罗地亚)': 'Croatian (Croatia)',
  上索布语: 'Upper Sorbian',
  '上索布语(德国)': 'Upper Sorbian (Germany)',
  匈牙利语: 'Hungarian',
  '匈牙利语(匈牙利)': 'Hungarian (Hungary)',
  亚美尼亚语: 'Armenian',
  '亚美尼亚语(亚美尼亚)': 'Armenian (Armenia)',
  印度尼西亚文: 'Indonesian',
  '印度尼西亚文(印度尼西亚)': 'Indonesian (Indonesia)',
  伊博语: 'Igbo',
  '伊博语(尼日利亚)': 'Igbo (Nigeria)',
  四川彝语: 'Sichuan Yi language',
  '四川彝语(中国)': 'Sichuan Yi language (China)',
  冰岛语: 'Icelandic',
  '冰岛语(冰岛)': 'Icelandic (Iceland)',
  意大利语: 'Italian',
  '意大利语(瑞士)': 'Italian (Switzerland)',
  '意大利语(意大利)': 'Italian (Italy)',
  '意大利语(圣马力诺)': 'Italian (San Marino)',
  '意大利语(梵蒂冈)': 'Italian (Vatican)',
  日语: 'Japanese',
  '日语(日本)': 'Japanese (Japan)',
  '日语(日本，JP，和历)': 'Japanese (Japan, JP, Japanese calendar)',
  恩艮巴语: 'Ngemba language',
  '恩艮巴语(喀麦隆)': 'Ngemba (Cameroon)',
  马切姆语: 'Machem language',
  '马切姆语(坦桑尼亚)': 'Machem (Tanzania)',
  格鲁吉亚语: 'Georgian',
  '格鲁吉亚语(格鲁吉亚)': 'Georgian (Georgia)',
  卡拜尔语: 'Kabar language',
  '卡拜尔语(阿尔及利亚)': 'Kabar (Algeria)',
  卡姆巴语: 'Kamba language',
  '卡姆巴语(肯尼亚)': 'Kamba (Kenya)',
  马孔德语: 'Macon',
  '马孔德语(坦桑尼亚)': 'Macon (Tanzania)',
  卡布佛得鲁语: 'Kabfodru language',
  '卡布佛得鲁语(佛得角)': 'Kabfodru (Cape Verde)',
  西桑海语: 'Sisanghai language',
  '西桑海语(马里)': 'Sisanghai (Mali)',
  吉库尤语: 'Jikuyu language',
  '吉库尤语(肯尼亚)': 'Kikuyu (Kenya)',
  哈萨克语: 'Kazakh',
  '哈萨克语(哈萨克斯坦)': 'Kazakh (Kazakhstan)',
  卡库语: 'Kaku language',
  '卡库语(喀麦隆)': 'Kaku (Cameroon)',
  格陵兰语: 'Greenland',
  '格陵兰语(格陵兰)': 'Greenland (Greenland)',
  卡伦金语: 'Kalenjin language',
  '卡伦金语(肯尼亚)': 'Kalenjin (Kenya)',
  高棉语: 'Khmer',
  '高棉语(柬埔寨)': 'Khmer (Cambodia)',
  卡纳达语: 'Kannada language',
  '卡纳达语(印度)': 'Kannada (India)',
  韩语: 'Korean',
  '韩语(朝鲜)': 'Korean (North Korea)',
  '韩语(韩国)': 'Korean (Korea)',
  孔卡尼语: 'Konkani language',
  '孔卡尼语(印度)': 'Konkani (India)',
  克什米尔语: 'Kashmiri',
  '克什米尔语(印度)': 'Kashmiri (India)',
  香巴拉语: 'Shambhala language',
  '香巴拉语(坦桑尼亚)': 'Shambhala (Tanzania)',
  巴菲亚语: 'Bafia language',
  '巴菲亚语(喀麦隆)': 'Bafia (Cameroon)',
  科隆语: 'Cologne',
  '科隆语(德国)': 'Cologne (Germany)',
  康沃尔语: 'Cornish',
  '康沃尔语(英国)': 'Cornish (UK)',
  柯尔克孜语: 'Kirgiz',
  '柯尔克孜语(吉尔吉斯斯坦)': 'Kirgiz (Kyrgyzstan)',
  朗吉语: 'Langji language',
  '朗吉语(坦桑尼亚)': 'Langi (Tanzania)',
  卢森堡语: 'Luxembourgish',
  '卢森堡语(卢森堡)': 'Luxembourg (Luxembourg)',
  卢干达语: 'Luganda language',
  '卢干达语(乌干达)': 'Luganda (Uganda)',
  拉科塔语: 'Lakota language',
  '拉科塔语(美国)': 'Lakota (US)',
  林加拉语: 'Lingala language',
  '林加拉语(安哥拉)': 'Lingala (Angola)',
  '林加拉语(刚果（金）)': 'Lingala (DRC)',
  '林加拉语(中非共和国)': 'Lingala (Central African Republic)',
  '林加拉语(刚果（布）)': 'Lingala (Congolese (Brazzaville))',
  老挝语: 'Lao',
  '老挝语(老挝)': 'Lao (Laos)',
  北卢尔语: 'Northern Lur language',
  '北卢尔语(伊拉克)': 'Northern Lur (Iraq)',
  '北卢尔语(伊朗)': 'Northern Lur (Iran)',
  立陶宛语: 'Lithuania',
  '立陶宛语(立陶宛)': 'Lithuania (Lithuania)',
  鲁巴加丹加语: 'Luba Katanga language',
  '鲁巴加丹加语(刚果（金）)': 'Luba Katanga (DRC)',
  卢奥语: 'Luo are',
  '卢奥语(肯尼亚)': 'Lu (Kenya)',
  卢雅语: 'Luya language',
  '卢雅语(肯尼亚)': 'Luya (Kenya)',
  拉脱维亚语: 'Latvia',
  '拉脱维亚语(拉脱维亚)': 'Latvia (Latvia)',
  马赛语: 'Masai language',
  '马赛语(肯尼亚)': 'Masai (Kenya)',
  '马赛语(坦桑尼亚)': 'Masai (Tanzania)',
  梅鲁语: 'Meru language',
  '梅鲁语(肯尼亚)': 'Meru (Kenya)',
  毛里求斯克里奥尔语: 'Mauritian Creole',
  '毛里求斯克里奥尔语(毛里求斯)': 'Mauritian Creole (Mauritius)',
  马拉加斯语: 'Malagas language',
  '马拉加斯语(马达加斯加)': 'Malagas (Madagascar)',
  马库阿语: 'Makua language',
  '马库阿语(莫桑比克)': 'Makua (Mozambique)',
  梅塔语: 'Meta language',
  '梅塔语(喀麦隆)': 'Meta (Cameroon)',
  马其顿语: 'Macedonian',
  '马其顿语(马其顿)': 'Macedonian (Macedonian)',
  马拉雅拉姆语: 'Malayalam language',
  '马拉雅拉姆语(印度)': 'Malayalam (India)',
  蒙古语: 'Mongolian',
  '蒙古语(蒙古)': 'Mongolian (Mongolian)',
  马拉地语: 'Marathi',
  '马拉地语(印度)': 'Marathi (India)',
  马来语: 'Malay',
  '马来语(文莱)': 'Malay (Brunei)',
  '马来语(马来西亚)': 'Malay (Malaysia)',
  '马来语(新加坡)': 'Malay (Singapore)',
  马耳他语: 'Malta',
  '马耳他语(马耳他)': 'Malta (Malta)',
  蒙当语: 'Mengdang language',
  '蒙当语(喀麦隆)': 'Mondang (Cameroon)',
  缅甸语: 'Burmese',
  '缅甸语(缅甸)': 'Burmese (Myanmar)',
  马赞德兰语: 'Mazandran language',
  '马赞德兰语(伊朗)': 'Mazandran (Iran)',
  纳马语: 'Nama language',
  '纳马语(纳米比亚)': 'Nama (Namibia)',
  书面挪威语: 'Written Norway',
  '书面挪威语(挪威)': 'Written in Norway (Norway)',
  '书面挪威语(斯瓦尔巴和扬马延)': 'Written in Norway (Svalbard and Jan Mayen)',
  北恩德贝勒语: 'North Ndebele language',
  '北恩德贝勒语(津巴布韦)': 'Northern Ndebele (Zimbabwe)',
  低地德语: 'Low German',
  '低地德语(德国)': 'Low German (Germany)',
  '低地德语(荷兰)': 'Low German (Netherlands)',
  尼泊尔语: 'Nepal',
  '尼泊尔语(印度)': 'Nepal (India)',
  '尼泊尔语(尼泊尔)': 'Nepal (Nepal)',
  荷兰语: 'Dutch',
  '荷兰语(阿鲁巴)': 'Dutch (Aruba)',
  '荷兰语(比利时)': 'Dutch (Belgium)',
  '荷兰语(荷属加勒比区)': 'Dutch (Dutch Caribbean)',
  '荷兰语(库拉索)': 'Dutch (Curacao)',
  '荷兰语(荷兰)': 'Dutch (Netherlands)',
  '荷兰语(苏里南)': 'Dutch (Suriname)',
  '荷兰语(荷属圣马丁)': 'Dutch (Sint Maarten)',
  夸西奥语: 'Quasio language',
  '夸西奥语(喀麦隆)': 'Quasio (Cameroon)',
  挪威尼诺斯克语: 'Norway Ninosk',
  '挪威语(挪威，Nynorsk)': 'Norway (Norway, Nynorsk)',
  恩甘澎语: 'Nganpeng language',
  '恩甘澎语(喀麦隆)': 'Nganpeng (Cameroon)',
  挪威语: 'Norway',
  '挪威语(挪威)': 'Norway (Norway)',
  努埃尔语: 'Nuer',
  '努埃尔语(南苏丹)': 'Nuer (South Sudan)',
  尼昂科勒语: 'Niangkol language',
  '尼昂科勒语(乌干达)': 'Nyangkol (Uganda)',
  奥罗莫语: 'Oromo language',
  '奥罗莫语(埃塞俄比亚)': 'Oromo (Ethiopia)',
  '奥罗莫语(肯尼亚)': 'Oromo (Kenya)',
  奥里亚语: 'Odia',
  '奥里亚语(印度)': 'Oriya (India)',
  奥塞梯语: 'Ossetian',
  '奥塞梯语(格鲁吉亚)': 'Ossetian (Georgia)',
  '奥塞梯语(俄罗斯)': 'Ossetian (Russia)',
  旁遮普语: 'Punjabi',
  '旁遮普语(阿拉伯文)': 'Punjabi (Arabic)',
  '旁遮普语(阿拉伯文，巴基斯坦)': 'Punjabi (Arabic, Pakistan)',
  '旁遮普语(果鲁穆奇文)': 'Punjabi (Gorumuchi)',
  '旁遮普语(果鲁穆奇文，印度)': 'Punjabi (Gorumuchi, India)',
  波兰语: 'Polish',
  '波兰语(波兰)': 'Polish (Poland)',
  普鲁士语: 'Prussian language',
  '普鲁士语(世界)': 'Prussian (World)',
  普什图语: 'Pashto',
  '普什图语(阿富汗)': 'Pashto (Afghanistan)',
  葡萄牙语: 'Portugal',
  '葡萄牙语(安哥拉)': 'Portugal (Angola)',
  '葡萄牙语(巴西)': 'Portugal (Brazil)',
  '葡萄牙语(瑞士)': 'Portugal (Switzerland)',
  '葡萄牙语(佛得角)': 'Portugal (Cape Verde)',
  '葡萄牙语(赤道几内亚)': 'Portugal (Equatorial Guinea)',
  '葡萄牙语(几内亚比绍)': 'Portugal (Guinea-Bissau)',
  '葡萄牙语(卢森堡)': 'Portugal (Luxembourg)',
  '葡萄牙语(中国澳门特别行政区)': 'Portugal (China Macao Special Administrative Region)',
  '葡萄牙语(莫桑比克)': 'Portugal (Mozambique)',
  '葡萄牙语(葡萄牙)': 'Portugal (Portugal)',
  '葡萄牙语(圣多美和普林西比)': 'Portugal (Sao Tome and Principe)',
  '葡萄牙语(东帝汶)': 'Portugal (East Timor)',
  克丘亚语: 'Quechua language',
  '克丘亚语(玻利维亚)': 'Quechua (Bolivia)',
  '克丘亚语(厄瓜多尔)': 'Quechua (Ecuador)',
  '克丘亚语(秘鲁)': 'Quechua (Peru)',
  罗曼什语: 'Romansh',
  '罗曼什语(瑞士)': 'Romansh (Switzerland)',
  隆迪语: 'Rondi language',
  '隆迪语(布隆迪)': 'Lundi (Burundi)',
  罗马尼亚语: 'Romania',
  '罗马尼亚语(摩尔多瓦)': 'Romania (Moldova)',
  '罗马尼亚语(罗马尼亚)': 'Romania (Romania)',
  兰博语: 'Rambo language',
  '兰博语(坦桑尼亚)': 'Rambo (Tanzania)',
  俄语: 'Russian',
  '俄语(白俄罗斯)': 'Russian (Belarus)',
  '俄语(吉尔吉斯斯坦)': 'Russian (Kyrgyzstan)',
  '俄语(哈萨克斯坦)': 'Russian (Kazakhstan)',
  '俄语(摩尔多瓦)': 'Russian (Moldova)',
  '俄语(俄罗斯)': 'Russian (Russia)',
  '俄语(乌克兰)': 'Russian (Ukraine)',
  卢旺达语: 'Rwandophone',
  '卢旺达语(卢旺达)': 'Rwandan (Rwanda)',
  罗瓦语: 'Rwa language',
  '罗瓦语(坦桑尼亚)': 'Rowa (Tanzania)',
  萨哈语: 'Saha language',
  '萨哈语(俄罗斯)': 'Sakha (Russia)',
  桑布鲁语: 'Samburu language',
  '桑布鲁语(肯尼亚)': 'Samburu (Kenya)',
  桑古语: 'Sangyu',
  '桑古语(坦桑尼亚)': 'Sangu (Tanzania)',
  信德语: 'Sindhi',
  '信德语(巴基斯坦)': 'Sindeur (Pakistan)',
  北方萨米语: 'Northern Sami',
  '北方萨米语(芬兰)': 'Northern Sami (Finland)',
  '北方萨米语(挪威)': 'Northern Sami (Norway)',
  '北方萨米语(瑞典)': 'Northern Sami (Sweden)',
  塞纳语: 'Sena language',
  '塞纳语(莫桑比克)': 'Sena (Mozambique)',
  东桑海语: 'Dongsanghai language',
  '东桑海语(马里)': 'Dongsanghai (Mali)',
  桑戈语: 'In Sango',
  '桑戈语(中非共和国)': 'Sango (Central African Republic)',
  希尔哈语: 'Sirha language',
  '希尔哈语(拉丁文)': 'Sirha (Latin)',
  '希尔哈语(拉丁文，摩洛哥)': 'Sirha (Latin, Morocco)',
  '希尔哈语(提非纳文)': 'Sirha (Tifina)',
  '希尔哈语(提非纳文，摩洛哥)': 'Sirha (Tifina, Morocco)',
  僧伽罗语: 'Sinhala',
  '僧伽罗语(斯里兰卡)': 'Sinhala (Sri Lanka)',
  斯洛伐克语: 'Slovakia',
  '斯洛伐克语(斯洛伐克)': 'Slovakia (Slovakia)',
  斯洛文尼亚语: 'Slovenia',
  '斯洛文尼亚语(斯洛文尼亚)': 'Slovenia (Slovenia)',
  伊纳里萨米语: 'Inari Sami',
  '伊纳里萨米语(芬兰)': 'Inari Sami (Finland)',
  绍纳语: 'Shona language',
  '绍纳语(津巴布韦)': 'Shona (Zimbabwe)',
  索马里语: 'Somali',
  '索马里语(吉布提)': 'Somali (Djibouti)',
  '索马里语(埃塞俄比亚)': 'Somali (Ethiopia)',
  '索马里语(肯尼亚)': 'Somali (Kenya)',
  '索马里语(索马里)': 'Somali (Somali)',
  阿尔巴尼亚语: 'Albania',
  '阿尔巴尼亚语(阿尔巴尼亚)': 'Albania (Albania)',
  '阿尔巴尼亚语(马其顿)': 'Albania (Macedonian)',
  '阿尔巴尼亚语(科索沃)': 'Albania (Kosovo)',
  塞尔维亚语: 'Serbian',
  '塞尔维亚语(波斯尼亚和黑塞哥维那)': 'Serbian (Bosnia and Herzegovina)',
  '塞尔维亚语(塞尔维亚及黑山)': 'Serbian (Serbia and Montenegro)',
  '塞尔维亚语(西里尔文)': 'Serbian (Cyrillic)',
  '塞尔维亚语(西里尔文，波斯尼亚和黑塞哥维那)': 'Serbian (Cyrillic, Bosnia and Herzegovina)',
  '塞尔维亚语(西里尔文，黑山)': 'Serbian (Cyrillic, Montenegro)',
  '塞尔维亚语(西里尔文，塞尔维亚)': 'Serbian (Cyrillic, Serbian)',
  '塞尔维亚语(西里尔文，科索沃)': 'Serbian (Cyrillic, Kosovo)',
  '塞尔维亚语(拉丁文)': 'Serbian (Latin)',
  '塞尔维亚语(拉丁文，波斯尼亚和黑塞哥维那)': 'Serbian (Latin, Bosnia and Herzegovina)',
  '塞尔维亚语(拉丁文，黑山)': 'Serbian (Latin, Montenegro)',
  '塞尔维亚语(拉丁文，塞尔维亚)': 'Serbian (Latin, Serbian)',
  '塞尔维亚语(拉丁文，科索沃)': 'Serbian (Latin, Kosovo)',
  '塞尔维亚语(黑山)': 'Serbian (Montenegro)',
  '塞尔维亚语(塞尔维亚)': 'Serbian (Serbia)',
  瑞典语: 'Swedish',
  '瑞典语(奥兰群岛)': 'Swedish (Aland Islands)',
  '瑞典语(芬兰)': 'Swedish (Finland)',
  '瑞典语(瑞典)': 'Swedish (Sweden)',
  斯瓦希里语: 'Swahili language',
  '斯瓦希里语(刚果（金）)': 'Kiswahili (DRC))',
  '斯瓦希里语(肯尼亚)': 'Kiswahili (Kenya)',
  '斯瓦希里语(坦桑尼亚)': 'Kiswahili (Tanzania)',
  '斯瓦希里语(乌干达)': 'Kiswahili (Uganda)',
  泰米尔语: 'Tamil',
  '泰米尔语(印度)': 'Tamil (India)',
  '泰米尔语(斯里兰卡)': 'Tamil (Sri Lanka)',
  '泰米尔语(马来西亚)': 'Tamil (Malaysia)',
  '泰米尔语(新加坡)': 'Tamil (Singapore)',
  泰卢固语: 'Telugu language',
  '泰卢固语(印度)': 'Telugu (India)',
  特索语: 'Teso language',
  '特索语(肯尼亚)': 'Teso (Kenya)',
  '特索语(乌干达)': 'Teso (Uganda)',
  塔吉克语: 'Tajik',
  '塔吉克语(塔吉克斯坦)': 'Tajik (Tajikistan)',
  泰语: 'Thai',
  '泰语(泰国)': 'Thai (Thailand)',
  '泰语(泰国，TH，泰文数字)': 'Thai (Thai, TH, Thai numerals)',
  提格利尼亚语: 'Tigrinian language',
  '提格利尼亚语(厄立特里亚)': 'Tiglinya (Eritrea)',
  '提格利尼亚语(埃塞俄比亚)': 'Tiglinya (Ethiopia)',
  土库曼语: 'Turkmen',
  '土库曼语(土库曼斯坦)': 'Turkmen (Turkmenistan)',
  汤加语: 'Tongan',
  '汤加语(汤加)': 'Tongan (Tonga)',
  土耳其语: 'Turkish',
  '土耳其语(塞浦路斯)': 'Turkish (Cyprus)',
  '土耳其语(土耳其)': 'Turkish (Turkey)',
  鞑靼语: 'Tatar language',
  '鞑靼语(俄罗斯)': 'Tatar (Russia)',
  北桑海语: 'North Sanghai language',
  '北桑海语(尼日尔)': 'Northern Sanghai (Niger)',
  塔马齐格特语: 'Tamazigt language',
  '塔马齐格特语(摩洛哥)': 'Tamazigote (Morocco)',
  维吾尔语: 'Uyghur',
  '维吾尔语(中国)': 'Uygur (China)',
  乌克兰语: 'Ukraine',
  '乌克兰语(乌克兰)': 'Ukraine (Ukraine)',
  乌尔都语: 'Urdu language',
  '乌尔都语(印度)': 'Urdu (India)',
  '乌尔都语(巴基斯坦)': 'Urdu (Pakistan)',
  乌兹别克语: 'Uzbek',
  '乌兹别克语(阿拉伯文)': 'Uzbek (Arabic)',
  '乌兹别克语(阿拉伯文，阿富汗)': 'Uzbek (Arabic, Afghan)',
  '乌兹别克语(西里尔文)': 'Uzbek (Cyrillic)',
  '乌兹别克语(西里尔文，乌兹别克斯坦)': 'Uzbek (Cyrillic, Uzbekistan)',
  '乌兹别克语(拉丁文)': 'Uzbek (Latin)',
  '乌兹别克语(拉丁文，乌兹别克斯坦)': 'Uzbek (Latin, Uzbekistan)',
  瓦伊语: 'Wye language',
  '瓦伊语(拉丁文)': 'Wye (Latin)',
  '瓦伊语(拉丁文，利比里亚)': 'Wye (Latin, Liberian)',
  '瓦伊语(瓦依文)': 'Wye (Wye)',
  '瓦伊语(瓦依文，利比里亚)': 'Wye (Wye, Liberia)',
  越南语: 'Vietnam',
  '越南语(越南)': 'Vietnam (Vietnam)',
  沃拉普克语: 'Warapuk language',
  '沃拉普克语(世界)': 'Warapuke (World)',
  温旧语: 'Wen Jiuyu',
  '温旧语(坦桑尼亚)': 'Wenjiu (Tanzania)',
  瓦尔瑟语: 'Walse language',
  '瓦尔瑟语(瑞士)': 'Walser (Switzerland)',
  沃洛夫语: 'Wolof, the lingua',
  '沃洛夫语(塞内加尔)': 'Vorof (Senegal)',
  索加语: 'Soga language',
  '索加语(乌干达)': 'Soga (Uganda)',
  洋卞语: 'Foreign Bian language',
  '洋卞语(喀麦隆)': 'Foreign Bian (Cameroon)',
  依地文: 'Yidi',
  '依地文(世界)': 'Yiddish (World)',
  约鲁巴语: 'Yoruba language',
  '约鲁巴语(贝宁)': 'Yoruba (Benin)',
  '约鲁巴语(尼日利亚)': 'Yoruba (Nigeria)',
  粤语: 'Cantonese',
  '粤语(简体)': 'Cantonese (Simplified)',
  '粤语(简体，中国)': 'Cantonese (Simplified, China)',
  '粤语(繁体)': 'Cantonese (Traditional)',
  '粤语(繁体，中国香港特别行政区)':
    'Cantonese (Traditional, China Hong Kong Special Administrative Region)',
  标准摩洛哥塔马塞特语: 'Standard Morocco Tamaset',
  '标准摩洛哥塔马塞特语(摩洛哥)': 'Standard Morocco Tamasset (Morocco)',
  祖鲁语: 'Zulu',
  '祖鲁语(南非)': 'Zulu (South Africa)',
  '希伯来语(以色列)': 'Hebrew (Israel)',
  未确定: 'Undetermined',
  费用中心: 'Cost centres',
  运营推广: 'Operation promotion',
  IP生产: 'IP production',
  IP生产成功: 'IP production was successful',
  IP生产失败: 'IP production failure',
  IP导入: 'IP import',
  IP移除: 'IP removal',
  IP到期: 'IP expires',
  IP过期: 'IP expired',
  IP销毁成功: 'IP destroyed successfully',
  IP即将销毁: 'IP is about to be destroyed',
  IP续费成功: 'IP renewal successful',
  IP续费失败: 'IP renewal failed',
  IP切换: 'IP handoff',
  转让账号审核: 'Transfer account review',
  账号克隆成功: 'Account cloning successfully',
  账号转让确认: 'Account transfer confirmation',
  账号转让结果: 'Account transfer results',
  账号联营确认: 'Account affiliation confirmation',
  账号联营结果: 'Account pool results',
  成员加入: 'Members to join',
  成员加入审批: 'Member joining approval',
  成员退出: 'Member exits',
  网址加白成功: 'The website was whitewashed successfully',
  网址加白失败: 'Failed to add white on the website',
  银行转账到账通知: 'Bank transfer receipt notice',
  待支付订单通知: 'Notification of pending orders',
  花瓣购买通知: 'Flower petal purchase notice',
  关键事项检查: 'Inspection of key matters',
  通用消息: 'Universal message',
  购买IP: 'Purchase of IP',
  购买流量: 'Purchase traffic',
  账户充值: 'Account recharge',
  购买花瓣: 'Buy petals',
  退款: 'Refund',
  IP续费: 'IP renewal',
  补差价: 'Make up the difference',
  购买流程任务卡: 'Purchase Process Task Card',
  流程任务卡续费: 'Process task card renewal',
  购买TikTok套餐: 'Buy TikTok Package',
  'IP+共享式主机': 'IP+ Shared Hosting',
  'IP+独享式主机': 'IP+ Exclusive Hosting',
  共享IP: 'Shared IP',
  用户自有云账号IP: 'User-owned cloud account IP',
  花瓣: 'Petals',
  流量加速: 'Flow acceleration',
  指纹实例: 'Fingerprint examples',
  存储空间: 'Storage space',
  代金券: 'Vouchers',
  公有云: 'Public cloud',
  数据中心: 'Data center',
  家庭住宅: 'Family home',
  移动网络: 'Mobile network',
  阿里云: 'Alibaba Cloud',
  腾讯云: 'Tencent Cloud',
  Google云: 'Google Cloud',
  花漾: 'Huayang',
  亚马逊中国: 'Amazon China',
  微软Azure: 'Microsoft Azure',
  百度云: 'Baidu cloud',
  华为云: 'Huawei Cloud',
  京东云: 'Jingdong cloud',
  通用: 'General',
  IP购买: 'IP purchase',
  流量购买: 'TAC',
  兴业银行股份有限公司深圳分行: 'Industrial Bank Co., Ltd. Shenzhen Branch',
  流量: 'Flow',
  高性价比: 'Competitive price.our',
  高并发: 'High concurrency',
  可远程: 'Remotely',
  美元: 'Dollars',
  鞋服箱包: 'Shoes, clothing, luggage',
  母婴玩具: 'Maternal and child toys',
  护肤美妆: 'Skin care beauty',
  电脑平板: 'Computer tablet',
  手机数码: 'Mobile phone digital',
  家电电器: 'Household appliances electrics',
  汽车配件: 'Automobile parts',
  工业用品: 'Industrial supplies',
  艺术珠宝: 'Art jewelry',
  手表眼镜: 'Watch glasses',
  户外运动: 'Outdoor sports',
  花园聚会: 'Garden party',
  生活家居: 'Life housing',
  家具建材: 'Furniture building materials',
  宠物用品: 'Pet products',
  食品生鲜: 'Food fresh',
  鲜花绿植: 'Flowers and green plants',
  医药保健: 'Medicines and health',
  计生情趣: 'Fun of family planning',
  图书文具: 'Books stationery',
  音乐影视: 'Music and film',
  软件程序: 'Software program',
  电商其他: 'E-commerce Others',
  导入IP: 'Import IP',
  导入账号: 'Import account',
  上传文件: 'Upload files',
  马来西亚语: 'Bahasa Malaysia',
  印尼语: 'Indonesian',
  '该账号似乎是一个“新”账号': 'The account appears to be a "new" account',
  '该账号没有任何Cookie数据，这意味着从来没有访问过此账号，也没有维护此账号的登录态，您需要先打开花漾浏览器，登录此账号并维护好登录态后再进行操作,':
    'This account does not have any Cookie data, which means that this account has never been visited, and the login status of this account has not been maintained. You need to open Huayang browser first, log in to this account, and maintain the login status before operating.',
  继续执行: 'Continue to implement',
  '该账号没有任何Cookie数据，用户取消执行操作':
    'This account does not have any Cookie data, and the user cancels the operation',
  按住: 'Hold ',
  可多选: ' to select multiple',
  该账号所在手机已离线: 'The mobile phone where this account is located is offline',
  该手机已离线: 'The phone is offline',
  '无法连接到服务器，请检查您的网络是否通畅，是否开启了全局代理。':
    'Unable to connect to the server. Please check whether your network is smooth and whether the global proxy is turned on.',
  系统维护: 'System maintenance',
  错误: 'Error',
  '无法连接服务器，请检查您的网络是否通畅。':
    'Unable to connect to the server. Please check whether your network is smooth.',
  网络错误: 'Network error',
  操作成功: 'Operation is successful',
  打开手机查看详情: 'Open your mobile phone to view details',
  无可用KOL模块: 'No KOL modules available',
  支付失败: 'Payment failure',
  充值: 'Recharge',
  余额支付: 'Balance payment',
  银行转账: 'Bank transfer',
  已创建: 'Has been created',
  已锁定: 'Is locked',
  待确认: 'To be confirmed',
  已支付: 'Paid',
  已取消: 'Cancelled',
  已退款: 'Refunded',
  个人普通发票: 'Individual ordinary invoice',
  企业增值税普通发票: 'Enterprise VAT Ordinary Invoice',
  不支持的发票类型: 'Unsupported invoice types',
  企业增值税专用发票: 'Enterprise VAT special invoice',
  已申请: 'Has applied for',
  已开票: 'Invoiced',
  未开票: 'Unbilled',
  生产成功: 'Production success',
  未开始: 'Not started',
  生产失败: 'Production failure',
  生产中: 'In the production',
  消耗花瓣: 'Consuming petals',
  初始赠送: 'Initial gift',
  赠送花瓣: 'Give petals',
  花瓣转入: 'Petal transfer',
  订单退款: 'Order refund',
  礼品卡激活: 'Gift card activation',
  RPA市场流程: 'RPA Market Process',
  RPA发送邮件: 'RPA sends mail',
  RPA发送微信模板消息: 'RPA sends WeChat template message',
  RPA发送短信: 'RPA sends text messages',
  流水号: 'Serial number',
  消费类型: 'Consumption type',
  消费时间: 'Consumption time',
  关联订单: 'Associated order',
  消费金额: 'Consumption amount',
  消费后余额: 'Post-consumption balance',
  '订单支付明细：': 'Order payment details:',
  '收支流水【': 'Revenue and expenditure [',
  '】详情': '] Details',
  交易类型: 'Transaction type',
  交易内容: 'Transaction content',
  支付方式: 'Payment methods',
  交易金额: 'Transaction amount',
  账户余额: 'Account balance',
  交易时间: 'Trading hours',
  开始时间: 'Start time',
  结束时间: 'End time',
  操作: 'Action',
  详情: 'Details',
  激活: 'Activation',
  验证: 'Verification',
  代金券面值: 'Voucher face value',
  有效期至: 'Valid until',
  '（限用一次）': '(Restricted to one time)',
  激活新的代金券: 'Activate new voucher',
  代金券卡号: 'Voucher card number',
  请输入代金券卡号: 'Please enter the voucher card number',
  代金券卡密: 'Voucher card secret',
  请输入代金券卡密: 'Please enter the voucher password',
  面值: 'Face value',
  '，最小可用': ', minimum available',
  余额: 'Balance',
  '（0次可用）': '(0 times available)',
  适用产品: 'Applicable product',
  立即使用: 'Used immediately',
  可用代金券: 'Available vouchers',
  暂无可用的代金券: 'No vouchers are available',
  不可用代金券: 'Voucher not available',
  暂无不可用的代金券: 'There are no unavailable vouchers',
  选择代金券: 'Select voucher',
  '代金券一旦删除，不可恢复，请确认':
    'Once the voucher is deleted, it cannot be restored. Please confirm',
  没有找到相关的代金券: 'No relevant vouchers were found',
  当前团队没有代金券: 'Current team does not have vouchers',
  如何获得代金券: 'How to get vouchers',
  当前可用: 'Currently available',
  即将过期: 'About to expire',
  已失效: 'Lapsed',
  已消费: 'Has consumed',
  连接方式: 'Connection mode',
  直连: 'Direct-connect',
  客户端IP: 'Client IP',
  '针对海外IP可选择是否开启加速通道，以及在加速通道之间的切换策略':
    'For overseas IP, you can choose whether to open acceleration channels and switch strategies between acceleration channels',
  '自动选择加速通道：': 'Automatic selection of acceleration channels:',
  所有免费加速通道: 'All free acceleration channels',
  所有收费加速通道: 'All toll acceleration channels',
  '指定以下加速通道：': 'Specify the following acceleration channels:',
  可针对不同连接方式下进行网络质量测试:
    'Network quality testing can be carried out for different connection methods',
  网络测试: 'Network test',
  设置IP的加速通道: 'Set up IP acceleration channels',
  选择所有免费的加速通道: 'Select all free acceleration channels',
  选择所有收费的加速通道: 'Select all charging acceleration channels',
  加速通道: 'Acceleration channel',
  网络性质: 'Network properties',
  客户端直连IP: 'Client is directly connected to IP',
  免费加速通道: 'Free acceleration channel',
  VIP加速通道: 'VIP acceleration channel',
  选择入口位置为境内的加速通道:
    'Select the entrance location as the acceleration channel within the territory',
  选择入口位置为海外的加速通道: 'Select the entrance location as an overseas acceleration channel',
  入口位置: 'Entry position',
  出口位置: 'Exit position',
  '当采用直连时，不会产生任何额外的花瓣支出；当采用接入点连接时，会根据不同的连接方式拥有不同的收费标准':
    'When using direct connections, no additional petal expenses will be incurred; when using access point connections, different charging standards will be imposed based on different connection methods',
  收费标准: 'Charging standards',
  '花瓣/GB': 'Petals/GB',
  连接方式切换策略: 'Connection mode switching strategy',
  '默认采用直连，只有当直连不通时才会在当前允许的连接方式内，按照收费标准由低到高依次尝试，除非连接不通才会切换下一个连接方式':
    'Direct connection is adopted by default. Only when the direct connection fails will the current allowed connection methods be tried from low to high according to the charging standard. Unless the connection fails, the next connection method will be switched',
  直连优先: 'Direct connection priority',
  '在当前允许的连接方式内，会按照连接质量的优劣自动选择最佳的连接方式':
    'Among the currently allowed connection methods, the best connection method will be automatically selected based on the connection quality',
  质量优先: 'Quality priority',
  '运行脚本并安装“花漾IPGO”': 'Run the script and install "Huayang IPGO"',
  '1、必须以管理员administrator身份执行上述脚本':
    '1. The above script must be executed as an administrator',
  '2、您也可以切换至图形界面安装形态':
    '2. You can also switch to the graphical interface installation mode',
  立即切换: 'Immediately switch',
  '安装遇到问题？': 'Problems with installation?',
  '请在开始菜单中运行“CMD”程序，在弹出的命令行窗口中运行以下脚本':
    'Please run the "CMD" program in the Start menu and run the following script in the pop-up command line window',
  '请确保VPS/云主机可正常访问公网，点击了解':
    'Please ensure that the VPS/cloud host can access the public network normally, click Learn',
  'VPS/云主机安全组规则设置参考': 'VPS/Cloud Host Security Group Rule Setting Reference',
  复制脚本: 'Replication scripts',
  验证导入的IP地址: 'Verify the imported IP address',
  '安装完成后，您可在命令行窗口中输入命令“ips”查看VPS/云主机的公网IP是否已经成功导入':
    'After the installation is complete, you can enter the command "ips" in the command line window to check whether the public IP of the VPS/cloud host has been successfully imported',
  '安装“花漾IPGO”': 'Install "Huayang IPGO"',
  '1、必须以管理员administrator身份执行安装程序':
    '1. You must execute the installation program as an administrator',
  '2、您也可以切换至命令行界面安装形态':
    '2. You can also switch to the command line interface installation mode',
  '请在目标VPS/云主机中下载“花漾IPGO安装包”，解压缩后双击“install.cmd”以运行安装程序。由于花漾IPGO需要修改系统配置，会被360安全卫士、腾讯电脑管家等软件误拦截，安装前请将这类安全软件退出':
    'Please download the "Huayang IPGO Installation Package" in the target VPS/cloud host, decompress it, and double-click "install.cmd" to run the installer. Since Huayang IPGO needs to modify the system configuration, it will be mistakenly intercepted by 360 Security Guards, Tencent Computer Butler and other software. Please exit such security software before installation',
  复制下载链接: 'Copy the download link',
  关联团队: 'Related Team',
  '打开程序后，输入您的团队专属桥接码，以让该程序与您的团队进行关联':
    'After opening the program, enter your team-specific bridging code to associate the program with your team',
  '安装完成后，去"IP地址"菜单观察IPGO的IP是否导入，如果没有出现，点击刷新':
    'After the installation is complete, go to the "IP Address" menu to see if the IP of IPGO is imported. If it does not appear, click Refresh',
  '1、必须以管理员root身份执行上述脚本':
    '1. The above script must be executed as administrator root',
  '2、请设置安全组规则，允许主机访问外网':
    '2. Please set security group rules to allow the host to access the external network',
  '复制花漾IPGO安装脚本，并在目标VPS/云主机上以管理员身份执行脚本并安装“花漾IPGO”':
    'Copy the Huayang IPGO installation script, execute the script as an administrator on the target VPS/cloud host and install "Huayang IPGO"',
  '如果您的VPS/云主机已经开启了SSH协议（一般而言，所有的Linux主机均会默认开启SSH协议），也可以通过SSH协议将其公网IP导入至花漾，这种方式最为简单（不用安装任何软件）':
    'If your VPS/cloud host has enabled the SSH protocol (generally speaking, all Linux hosts will enable the SSH protocol by default), you can also import its public IP to Huayang through the SSH protocol. This is the easiest method (no need to install any software)',
  打开SSH协议导入向导: 'Open the SSH Protocol Import Wizard',
  '导入已有VPS/云主机的静态IP地址': 'Import static IP addresses of existing VPS/cloud hosts',
  '如果您已经拥有VPS/云主机，只需在您的VPS/云主机中安装“花漾IPGO”服务程序，即可将VPS/云主机的公网IP导入到花漾IP池中；请根据您的VPS/云主机的操作系统而选择不同的安装脚本':
    'If you already own a VPS/cloud host, just install the "Huayang IPGO" service program in your VPS/cloud host to import the public IP of the VPS/cloud host into the Huayang IP pool; please choose a different installation script based on your VPS/cloud host operating system',
  'VPS/云主机操作系统': 'VPS/Cloud Host Operating System',
  IPGO安装步骤: 'IPGO installation steps',
  查看更多IP服务商: 'See more IP service providers',
  购买国内家庭住宅IP请联络在线客服:
    'To purchase domestic family residential IP, please contact online customer service',
  请和在线客服联络: 'Please contact online customer service',
  用于店铺运营: 'Used for store operations',
  '如果您有Amazon、eBay、Shopee、TikTok小店、Lazada、Etsy等店铺需要长期运营，可以购买花漾为您提供的公有云主机，内含静态、独享的IP地址':
    'If you have Amazon, eBay, Shopee, TikTok Store, Lazada, Etsy and other stores that need long-term operations, you can purchase the public cloud host provided by Huayang, which contains a static and exclusive IP address',
  国内家庭住宅IP: 'Domestic Family Residential IP',
  '国内家庭住宅IP主要用来访问国内的站点，如抖音、小红书、快手、网游等用途':
    'Domestic family residential IP is mainly used to access domestic sites, such as Douyin, Xiaohongshu, Fast Hand, online games and other purposes',
  购买IP资源: 'Purchase IP resources',
  IP值检测失败: 'IP value detection failed',
  正在为您检测IP最新值: 'Checking the latest IP values for you',
  '您当前输入的IP修订值和检测值并不相符，确认要保存吗？':
    'The IP revision value you are currently entering does not match the test value. Are you sure you want to save it?',
  IP当前值: 'Current IP value',
  IP检测值: 'IP test value',
  IP修订值: 'IP revision value',
  请输入IP地址: 'Please enter IP address',
  '静态IP的值一般而言是固定不变的，请确认是否要更新此IP的值':
    'Generally speaking, the value of static IP is fixed. Please confirm whether you want to update the value of this IP',
  购买的公有云主机的出口IP值不会发生变化:
    'The export IP value of purchased public cloud hosts will not change',
  '公有云主机内置的是静态、独享的IP，其出口IP不会发生变化':
    'The built-in static and exclusive IP of public cloud hosts will not change',
  处于转让状态的IP不能删除: 'IP in transfer status cannot be deleted',
  平台购买的IP不可以删除: 'IP purchased by the platform cannot be deleted',
  平台购买的IP到期后会自动删除:
    'IP purchased by the platform will be automatically deleted after expiration',
  处于准备状态的IP不能删除: 'IP in preparation cannot be deleted',
  '确定要删除当前IP吗?': 'Are you sure you want to delete the current IP?',
  '确定要删除选择的IP吗?': 'Are you sure you want to delete the selected IP?',
  '自有IP一旦删除，将自动切断所有绑定此IP的分身的绑定关系，除非用户手动恢复':
    'Once an own IP is deleted, all binding relationships of browsers bound to this IP will be automatically severed unless the user manually restores it',
  正在删除: 'Deleting',
  删除成功: 'Deleted successfully',
  '当前IP转让中，无法继续操作': 'The current IP transfer is underway and cannot continue',
  '为IP“{{ip}}”绑定分身': 'Bind browser to IP "{{ip}}"',
  绑定成功: 'Binding success',
  自有IP: 'Own IP',
  平台IP: 'Platform IP',
  正在更新: 'Is updating',
  '接入点...': 'Access point...',
  批量设置IP的加速通道: 'Batch setting up IP acceleration channels',
  批量设置平台IP的加速通道: 'Batch setting up acceleration channels for platform IP',
  批量设置自有IP的加速通道: 'Set up acceleration channels for your own IP in batches',
  '当前选中的既有自有IP，也有平台IP，需要提醒您的是，针对自有IP和平台IP设置的加速通道是不同的，您需要分别设置':
    'Currently, there are both private IPs and platform IPs selected. I need to remind you that the acceleration channels set for private IPs and platform IPs are different, and you need to set them separately.',
  不支持在网页下进行IP质量测试: 'IP quality testing under web pages is not supported',
  只有在花漾客户端中才能够进行IP质量测试:
    'IP quality testing can only be performed in Huayang client',
  只有动态IP才支持手动刷新: 'Only dynamic IP supports manual refresh',
  '手动刷新是指更新动态IP的出口IP，是由IP服务商提供的私有特性':
    'Manual refresh refers to updating the egress IP of dynamic IP and is a private feature provided by the IP service provider',
  该动态IP不支持手动刷新: 'This dynamic IP does not support manual refresh',
  '手动刷新是由IP服务商提供的特性，您需要在IP属性中设置手动刷新的URL，访问此URL时会更新动态IP的出口IP':
    'Manual refresh is a feature provided by the IP service provider. You need to set the URL for manual refresh in the IP properties. When you access this URL, the egress IP of the dynamic IP will be updated',
  '确定要更该换动态IP的出口IP吗？':
    'Are you sure you want to change the outbound IP to the dynamic IP?',
  '当访问以下URL时，会更换该动态IP的出口IP':
    'When accessing the following URL, the egress IP of this dynamic IP will be changed',
  '正在为您手动刷新该动态IP的出口IP...':
    'The exit IP of this dynamic IP is being manually refreshed for you...',
  已发送手动刷新的请求: 'Request for manual refresh sent',
  手动刷新的请求发送失败: 'Manual refresh request failed to send',
  批量设置自动续费: 'Batch settings for automatic renewal',
  'IP自动续费时，需从账户余额中扣除款项，请确保您的账户中余额充足':
    'When you automatically renew IP, you need to deduct money from your account balance. Please ensure that your account has a sufficient balance.',
  自动续费策略: 'Automatic renewal policy',
  开启: 'On',
  IP列表: 'IP list',
  费用: 'Costs',
  '元/': 'Yuan/',
  自动续费: 'Automatic renewal',
  支出成本预估: 'Expenditure cost estimates',
  '元/续费周期': 'Yuan/renewal cycle',
  名称: 'Name',
  'IP数量：': 'Number of IPs:',
  '加载中...': 'Loading...',
  手动填写IP: 'Manually fill in IP',
  IP标签管理: 'IP tag management',
  IP池标签管理: 'IP pool tag management',
  您还没有IP资源: "You don't have IP resources yet",
  设置全部IP的加速通道: 'Set up acceleration channels for all IPs',
  是否要对所有的: 'Do you want to respond to all',
  '设置加速通道？': 'Set up acceleration channels?',
  '根据IP数量的不同，这可能会是一个非常耗时的操作，请确认是否继续':
    'Depending on the number of IPs, this may be a very time-consuming operation. Please confirm whether to continue',
  网络加速设置: 'Network acceleration settings',
  您还有未结束的全量测速任务: 'You still have unfinished full-scale speed measurement tasks',
  '是否要对所有的IP进行质量测试？': 'Should I conduct quality testing on all IPs?',
  '{{count}}个IP的质量测试已完成': 'Quality testing for {{count}} IPs has been completed',
  请直接在IP列表或详情中查看每个IP的测速结果:
    'Please view the speed measurement results of each IP directly in the IP list or details',
  '正在对{{count}}个IP进行质量测试，已完成{{percent}}%':
    'Quality testing is underway on {{count}} IPs,{{percent}}% completed',
  '正在对{{count}}个IP进行质量测试，请稍候...':
    'Quality testing is underway on {{count}} IPs, please wait...',
  '隐藏至后台运行会形成一笔任务，可在任务中查看执行细节':
    'Hiding it into the background will form a task, where execution details can be viewed',
  隐藏至后台执行: 'Hide to background execution',
  IP测速: 'IP speed measurement',
  质量测试: 'Quality test',
  全量操作: 'Full operation',
  不限归属地: 'Unlimited place',
  IP纯净度检查: 'IP purity check',
  时间: 'Time',
  最近1个月: 'Last month',
  最近3个月: 'Last 3 months',
  最近6个月: 'Last 6 months',
  检索: 'Retrieval',
  没有找到相匹配的访问记录: 'No matching access records were found',
  访问者: 'Visitors',
  当前团队: 'Current team',
  其他团队: 'Other team',
  访问时间: 'Access time',
  什么是IP纯净度检查: 'What is IP purity check',
  '1、IP纯净度检查是指花漾依据自己的大数据系统，对指定IP在指定时间周期内访问过的所有网站的罗列，它能够比较有效的反应一个IP的纯净度':
    "1. IP purity check refers to Huayong's list of all websites visited by a specified IP within a specified time period based on its own big data system. It can effectively reflect the purity of an IP.",
  '2、凡是在花漾内购买的平台IP，均被纳入到花漾的IP监测大数据系统；不是在花漾内购买的IP，自导入花漾后也将被纳入到花漾的IP监测大数据系统':
    "2. All platform IPs purchased in Huayang will be included in Huayang's IP monitoring big data system; IPs not purchased in Huayang will also be included in Huayang's IP monitoring big data system after being imported into Huayang. system",
  '3、出于信息安全与数据隐私的考虑，针对某个网站的访问，我们仅会记录访问的域名与访问者归属的组织与时间，并不会记录具体的访问路径，更不会存储任何会话数据':
    '3. For information security and data privacy considerations, for visits to a certain website, we will only record the domain name visited and the organization and time to which the visitor belongs. We will not record the specific access path, let alone store any session data.',
  出口IP: 'Egress IP',
  归属地: 'Attribution',
  语言: 'Language',
  时区: 'Time zone',
  经纬度: 'Latitude and longitude',
  查询地理位置: 'Query geographical location',
  IP来源: 'IP source',
  续费设置: 'Renewal settings',
  地理位置: 'Geographical location',
  IP属性: 'IP properties',
  请输入备注: 'Please enter a comment',
  请输入名称: 'Please enter a name',
  '最少4个字符，最长128个字符': 'Minimum 4 characters, maximum 128 characters',
  网络类型: 'Network type',
  使用范围: 'Scope of use',
  备注: 'Remarks',
  指定经纬度: 'Designated latitude and longitude',
  经纬度格式不合法: 'Illegal latitude and longitude format',
  '经度在前,纬度在后,英文逗号分隔':
    'Longitude is in front, latitude is in back, separated by English commas',
  百度: 'Baidu',
  谷歌: 'Google',
  拾取坐标: 'Pick up coordinates',
  禁用经纬度: 'Disable latitude and longitude',
  禁用: 'Disable',
  未设置: 'Not set',
  '确定要更改IP归属地吗？': 'Are you sure you want to change your IP home?',
  'IP归属地将影响到所在时区与默认语言，并影响到绑定在同一分身上的浏览器指纹实例的动态属性”语言“、”经纬度“与”时区“，请确认您要做IP归属地的修订':
    'The IP affiliation will affect the time zone and default language, as well as the dynamic attributes of "language","latitude and longitude" and "time zone" of the browser fingerprint instance bound to the same browser. Please confirm that you want to make the IP affiliation revision.',
  同步更改时区设置: 'Synchronously change time zone settings',
  同步更改语言设置: 'Sync changes language settings',
  同步更改经纬度: 'Synchronously change latitude and longitude',
  '确定要更改IP的时区吗？': 'Are you sure you want to change the IP time zone?',
  '一般而言，IP的时区是跟归属地紧密相关的，请确认是否要继续修订时区属性':
    'Generally speaking, the time zone of an IP is closely related to the home place. Please confirm whether you want to continue to revise the time zone attribute',
  '确定要更改IP的语言吗？': 'Are you sure you want to change the language of the IP?',
  '一般而言，IP的语言是跟归属地紧密相关的，请确认是否要继续修订语言属性':
    'Generally speaking, the language of the IP is closely related to the place of origin. Please confirm whether you want to continue to revise the language attributes',
  '确定要更改IP的经纬度吗？':
    'Are you sure you want to change the latitude and longitude of the IP?',
  '一般而言，IP的经纬度是跟归属地紧密相关的，请确认是否要继续修订经纬度属性':
    'Generally speaking, the latitude and longitude of the IP is closely related to the home place. Please confirm whether you want to continue to revise the latitude and longitude attributes',
  'IP的地理位置来源于花漾的“IP地理位置库”，受限于IP地理位置库的精度，系统无法保证每个IP归属地的准确性，如发觉存在较大出入，可自行指定IP的地理位置，请注意，这并不会影响到所绑分身的安全性':
    'The geographical location of the IP comes from Huayang\'s "IP Geolocation Library". Due to the accuracy of the IP Geolocation Library, the system cannot guarantee the accuracy of each IP\'s affiliation. If there is a large discrepancy, you can specify the geographical location of the IP yourself. Please note that this will not affect the security of the browser you bind.',
  请选择时区: 'Please select a time zone',
  请选择语言: 'Please select Language',
  来源: 'Source',
  操作系统: 'Operating system',
  核: 'C',
  实例规格: 'Instance specifications',
  商品类别: 'Commodity categories',
  云厂商: 'Cloud vendors',
  协议: 'Agreement',
  商品名称: 'Product name',
  供货商: 'Supplier',
  代理地址: 'Proxy address',
  请输入代理地址: 'Please enter the agent address',
  密码: 'Password',
  密钥: 'Key',
  认证方式: 'Authentication method',
  端口号: 'Port number',
  请输入端口号: 'Please enter port number',
  端口号必须为数字: 'Port number must be a number',
  不适用: 'Not apply',
  流量配额: 'Traffic quota',
  超额流量单价: 'Unit price of excess flow',
  登录密码: 'Login password',
  密钥文件: 'Key file',
  登录账号: 'Login account',
  静态: 'Static',
  动态: 'Dynamic',
  'IP服务商提供的特性，访问此URL时会更新出口IP（选填）':
    'Features provided by the IP service provider. When accessing this URL, the egress IP will be updated (optional)',
  性质: 'Nature',
  供应商: 'Suppliers',
  会话保持: 'Session persistence',
  '会话保持是指能够通过参数来控制IP值的动态切换，需要供应商明确是否支持以及支持的方式':
    'Session retention refers to the ability to control dynamic switching of IP values through parameters, and requires the supplier to clarify whether it supports it and how it supports it.',
  支持: 'Support',
  不支持: 'Not support',
  访问此URL时会更新出口IP: 'The exit IP is updated when accessing this URL',
  手动刷新: 'Manually refresh',
  正在获取商品价格: 'Getting product prices',
  周: 'Weeks',
  '确定要由按周续费变更为按月续费吗？':
    'Are you sure you want to change from weekly renewal to monthly renewal?',
  按周费用: 'Weekly fee',
  '/周': '/week',
  按月费用: 'Monthly fee',
  '/月': '/month',
  用户取消操作: 'User cancels operation',
  暂不支持变更续费周期: 'Changing the renewal cycle is not currently supported',
  '当前IP续费周期为月，暂不支持变更续费周期':
    'The current IP renewal cycle is monthly, and changing the renewal cycle is not supported for the time being',
  '自有IP导入至花漾且采用直连方式时不收取任何费用，但如果采用花漾接入点连接并产生了加速流量，会按照实际产生的加速流量扣除花瓣':
    'There is no charge when importing your own IP to Huayang and using a direct connection method. However, if you use Huayang access point to connect and generate accelerated traffic, petals will be deducted based on the actual accelerated traffic generated.',
  '计价周期为“周”的公有云主机，可以变更续费周期为“月”从而节省成本；一旦开启自动续费请确保账户余额充足':
    'For public cloud hosts with a pricing cycle of "weeks", the renewal cycle can be changed to "months" to save costs; once automatic renewal is enabled, please ensure that the account balance is sufficient',
  变更续费周期: 'Change renewal cycle',
  无: 'No',
  永久免费: 'Permanent free',
  '0元': 'RMB0',
  计价周期: 'Pricing cycle',
  列表价格: 'List price',
  购买时间: 'Purchase time',
  到期时间: 'Expiration time',
  局域网: 'Local area network',
  系统代理: 'System agent',
  本机IP直连: 'Local IP direct connection',
  '网络类型：': 'Network type:',
  '选中的分身已绑定IP或开启了本地代理，确定要继续吗？':
    'The selected browser has an IP bound or a local agent has been turned on. Are you sure you want to continue?',
  '选中的分身中存在已经绑定IP地址或开启本地代理的情况，请确认是否继续':
    'The selected browser has an IP address bound or a local agent is enabled. Please confirm whether to continue',
  '将同一个{{type}}绑定在多个分身上请注意是否会引起分身关联':
    'If you bind the same {{type}} to multiple browsers, please note whether it will cause browsers to be associated',
  '我们不建议您将同一个IP同时绑定到多个{{type}}分身':
    'We do not recommend that you bind the same IP to multiple {{type}} browsers at the same time',
  当前IP已经绑定在此分身: 'The current IP is already bound to this browser',
  联营分身: 'Joint venture',
  客户端直连: 'Client direct connection',
  '花漾为您提供的免费加速通道，由若干加速线路组成':
    'The free acceleration channel provided by Huayang consists of several acceleration lines',
  '花漾为您提供的VIP收费加速通道，会根据实际消耗的流量扣除一定数量的花瓣':
    'The VIP charging acceleration channel provided by Huayang will deduct a certain number of petals based on the actual traffic consumed',
  '当前团队自有 IP 不支持网络加速设置':
    "The current team's own IP does not support network acceleration settings",
  '允许当前IP使用以下连接方式：': 'Allow the current IP to use the following connection methods:',
  通过花漾客户端直连IP代理: 'Directly connect the IP proxy through Huayang client',
  '当前客户端IP为：': 'The current client IP is:',
  'IP连接方式的切换策略：': 'Switching strategy for IP connection method:',
  '默认由客户端直接连接IP，只有当直连不通时才会在当前允许的连接方式内，按照收费标准由低到高依次尝试':
    'By default, the client connects directly to the IP. Only when the direct connection fails will the client try from low to high within the currently allowed connection methods according to the charging standard',
  自有IP加速通道: 'Own IP acceleration channel',
  平台IP加速通道: 'Platform IP acceleration channel',
  '正在获取最新的加速通道信息...': 'Getting the latest acceleration channel information...',
  '针对海外IP可选择是否开启加速通道，以及连接方式的切换策略':
    'For overseas IPs, you can choose whether to turn on the acceleration channel and switch strategies for connection methods',
  '如需使用海外IP加速通道，请和在线客服联系':
    'If you need to use overseas IP acceleration channels, please contact online customer service',
  '正在更新IP接入点...': 'Updating IP access points...',
  '只有选中的加速通道才会访问你的IP（适用于IP白名单）：':
    'Only the selected acceleration channel will access your IP (applies to IP whitelist):',
  类型: 'Type',
  平台IP无需设置白名单: 'Platform IP does not need to be whitelisted',
  '如果您的IP服务商需要设置IP白名单，或者您希望指定具体使用哪些线路访问您的代理IP，请点击此处设置':
    'If your IP service provider needs to set up an IP whitelist, or if you want to specify which lines to use to access your agent IP, please click here to set up',
  立即设置: 'Immediately set',
  可将上述IP加入到IP服务商的白名单:
    'The above IP can be added to the whitelist of IP service providers',
  重新设置: 'Reset',
  当前IP尚未绑定分身: 'The current IP is not bound to the browser yet',
  立即绑定: 'Binding now',
  当前分身: 'Current browser',
  当前未绑定: 'Currently not bound',
  历史绑定: 'Historical binding',
  次: 'Time',
  分身绑定状态: 'Browser binding status',
  分钟: ' Minutes',
  秒: ' Seconds',
  天: ' Days',
  年: ' Years',
  小时: ' Hours',
  已: 'Has',
  未: 'Not',
  开启自动续费: 'Enable auto renewal',
  到期: 'Expiration',
  长期有效: 'Long-term valid',
  成本: 'Cost',
  IP价格: 'IP price',
  持有时长: 'Holding duration',
  已超额: 'Excess',
  已用: 'Used',
  网络质量: 'Network quality',
  访问质量: 'Access quality',
  已用流量: 'Used traffic',
  '错误日志：': 'Error Log:',
  代理服务连接失败: 'Proxy service connection failed',
  没有可用的连接信息: 'No connection information available',
  花漾客户端所在位置: 'Location of Huayang client',
  '出口IP：': 'Export IP:',
  '地理位置：': 'Geographical location:',
  不支持测速: 'Speed measurement is not supported',
  错误日志: 'Error log',
  花漾灵动接入点: 'Huayang Smart Access Point',
  '域名：': 'Domain name:',
  '位置：': 'Location:',
  '正在寻找可用接入点...': 'Looking for available access points...',
  未获取到IP详情信息: 'IP details not obtained',
  正在进行质量测试: 'Quality testing is underway',
  '当前IP正处于准备状态，请稍候...': 'The current IP is in ready state, please wait...',
  IPGO已停止: 'IPGO has stopped',
  IPGO已被卸载: 'IPGO has been uninstalled',
  IP状态异常: 'IP status is abnormal',
  IPGO运行正常: 'IPGO is operating normally',
  IP状态正常: 'IP status is normal',
  代理协议连接异常: 'Proxy protocol connection exception',
  代理协议检测正常: 'Proxy protocol detection normal',
  只能在IP允许的连接方式内进行质量测试:
    'Quality testing can only be performed within the connection methods allowed by IP',
  更改IP连接方式: 'Change IP connection method',
  暂不支持: 'Does not support',
  '新版本特性，当前版本暂未支持，请检查更新':
    'Features of new version. The current version is not supported yet. Please check for updates.',
  访问质量测试: 'Access quality testing',
  '共{{count}}个分身': 'Total of {{count}} browsers',
  解绑: 'Unbind',
  至今: 'So far',
  绑定: 'Binding',
  绑定时间: 'Binding time',
  访问次数: 'Number of visits',
  解绑时间: 'Unbinding time',
  未解绑: 'Not untied',
  查看分身详情: 'View details of browser',
  次访问: 'Visit',
  间隔: 'Interval',
  绑定新的分身: 'Bind a new browser',
  'IP流量包含自身流量以及通过加速通道产生的加速流量，可在IP流量日志中查看每一笔流量的细节':
    'IP traffic includes its own traffic and accelerated traffic generated through the accelerated channel. You can view the details of each traffic in the IP traffic log',
  '自有IP不受流量配额的限制，只有当自有IP通过接入点连接产生加速流量时才会扣除花瓣':
    'Own IPs are not limited by traffic quotas, and petals will be deducted only when their own IPs generate accelerated traffic through access point connections.',
  '商品规格为“高性价比”的平台IP在购买周期内可免费使用的流量称之为流量配额，超过配额时将按照实际超额流量扣除花瓣':
    'The traffic that can be used for free during the purchase cycle by platform IPs with product specifications of "high cost performance" is called the traffic quota. When the quota is exceeded, petals will be deducted according to the actual excess traffic.',
  '商品规格为“企业级”的平台IP在购买周期内不限制流量，但当超过一定流量额度时，可能会产生网络限速':
    'Platform IPs with product specifications of "enterprise-level" do not restrict traffic during the purchase cycle, but when a certain traffic limit is exceeded, network speed limits may occur.',
  超额流量: 'Excess traffic',
  接入点加速流量: 'Access points accelerate traffic',
  加速通道流量: 'Accelerate channel flow',
  配额内已用流量: 'Used traffic within quota',
  IP流量: 'IP traffic',
  公有IP: 'Public IP',
  类别: 'Category',
  会话打开失败: 'Session opening failed',
  '请打开SSH终端，并输入以下命令：':
    'Please open the SSH terminal and enter the following command:',
  脚本已复制到剪切板: 'Script copied to clipboard',
  请妥善保管您的主机密码: 'Please keep your host password safe',
  '我们需要您的私钥文件，才能够帮您获取到主机密码：':
    'We need your private key file to help you get the host password:',
  请输入私钥: 'Please enter private key',
  可直接将私钥文件粘贴在此: 'You can paste the private key file directly here',
  无密码: 'No password',
  '主机尚未就绪，请稍后再获取密码': 'The host is not ready yet. Please get the password later.',
  获取密码: 'Obtain the password',
  用户名: 'User name',
  '私钥文件已下载，请妥善保存': 'The private key file has been downloaded, please save it properly',
  '通过平台购买的独享主机，共享同一份密钥对，因此，下载后的密钥文件请您妥善保存,':
    'Exclusive hosts purchased through the platform share the same key pair. Therefore, please save the downloaded key file properly.',
  '下载没有开始？': "The download didn't start?",
  重新下载: 'Re-download',
  下载私钥文件: 'Download private key file',
  下载远程桌面文件: 'Download remote desktop files',
  '由于Mac系统限制,需要您手动输入密码':
    'Due to Mac system limitations, you need to enter your password manually',
  我知道了: 'I see',
  远程访问: 'Remote access',
  请在花漾客户端中访问: 'Please visit in Huayang client',
  立即访问: 'Immediate access to',
  '远程访问VPS/云主机': 'Remote access to VPS/cloud hosting',
  IP地址: 'IP address',
  '关于远程访问VPS/云主机': 'About remote access to VPS/cloud hosting',
  '1、您购买的平台IP本质上是绑定在VPS/云主机之上的IP地址':
    '1. The platform IP you purchase is essentially the IP address bound to the VPS/cloud host',
  '2、为保证IP地址的独享性与纯净性，这台VPS/云主机只归您所独有，同时，我们强烈建议您长期持有这台云主机，以避免由于VPS/云主机被释放而导致绑定的IP地址失效':
    '2. In order to ensure the exclusiveness and purity of the IP address, this VPS/cloud host is exclusive to you. At the same time, we strongly recommend that you hold this cloud host for a long time to avoid the binding IP address being invalid due to the release of the VPS/cloud host.',
  '3、在这台云主机中安装了我们为您提供的IP代理软件“花漾IPGO”，请注意不要轻易卸载或停止IPGO服务':
    '3. The IP proxy software "Huayang IPGO" we provide for you is installed in this cloud host. Please be careful not to uninstall or stop the IPGO service easily',
  '4、我们为您提供这台云主机的用户名和密码，您可以远程登录这台云主机':
    '4. We will provide you with the username and password of this cloud host. You can log in to this cloud host remotely',
  处于准备或转让状态的IP不能删除:
    'IPs that are in preparation or transfer status cannot be deleted',
  '当前IP正处于准备中或者转让中的状态，无法删除':
    'The current IP is in preparation or transfer state and cannot be deleted',
  '购买的平台IP不允许删除，到期后会自动回收；如果希望长期持有此IP，请注意开启自动续费功能，并确保拥有充足的账户余额':
    'The purchased platform IP is not allowed to be deleted and will be automatically recycled after expiration; if you want to hold this IP for a long time, please note that you can turn on the automatic renewal function and ensure you have sufficient account balance',
  更换出口IP: 'Replace export IP',
  纯净度: 'Purity',
  知道: 'Know',
  自有IP无需续费: 'Own IP without renewal',
  '如果自有IP采用接入点连接方式，会产生网络加速流量从而带来花瓣的消耗，因此，请注意保证团队的花瓣余额充足':
    'If your own IP uses access point connection, it will generate network acceleration traffic and consume petals. Therefore, please pay attention to ensure that the team has sufficient petals balance.',
  '该IP正在转让给其它团队，无法执行任何操作':
    'The IP is being transferred to other teams and no operation can be performed',
  续费: 'To renew your',
  绑定分身: 'Binding browser',
  网络加速: 'Network acceleration',
  'IP描述：--': 'IP description: --',
  操作轨迹: 'Operational trajectories',
  将本IP导入到平台: 'Import this IP to the platform',
  从平台购买本IP: 'Purchase this IP from the platform',
  '设置IP：': 'Set IP:',
  未做修改: 'Not been modified',
  '将本IP与分身【': 'Compare this IP with your browser [',
  '】绑定': '] Binding',
  '】解绑': '] Untie',
  '分身【': 'Split [',
  '】绑定本IP': '] Binding this IP',
  '】解绑本IP': '] Unbind this IP',
  '本IP地址由【': 'This IP address is provided by [',
  '】变更为【': '] changed to [',
  '】切换为【': '] Switch to [',
  购买平台为您提供的IP资源: 'Purchase IP resources provided by the platform',
  购买平台IP资源: 'Purchase platform IP resources',
  '我们为您提供覆盖全球主要城市的IP资源，您可按需购买':
    'We provide you with IP resources covering major cities around the world, which you can purchase on demand',
  '您可以根据您的使用用途，选择购买适合的IP资源，花漾提供的均为合规的公有云主机，内核静态、独享的IP地址，适合于跨境电商的店铺运营':
    'You can choose to purchase appropriate IP resources based on your use. Huayang provides compliant public cloud hosts with static and exclusive IP addresses, suitable for cross-border e-commerce store operations',
  立即购买: 'Buy now',
  '花漾为您提供遍布全球主要城市的公有云主机（含静态IP资源）':
    'Huayang provides you with public cloud hosts (including static IP resources) located in major cities around the world',
  已有IP资源: 'Existing IP resources',
  '如果您已经采购了其它IP服务商的资源，您可以将其导入至花漾中以供使用':
    'If you have purchased resources from other IP service providers, you can import them into Huayang for use.',
  导入IP地址: 'Import IP Address',
  '如果您已有IP地址，可将其导入至花漾，支持Socks5、Http、SSH等代理协议':
    'If you already have an IP address, you can import it to Huayang, which supports Socks5, Http, SSH and other proxy protocols',
  立即导入: 'Import now',
  批量导入IP: 'Batch import IP',
  您可以基于txt文本文件或者Excel文件将多个IP地址一次性批量导入至花漾:
    'You can import multiple IP addresses into Huayang in batches at one time based on txt text files or Excel files',
  批量导入: 'Batch import',
  创建IP池: 'Create IP pool',
  'IP池是指由若干IP地址组成的池子，常用于国内IP提供商，它们允许用户以API的形式批量提取IP地址':
    'IP pool refers to a pool of several IP addresses, often used by domestic IP providers. They allow users to extract IP addresses in batches in the form of APIs',
  立即创建: 'Immediately create',
  '自己已经拥有了IP资源，可将其导入至花漾中':
    'You already have IP resources and can import them into Huayang',
  '已有VPS/云主机': 'Existing VPS/cloud hosting',
  麦可隆浏览器迁移工具: 'Myron Browser Migration Tool',
  '麦可隆能够将VPS/云主机的浏览器环境无缝迁移至':
    'Micron can seamlessly migrate the browser environment of VPS/cloud hosting to',
  '中，包括主机浏览器的指纹信息、Cookie数据、网站密码等，并可指定是否在主机中安装IPGO以便将主机的IP地址导入至花漾中':
    ", including the fingerprint information of the host browser, Cookie data, website password, etc., and can specify whether to install IPGO in the host to import the host's IP address into Huayang",
  '是否创建/导入分身？': 'Create/import browser?',
  '需要先创建分身才能开启麦可隆迁移，是否立即创建/导入分身？':
    'You need to create an browser before starting Myclon migration. Do you want to create/import an browser now?',
  迁移克隆: 'Migrating browser',
  花漾IPGO: 'Huayang IPGO',
  'IPGO是云上悦动团队自主研发的针对VPS/云主机的IP代理服务程序，只需在VPS/云主机中安装IPGO，即可将VPS/云主机的IP地址一键导入至花漾中':
    'IPGO is an IP proxy service program for VPS/cloud hosts independently developed by the Yunshang Yuedong team. Just install IPGO in the VPS/cloud host, and you can import the IP address of the VPS/cloud host into Huayang with one click.',
  '添加VPS/云主机IP': 'Add VPS/cloud hosting IP',
  '已有VPS/云主机，可将其IP地址导入至花漾中':
    'There is a VPS/cloud host, and your IP address can be imported into Huayang',
  IPGO设置: 'IPGO settings',
  主机名称: 'Host name',
  导入规则: 'Import rule',
  全部导入: 'Import all',
  只导入IPv4: 'Import IPv4 only',
  只导入IPv6: 'Import IPv6 only',
  手动指定: 'Manually specify',
  网卡列表: 'List of network cards',
  网卡名称: 'Network card name',
  MAC地址: 'Mac address',
  内网地址: 'Private network address',
  '{{action}}指令已发出': '{{action}} Command has been issued',
  汇报: 'Report',
  '确定要重启选中的IPGO吗？': 'Are you sure you want to restart the selected IPGO?',
  此操作可能会导致30秒左右的服务中断:
    'This operation may cause a service interruption of approximately 30 seconds',
  重启: 'Restart',
  '确定要卸载选中的IPGO吗？': 'Are you sure you want to uninstall the selected IPGO?',
  此操作会自动删除通过此IPGO导入的IP地址:
    'This action will automatically delete IP addresses imported through this IPGO',
  卸载: 'Uninstall',
  导入的IP: 'Imported IP',
  '{{count}}个IP': '{{count}} IPs',
  主机规格: 'Host specifications',
  安装时间: 'Installation time',
  版本: 'Version',
  最近心跳时间: 'Recent heartbeat time',
  状态: 'Status',
  已停止: 'Has stopped',
  已卸载: 'Uninstalled',
  设置: 'Set',
  强制汇报: 'Mandatory reporting',
  IPGO列表字段自定义: 'IPGO list field customization',
  已就绪: 'Is ready',
  根据IP搜索: 'Search based on IP',
  更新时间: 'Update time',
  设置列表字段: 'Set list fields',
  字段: 'Fields',
  可在此处管理所有由当前团队自行安装的IPGO以及由其导入的IP地址:
    'All IPGO installed by the current team and the IP addresses imported by it can be managed here',
  'IPGO是由花漾灵动自主研发的主机代理软件，在主机/VPS中安装IPGO，可将其IP地址导入至花漾':
    'IPGO is a host agent software independently developed by Huayang Smart. By installing IPGO in the host/VPS, you can import its IP address into Huayang',
  '导入VPS/云主机的IP地址': 'Import the IP address of the VPS/cloud host',
  请更换检索条件: 'Please change the search criteria',
  请选择要操作的IPGO: 'Please select the IPGO to operate',
  批量重启: 'Batch restart',
  批量汇报: 'Batch report',
  批量卸载: 'Bulk unload',
  当前团队内所有IP地址: 'All current IP addresses in the team',
  当前团队内所有的静态IP: 'All static IPs in the current team',
  当前团队内所有的动态IP: 'All current dynamic IPs in the team',
  未绑定任何一个浏览器分身的IP地址: 'The IP address of any browser is not bound',
  在花漾内购买的公有云主机的IP地址: 'IP address of the public cloud host purchased in Huayang',
  联营IP: 'Associate IP',
  '和联营的浏览器分身一起带过来的IP地址，您可能需要为这些IP地址进行续费操作':
    'IP addresses brought with your affiliated browser browsers. You may need to renew these IP addresses',
  '72小时内即将过期并被销毁的公有云主机（IP一旦释放无法找回）':
    'Public cloud hosts that will expire and be destroyed within 72 hours (IP cannot be retrieved once released)',
  无法连通或无法提供服务的IP地址:
    'IP addresses that cannot be connected or cannot provide services',
  '当IP服务商允许用户以API的形式批量提取IP地址时，我们通常把它包装成一个IP池，方便您的使用':
    'When IP service providers allow users to extract IP addresses in batches in the form of APIs, we usually package it into an IP pool for your convenience.',
  联营团队: 'Joint Venture Team',
  导入时间: 'Import time',
  已开启: 'On',
  未开启: 'Off',
  购买周期: 'Buying cycle',
  '按{{period}}': 'Press {{period}}',
  为平台IP续费: 'Renew platform IP',
  '联营IP是指随同联营分身带过来的IP地址，只能对其进行续费，无法执行其它操作':
    'The joint venture IP refers to the IP address brought by the joint venture browser. It can only be renewed and no other operations can be performed',
  请先选中1至多个IP: 'Please select 1 or more IPs first',
  批量续费: 'Batch renewal',
  可复用: 'Reusable',
  丢弃: 'Discarded',
  按最小流量分配: 'Allocation by minimum traffic',
  按负载分配: 'Allocation by load',
  顺序分配: 'Sequentially assigned',
  随机分配: 'Randomly assigned',
  需要时生产: 'Produce when needed',
  手工生产: 'Manual production',
  为空时生产: 'Production for space-time',
  创建失败: 'Creation failed',
  '关于{{about}}，希望以下信息可以帮到您':
    'About {{about}}, I hope the following information can help you',
  不支持的IP池供应商: 'Unsupported IP pool providers',
  '不同的IP池供应商会有不同的IP获取机制，您可将您的IP池供应商反馈给花漾的在线客服，花漾会在1个工作日之内完成对您的供应商的适配':
    "Different IP pool providers will have different IP acquisition mechanisms. You can feedback your IP pool provider to Huayang's online customer service, and Huayang will complete the adaptation to your provider within 1 working day",
  当前供应商推荐使用海外接入点: 'Current suppliers recommend overseas access points',
  当前供应商推荐使用直连或国内接入点:
    'Current suppliers recommend using direct connections or domestic access points',
  是否独占访问: 'Whether to access exclusively',
  独占式: 'Exclusive',
  提取方式: 'Extraction methods',
  '如果IP服务位于海外，建议使用花漾海外接入点；如果IP服务位于国内，建议使用客户端或花漾国内接入点；如果IP服务商要求采用白名单且需要唯一的访问IP时，则只能使用您的客户端IP（您的客户端IP是唯一的）':
    'If the IP service is located overseas, it is recommended to use Huayang Overseas Access Point; if the IP service is located domestically, it is recommended to use the client or Huayang Domestic Access Point; if the IP service provider requires whitelisting and requires a unique access IP, you can only use your client IP (your client IP is unique)',
  '（取决于IP自身）': '(Depends on the IP itself)',
  接入点: 'Access point',
  客户端: 'Client',
  基本设置: 'Basic settings',
  IP池名称: 'IP pool name',
  请输入IP池名称: 'Please enter IP pool name',
  请选择供应商: 'Please select a supplier',
  需要在花漾客户端内完成IP导入操作:
    'You need to complete the IP import operation in Huayang client',
  IP批量导入: 'IP batch import',
  IP池创建完毕后开启IP批量导入向导: 'Start the IP Batch Import Wizard after creating the IP pool',
  '您可将分身绑定在IP池，当访问分身以打开花漾浏览器时，系统会从IP池中申请一个IP绑定到该分身上，IP池适用于通过API提取IP地址的服务商':
    'You can bind your browser to the IP pool. When you access your browser to open the Huayne browser, the system will apply for an IP from the IP pool to bind it to the browser. The IP pool is suitable for service providers who extract the IP address through the API',
  引用已有IP: 'Reference existing IP',
  '您可将当前团队已有IP地址导入至IP池，这并不会影响到被导入IP与其它分身的绑定关系':
    'You can import the existing IP address of the current team into the IP pool, which will not affect the binding relationship between the imported IP and other browsers',
  已引用到当前IP池: 'Referenced to current IP pool',
  池容量: 'Pool capacity',
  IP生命时长: 'IP lifetime',
  '确定要删除指定的IP池吗？': 'Are you sure you want to delete the specified IP pool?',
  IP列表字段自定义: 'IP list field customization',
  请先选中1至多个IP池: 'Please select 1 to multiple IP pools first',
  '正在删除IP池，请稍候': 'Please wait while deleting IP pool',
  批量删除: 'Delete',
  基本属性: 'Basic',
  创建者: 'Creator',
  描述: 'Described',
  相关策略: 'Relevant strategies',
  相关策略更详细的介绍请阅读: 'Please read for a more detailed introduction of relevant strategies',
  IP池策略介绍: 'Introduction to IP pool policy',
  IP生产策略: 'IP production strategy',
  IP分配策略: 'IP allocation policy',
  IP回收策略: 'IP recycling strategy',
  IP池属性: 'IP pool properties',
  '如果供应商返回的IP数据支持过期时间，则优先使用过期时间。':
    'If the IP data returned by the supplier supports expiration times, the expiration times are used first.',
  指定时长: 'Specified time length',
  请选择您要绑定的IP池: 'Please select the IP pool you want to bind to',
  绑定IP池: 'Binding IP pool',
  当前正在绑定该IP池: 'This IP pool is currently being bound',
  缺少可用的IP池: 'Lack of available IP pool',
  '当前团队尚未创建IP池，您需要先创建IP池':
    'The current team has not created an IP pool yet. You need to create an IP pool first',
  查看IP池: 'View IP pool',
  'IP池名称为4-32个字符': 'IP pool names are 4-32 characters long',
  通过您的客户端直接访问IP地址: 'Direct access to IP addresses through your client',
  '您当前的客户端IP地址为：': 'Your current client IP address is:',
  '入口IPv4：': 'Import IPv4:',
  '入口IPv6：': 'Import IPv6:',
  '入口位置：': 'Entrance location:',
  '出口IPv4：': 'Export IPv4:',
  '出口IPv6：': 'Export IPv6:',
  '出口位置：': 'Exit location:',
  关于IP白名单: 'About IP whitelist',
  '如果您的IP服务商已开启白名单，请将下述IP加入白名单列表':
    'If your IP service provider has whitelisted, please add the following IPs to the whitelist list list',
  显示IPv4: 'Display IPv4',
  显示IPv6: 'Displays IPv6',
  '不支持的代理协议：{{proxyType}}': 'Unsupported proxy protocol: {{proxyType}}',
  代理协议为空: 'Proxy agreement is empty',
  是: 'Is',
  独享: 'Exclusive',
  共享: 'Shared',
  临期: 'Temporary',
  转让: 'Transfer',
  '错误提示为：': 'The error prompt is:',
  '您购买的IP正处于“准备”状态，预期还需要几分钟的时间才能够就绪，请稍候':
    'The IP you purchased is in a "ready" state. It is expected that it will take a few minutes to be ready. Please wait',
  '当前IP已失效，': 'Current IP has expired,',
  '我们建议的处理措施为：若排除服务器自身及网络问题，请检查IPGO是否正常运行，如无法运行可以':
    'Our recommended handling measures are: If you resolve problems with the server itself and the network, please check whether IPGO is running normally. If it fails, you can.',
  重新安装IPGO: 'Reinstall IPGO',
  '当前IP（': 'Current IP (',
  '代理自有IP）已失效，': "Agent's own IP) has expired,",
  '我们建议的处理措施为：请在': 'Our recommended treatment measures are: please visit the',
  IP设置: 'IP settings',
  中检查代理配置信息是否正确: 'Check whether the agent configuration information is correct in',
  '当前IP服务即将到期，请您及时续费，否则将无法正常使用':
    'The current IP service is about to expire. Please renew it in time, otherwise it will not be able to be used normally.',
  全部聊天: 'All Chat',
  聊天室同步: 'Chat room synchronization',
  '-没有更多啦-': '- No more-',
  '任务已提交,请等待调度': 'The task has been submitted, please wait for dispatch',
  '-没有更多消息啦-': '- No more news-',
  发送: 'Sent',
  联系人同步: 'Contact synchronization',
  创建成功: 'Created successfully',
  修改Kakao账号: 'Modify Kakao account',
  新增Kakao账号: 'Add Kakao account',
  请上传头像: 'Please upload your browser',
  请输入昵称: 'Please enter your nickname',
  手机号码: 'Mobile phone',
  地址: 'Address',
  邮编: 'Zip code',
  发送消息: 'Send a message',
  请输入聊天内容: 'Please enter the chat content',
  '同步任务已提交,请等待调度': 'Synchronization task has been submitted, please wait for dispatch',
  同步Kakao联系人: 'Sync Kakao contacts',
  同步Kakao聊天室: 'Sync Kakao chat rooms',
  设备: 'Device',
  请选择设备: 'Please select a device',
  全部联系人: 'All contacts',
  同步至手机: 'Sync to mobile phone',
  '将所选的联系人同步至对应手机/账号':
    'Sync the selected contact to the corresponding mobile phone/account',
  '已为您创建了一笔“联系人同步”的任务': ' "Contact Sync" task has been created for you',
  确定删除选中的: 'Are you sure you want to delete the selected ',
  '该操作删除的数据无法恢复，请谨慎操作':
    'The data deleted by this operation cannot be recovered. Please operate with caution',
  确定收藏选中的: 'Are you sure you want to favorite the selected',
  '个联系人吗？': ' contact person?',
  同步到手机: 'Sync to mobile phone',
  批量收藏: 'Batch collection',
  新增联系人: 'New contact',
  请至少选择一个联系人: 'Please select at least one contact person',
  根据昵称和手机号码检索: 'Search based on nickname and mobile phone number',
  昵称: 'Nickname',
  手机号: 'Mobile phone',
  归属账号: 'Belonging account number',
  聊天: 'Chat',
  取消收藏: 'Cancel collection',
  收藏: 'Collection',
  团队收藏的联系人: 'Contact person for the team collection',
  立即加入: 'Join now',
  注册并加入: 'Registered to join',
  登录并加入: 'Log in and join',
  '您当前以其它的身份登录了花漾系统，需要注销后才能够完成后续工作':
    'You are currently logged in to Huayang system as a different identity. You need to log out before you can complete subsequent work.',
  立即注销: 'Immediately cancel',
  您已成功加入团队: 'You have successfully joined the team',
  进入团队看看: 'Join the team and have a look',
  您的登录账号: 'Your login details',
  邀请人: 'Inviter',
  拟加入团队: 'Want to join the team',
  请输入密码: 'Please enter the password',
  '8-40个字符，需至少包含以下三类：大写字母、小写字母、数字、特殊符号':
    '8-40 characters, which must contain at least the following three categories: upper case letters, lower case letters, numbers, and special symbols',
  请确认密码: 'Please confirm your password',
  请再次输入密码: 'Please enter your',
  两次输入的密码不一致: 'The passwords entered twice are inconsistent',
  分配给我的主播达人: 'Live creator',
  分配给我的视频达人: 'Video creator',
  分配给我的刷榜大哥: 'Gifter',
  分配给我的普通用户: 'Normal users',
  正在直播的主播达人: 'Creators in live',
  其它: 'Other',
  '已设置用户状态为：': 'The user status has been set as:',
  已取消认领该用户: 'The user has been cancelled',
  '已设置用户状态为：已触达': 'The user status has been set as: reached',
  已屏蔽: 'Has blocked',
  '，原因为“': ', the reason is "',
  当前条件下没有找到符合条件的刷榜大哥:
    'No eligible gifter can be found under the current conditions',
  '暂无数据，请确认管理员是否有给您分配刷榜大哥':
    'No data yet. Please confirm whether the administrator has assigned you a gifter to swipe the list.',
  刷新: 'Refresh',
  唤醒TikTok: 'Wake up TikTok',
  未接触: 'Uncontacted',
  已触达: 'Sent',
  已签约: 'Signed',
  暂且搁置: 'On Hold',
  取消认领: 'Unclaim',
  屏蔽此用户: 'Block',
  'TikTok唤醒方式：': 'TikTok wake-up method:',
  AppStore唤醒: 'AppStore Wake Up',
  Tiktok协议唤醒: 'Tiktok protocol wake-up',
  '弹出菜单：': 'Pop-up menu:',
  '快捷菜单（含用户状态管理）': 'Shortcut menu (including user status management)',
  直接唤醒Tiktok: 'Wake up Tiktok directly',
  唤醒TK时自动将用户设置为已触达: 'Automatically sets user to reached when awakening TK',
  切换至电脑版: 'Switch to PC version',
  请选择原因屏蔽: 'Please select the reason for blocking',
  '已设置达人状态为：': 'Creator status has been set as:',
  已取消认领该达人: 'Claim for the creator has been cancelled',
  '已设置达人状态为：已触达': 'The status of the person has been set to: reached',
  当前条件下没有找到符合条件的主播达人:
    'No qualified creators have been found under the current conditions',
  '暂无数据，请确认管理员是否有给您分配主播':
    'No data yet. Please confirm whether the administrator has assigned you an creator',
  屏蔽此达人: 'Block this creator',
  '快捷菜单（含达人状态管理）': 'Shortcut menu (including status management of creator)',
  唤醒TK时自动将达人设置为已触达: 'When awakening TK, automatically sets the person to reached',
  当前条件下没有找到符合条件的普通用户:
    'No qualified normal users were found under the current conditions',
  '暂无数据，请确认管理员是否有给您分配普通用户':
    'No data yet. Please confirm whether the administrator has assigned you normal users.',
  当前条件下没有找到符合条件的视频达人:
    'No qualified video celebrities were found under the current conditions',
  '暂无数据，请确认管理员是否有给您分配视频达人':
    'No data yet. Please confirm whether the administrator has assigned creators to you.',
  购买: 'Purchase',
  '（即时生效至': '(Effective immediately until',
  标准价格: 'Standard price',
  '元/月': 'Yuan/month',
  优惠后价格: 'Preferential price',
  该链接已过期: 'This link has expired',
  您的TikTok店铺: 'Your TikTok store',
  已授权给花漾灵动OpenAPI权限: 'Huayang Smart OpenAPI permissions have been authorized',
  '感谢您对我们的信任，我们将一如继往的为您提供优秀的产品与服务':
    'Thank you for your trust in us, we will continue to provide you with excellent products and services',
  '现在，您可以关闭这个页面了': 'Now you can close this page',
  周一: 'Monday',
  周二: 'Tuesday',
  周三: 'Wednesday',
  周四: 'Thursday',
  周五: 'Friday',
  周六: 'Saturday',
  周日: 'Sunday',
  全部达人: 'All creators',
  '（取前': '(Take top ',
  '个）': '）',
  当前条件可查询到: 'Current conditions can be queried',
  个达人: ' creators',
  取筛选过的前: 'Take the screened ones',
  修改筛选条件: 'Modify filter criteria',
  查看筛选结果: 'View filter results',
  个筛选条件: 'Screening condition',
  计划已: 'Plan has',
  启用: 'Enabled',
  自动计划: 'Autoschedule',
  创建自动计划: 'Create auto plans',
  序号: 'Index',
  计划类型: 'Plan type',
  筛选条件: 'Your filters',
  执行时间: 'Time',
  执行频率: 'Execution frequency',
  每天: 'Every day',
  下次执行时间: 'Next',
  暂停: 'Suspended',
  启动: 'Start',
  修改: 'Modify',
  删除: 'Delete',
  删除计划: 'Delete plan',
  '确定删除该计划吗？': 'Are you sure you want to delete this plan?',
  全选: 'Select all',
  计划属性: 'Schedule properties',
  目标达人: 'Target',
  主播达人: 'LIVE creators',
  计划名称: 'Plan name',
  请输入计划名称: 'Please enter plan name',
  计划名称已存在: 'Plan name already exists',
  同步删除与该主播的聊天记录: 'Sync deleting chats with this creator',
  删除会话: 'Delete session',
  请至少添加一项执行时间: 'Please add at least one execution time',
  请选择执行时间: 'Please select execution time',
  增加一笔精准执行: 'Add a precise execution',
  重复日期: 'Repeat',
  请选择重复日期: 'Please select a duplicate date',
  相关条件: 'Relevant conditions',
  其它设置: 'Other settings',
  请选择账号: 'Please select an account',
  拟使用的账号: 'Account',
  邀约规则: 'Rules',
  '忽略已分配/已认领的': 'Ignore assigned/claimed',
  全部邀约: 'All invitations',
  '带有标签“直播间守候”的账号': 'Account with the tag "Live Room Waiting"',
  持续时长: 'Duration',
  结果: 'Results',
  计划已修改: 'Plan has been revised',
  计划已创建: 'Plan created',
  '通过设置的查询条件，筛选出目标达人，自动执行任务':
    'Filter out the target people through the set query conditions and automatically execute the task',
  创建: 'Create',
  '屏蔽达人的原因（最多4条）': 'Reasons for block (up to 4)',
  不能为空: 'Cannot be empty',
  正在清理: 'Cleaning ',
  天前的: ' days ago ',
  所有: 'All ',
  '主播缓存数据..': 'Creator cache',
  清理成功: 'Clean up successfully',
  清理方式: 'Method',
  按条件清理: 'Clean up according to conditions',
  全部清理: 'Completely cleaned',
  请输入保留天数: 'Please enter retention days',
  保留天数: 'Retention',
  勾选状态的达人将不会被清理: 'Those who check the status will not be cleared',
  保留达人: 'Reserve the people',
  只清理流水为0的主播: 'Only clean up creators with zero turnover',
  立即清理: 'Immediately clean up',
  主播缓存时长: 'Creator cache',
  '为避免主播达人成为“死数据”，必须对已有的主播达人进行清理，机器人才能够重新抓取并更新主播达人的状态，缓存时间范围为2-30天':
    'In order to prevent creators from becoming "dead data", existing creators must be cleaned up before the robot can grab and update the status of creators again. The caching time range is 2-30 days',
  缓存时长: 'Cache duration',
  凡导入日期大于缓存时长的主播达人将被自动清理:
    'All creators whose import date is greater than the cache duration will be automatically cleaned up',
  勾选状态的主播达人将不会被清理: 'Creators will not be cleared',
  '确认移除这个设备吗？': 'Are you sure to remove this device?',
  绑定设备新增成功: 'Binding device added successfully',
  RPA运行设备: 'RPA device',
  设备名称: 'Device name',
  登录身份: 'Log on as',
  角色: 'Role',
  配置: 'Device Specs',
  并发数量: 'Concur',
  修改并发数: 'Modify concurrency',
  请输入并发数量: 'Please enter the concurrent quantity',
  不清理: "Don't clear",
  通用配置: 'Generic configuration',
  屏蔽达人原因: 'Reasons for block',
  '超过指定时长的主播会被删除，下次筛选时能够抓取其最新的数据，并能够更新其可邀约状态':
    'Creators that exceed the specified period of time will be deleted, and their latest data can be retrieved during the next screening, and their invisibility status can be updated',
  KaKao配置: 'KaKao Configuration',
  发送私信的分身的标签: 'The tag of the browser who sent the private message',
  单账号每日最多私信条数: 'Max PM per day one account',
  单账号每批次最多私信条数: 'Max PM per batch one account',
  单账号每批私信间隔: 'Interval between PMs',
  工作时间设置: 'Working time',
  编辑Kakao账号: 'Edit Kakao account',
  '请确保手机已经通过USB线缆连接到花漾客户端，并确保手机上的KaKao账号已经登录并可以正常使用':
    'Please make sure that your phone is connected to the Huayang client via a USB cable, and make sure that the KaKao account on your phone is logged in and can be used normally',
  手机设备: 'Handset device',
  请选择手机设备: 'Please select a mobile device',
  KaKao账号全称: 'Full name of KaKao account',
  KaKao账号简称: 'KaKao account abbreviation',
  '＂授权': '"Authorization',
  授权: 'Authorized',
  KaKao账号: 'KaKao account',
  添加账号: 'Add Account',
  个接收者: 'Of recipients',
  流程执行人: 'Process owners in the',
  默认消息推送方式: 'Default message push method',
  消息推送方式: 'Message push method',
  '每个流程在执行结束后都可以通过钉钉、企业微信、飞书等形式进行通知，当您在这里设置团队内部沟通方式后，每个流程执行结束后都默认通过此沟通方式发送消息通知（每个流程可以单独定义）':
    'After execution, each process can be notified through nails, corporate WeChat, flying books, etc. After you set up the internal team communication method here, message notifications will be sent through this communication method by default after execution of each process (Each process can be defined separately)',
  企业微信: 'Enterprise WeChat',
  钉钉: 'Nail',
  飞书: 'Flying book',
  自定义关键词: 'Custom keywords',
  '您需要在群聊机器人的安全选项中设置自定义关键词“花漾”':
    'You need to set the custom keyword "Hua Yang" in the security options of the Group chats robots',
  默认消息接收者: 'Default message recipient',
  消息接收者: 'Message recipient',
  内部沟通群Webhook: 'Internal communication group Webhook',
  需要确保接收者绑定了微信: 'Need to ensure that the recipient is bound to WeChat',
  参考指引: 'Reference guide on',
  '针对不同的消息通知机制，需要填入不同的Webhook，请阅读':
    'Different Webhooks need to be filled in for different message notification mechanisms. Please read',
  日: 'On',
  一: 'A',
  二: 'Second',
  三: 'Three',
  四: 'Four',
  五: 'Five',
  六: 'Six',
  私信发送数量计算器: 'Private Message Sending Quantity Calculator',
  经纪人账号数量: 'Backstage accounts',
  日期: 'Date',
  平均每封私信所需时间: 'Average time per message',
  该日最高可发送私信数量: 'Max messages in one day',
  封: 'Seal',
  手机账号: 'Mobile',
  最高并发数量: 'Maximum Concurrent Count',
  单账号每批次最多分享名片数量:
    'The maximum number of business cards shared per batch for a single account',
  每手机每批次间隔: 'Each mobile phone, each batch interval',
  单账号每日最多分享名片数量:
    'The maximum number of business cards shared by a single account per day',
  单账号每日最多私信默认条数: 'The maximum number of private messages per day for a single account',
  发送私信的: 'PM ',
  的标签: ' Tag',
  '建议：在运营人员工作时间段之前，完成70%的私信发送':
    'Recommendation: Complete 70% of private messages sent before operators work hours',
  '0代表不限制': '0 means no limit',
  蓝色区块代表该时段为工作时间: 'The blue block represents the working time period',
  不限制: 'Not limit',
  TikTok分享名片的手机账号: "TikTok's mobile account for sharing business cards",
  浏览器私信账号: 'Browser private message account',
  浏览器官方私信账号: 'Browser official private message account',
  发送私信的手机账号: 'Mobile account used to send private messages',
  公会后台私信账号: 'Guild backstage private message account',
  '澳大利亚+': 'Australia +',
  台湾: 'Taiwan',
  巴勒斯坦: 'Palestinian',
  '英国+': 'UK +',
  '法国+': 'France +',
  '西班牙+': 'Spain +',
  '德国+': 'Germany +',
  '瑞典+': 'Sweden +',
  最低流水配置: 'Min coins',
  最低流水要求: 'Min coins',
  请设置最低流水要求: 'Please set minimum coins requirements',
  '历史最高流水大于上述数值的主播才允许抓取到您的团队，最小值为0，最大值为200，请注意，最低流水要求越高，每日抓取的符合要求的主播数量越少':
    'Only creators with the highest historical coins rate greater than the above value are allowed to crawl to your team. The minimum value is 0 and the maximum value is 200. Please note that the higher the minimum coins rate requirement, the fewer creators that meet the requirements can be crawled every day',
  '确定要更改TikTok所属区域吗？':
    'Are you sure you want to change the region to which TikTok belongs?',
  '请注意，一个月只能更改': 'Please note that you can only change in one month',
  '次；更改后，下次抓取主播数据时会自动生效；现有的主播数据继续保留，请自行决定是否清理':
    'Times; after the change, it will automatically take effect the next time you grab creator data; existing creator data will continue to be retained, please decide whether to clean it up',
  TikTok公会所属区域: 'TikTok Area',
  '更改公会所属区域，请注意，一个月只能更改':
    'Change the area to which the guild belongs. Please note that you can only change it within one month.',
  所属区域: 'Region',
  国家列表: 'Countries',
  至少选择一个国家: 'Select at least one country',
  主播类型: 'Creator type',
  至少要选择一种类型的主播: 'Select at least one type of creator',
  游戏主播: 'Game creator',
  抓取游戏主播: 'Game creators',
  带货主播: 'Showcase creators',
  抓取带货主播: 'Showcase creators',
  其他类型主播: 'Other creators',
  抓取其他类型的主播: 'Other creators',
  当前不允许切换区域: 'Switching zones is not currently allowed',
  TikTok公会配置: 'TikTok Guild Configuration',
  最低流水: 'Min coins',
  '该产品的最新价格已经发生变化，您已切换至新的价格体系，也可以继续使用旧的价格体系，请选择最适合您的方式':
    'The latest price of this product has changed. You have switched to the new price system, or you can continue to use the old price system. Please choose the method that suits you best.',
  切换至旧报价: 'Switch to old quote',
  '该产品的最新价格已经发生变化，您可以使用旧有价格续费，也可以使用新的价格体系，请选择最适合您的方式':
    'The latest price of this product has changed. You can use the old price to renew it or use the new price system. Please choose the method that suits you best.',
  切换至新报价: 'Switch to new quote',
  续费商品: 'Renew goods',
  '使用旧报价时无法使用优惠码，只能使用官方优惠条件':
    'Coupon codes cannot be used when using old quotes, only official discount conditions can be used',
  请输入优惠码: 'Please enter the coupon code',
  流程名称: 'Process name',
  失败策略: 'Failure policy',
  忽略: 'Ignore',
  当任务池中的任务在执行中遇到失败等异常情况的策略:
    'Strategy for handling failures or exceptions during task execution',
  拖拽可调整优先级: 'Drag and drop to adjust priority',
  流程优先级: ' Process priority',
  '排前面的优先级越高，任务池中的任务越早调度':
    'The higher the priority at the front, the earlier the task is scheduled',
  选择客户端设备: 'Select device',
  本机: 'Current computer',
  核数: 'Cpus',
  设备标识: 'Identification',
  在线: 'Online',
  离线: 'Offline',
  已绑定: 'Bound',
  未绑定: 'Unbound',
  该设备未绑定流程任务卡: 'The device is not bound to a process task card',
  已经添加过了: 'Already added',
  并发数: 'Concurrency',
  使用的手机账号: 'Mobile account',
  使用与该达人最近沟通用到的手机账号:
    'Use the mobile account used to communicate with the person recently',
  '如果从未有手机账号联系过或者最近一次用到的手机账号包含有标签“账号封禁”，则分配新的手机账号':
    'If a mobile account has never been contacted or the mobile account used last time contains the tag "Account Block", a new mobile account will be assigned',
  强制指定以下手机账号: 'Mandatory designation of the following mobile accounts',
  '如果强制使用某个手机账号，请不要选择过多的达人，否则，受到手机私信发送频率的限制，会导致发送私信的间隔过长':
    'If you are forced to use a certain mobile phone account, please do not select too many people. Otherwise, due to the restriction on the frequency of sending private messages on your mobile phone, the interval between sending private messages will be too long',
  强制分配新的手机账号: 'Force the allocation of new mobile accounts',
  手机账号分配原则: 'Mobile account allocation principles',
  '请注意：会忽略带有标签“账号封禁”的手机账号':
    'Please note: Mobile accounts with the tag "Account Block" will be ignored',
  '”和以下标签的手机账号': '"and the mobile phone account with the following tags',
  私信过快: 'Private message is too fast',
  关注概率: 'Focus on chance',
  '关注视频主的概率，最小值0（暨0%，不关注），最大值100（暨100%，必关注）':
    'The chance of follower to the video owner, the minimum value is 0 (cum 0%, not follower), the maximum value is 100 (cum 100%, must follower)',
  点赞概率: 'Like chance',
  '点赞的概率，最小值0（暨0%，不点赞），最大值100（暨100%，必点赞）':
    'The chance of likes, the minimum value is 0 (cum 0%, no likes), the maximum value is 100 (cum 100%, must like)',
  收藏概率: 'Collection chance',
  '收藏的概率，最小值0（暨0%，不收藏），最大值100（暨100%，必收藏）':
    'The chance of collection, the minimum value is 0 (cum 0%, no collection), the maximum value is 100 (cum 100%, mandatory collection)',
  观看数量: 'Number of views',
  '观看视频的数量，当观看完指定数量的视频后，流程结束':
    'The number of videos viewed. When the specified number of videos is viewed, the process ends',
  '设置每个视频停留浏览的时长，单位为秒，可设置固定时长，也可设置随机时长，随机时长设置范围通过"-"减号分隔，例如：8-15':
    'Set the duration of viewing each video in seconds. You can set a fixed duration or a random duration. The random duration setting range is separated by a "-" minus sign, for example: 8-15',
  视频观看时长: 'Video viewing time',
  请输入视频观看时长区间: 'Please enter the video viewing duration range',
  '（秒）': '(seconds)',
  '评论的概率，最小值0（暨0%，不评论），最大值100（暨100%，必评论）':
    'The chance of comment, the minimum value is 0 (plus 0%, no comment), the maximum value is 100 (plus 100%, a comment must be made)',
  评论概率: 'Comment chance',
  辅助表情: 'Auxiliary expression',
  '在私信消息中添加一些辅助表情（使私信内容产生差异化）的概率':
    'The chance of adding some auxiliary emoticons to the private message message (differentiated the content of the private message)',
  评论内容: 'Comment content',
  和私信内容保持一致: 'Keep consistent with the content of the private message',
  从以下评论内容中随机选择: 'Choose randomly from the following comments',
  请输入评论内容: 'Please enter the comment content',
  '请输入您的评论内容，以回车换行符为分割':
    'Please enter your comment content, divided by carriage return and newline character',
  删除所有会话: 'Delete all sessions',
  '（避免私信人员过多导致聊天记录显示不全）':
    '(Avoid too many private messages, causing incomplete chat records to be displayed)',
  关注主播: 'Focus on the creator',
  发送邀约卡片: 'Send invitation card',
  '（只有公会后台账号才支持此特性）': '(Only guild backend accounts support this feature)',
  使用经纪人话术模板: 'Use backstage speaking templates',
  '（在花漾中设置的邀约话术不再生效）':
    '(The invitation words set in Huayang are no longer effective)',
  '通过对达人进行关注、点赞、收藏与评论等动作，增加私信回复率':
    'Increase the response rate of private messages by following, liking, collecting and commenting on celebrities',
  TikTok主播达人邀约话术: "Live creator's invitation script",
  TikTok普通用户邀约话术: "Normal user's invitation script",
  TikTok刷榜大哥邀约话术: "Gifter's invitation script",
  TikTok视频达人邀约话术: "Video creator's invitation script",
  添加成功: 'Added successfully',
  添加话术: 'Add script',
  请输入话术: 'Please enter your script',
  话术: 'Script',
  添加时间: 'Addition time',
  修改话术: 'Modify your script',
  删除话术: 'Delete script',
  '确定删除该话术吗？': 'Are you sure you want to delete this script?',
  团队名称: 'Team name',
  请输入团队标识: 'Team ID',
  团队标识应该是一长串数字: 'The team logo should be a long list of numbers',
  用户数量: 'Max users',
  请设置用户数量: 'Please set the number of users',
  每日采集主播数量: 'Creators per day',
  请设置每日采集主播数量: 'Please set the number of creators collected per day',
  生效时间: 'Effective time',
  请设置生效时间: 'Please set the effective time',
  失效时间: 'Expiration',
  请设置失效时间: 'Please set the expiration time',
  校验: 'Check',
  手动失效: 'Expired now',
  手动有效: 'Effective now',
  修改子团队配置: 'Modify sub-team configuration',
  激活子团队: 'Activate sub-teams',
  根据团队名称或标识检索: 'By team name or ID',
  子团队名称: 'Name',
  子团队标识: 'ID',
  修改配置: 'Modify the configuration',
  更新成功: 'Update was successful',
  主播信息更新配置: ' Creator information update configuration',
  信息更新账号所用标签: 'Tag of browsers',
  每分身每次更新主播数: 'Creators updated per clone',
  主播信息更新: ' creators information update',
  '（已用': '(',
  '）_已用': ' used)',
  已购商品: 'Product',
  对应团队: 'Team',
  账号配额: 'Account',
  手机数量配额: 'Mobile',
  团队成员配额: 'Member',
  过期时间: 'Expiration',
  TikTok账号名称: 'TikTok account',
  微信消息通知: 'WeChat message notification',
  消息发送时间: 'Message transmission time',
  请选择时间: 'Please select time',
  消息接收人: 'Message recipient',
  TikTok配置: 'TikTok configuration',
  Kakao配置: 'Kakao Configuration',
  子团队: 'Sub-teams',
  经纪人账号: 'Backstage account',
  普通账号: 'Browser',
  官方账号: 'Official account',
  指纹特征码: 'Fingerprint signature code',
  来源地: 'Source',
  平台浏览器: 'Platform browser',
  '平台：': 'Platform:',
  '站点：': 'Site:',
  打开浏览器: 'Open browser',
  清理本地缓存文件: 'Clean up local cache files',
  '删除本地位于“浏览器配置文件”中的和该分身相关的所有数据，打开浏览器会话时会从云端重新下载缓存数据':
    'Delete all data related to the browser located locally in the "Browser Configuration File", and the cached data will be downloaded again from the cloud when you open a browser session',
  清理云端缓存文件: 'Clean up cached files in the cloud',
  '删除云端和该分身相关的所有缓存数据，当关闭浏览器会话时会将本地的数据重新上传至云端（上传哪些数据取决于云端同步策略）':
    'Delete all cached data related to the cloud and the browser. When the browser session is closed, the local data will be uploaded back to the cloud (which data is uploaded depends on the cloud synchronization policy)',
  '正在为您清理，请稍候...': 'Please wait while cleaning it for you...',
  '分身清理是指清理所选分身的Cookie（含LocalStorage、IndexedDB）以及缓存数据，分身清理等价于常规浏览器中的“清理缓存”操作':
    'Browser cleanup refers to cleaning up the cookies (including LocalStorage, IndexedDB) and cached data of the selected browser. Browser cleanup is equivalent to the "cleanup cache" operation in regular browsers',
  分身清理完毕: 'The browsers are cleared',
  打开缓存文件夹: 'Open cache folder',
  请在花漾客户端内操作: 'Please operate in Huayang client',
  当前分身对应的浏览器处于打开状态: 'The browser corresponding to the current browser is open',
  请手动关闭该分身所对应的花漾浏览器后再执行清理操作:
    'Please manually close the Huayang browser corresponding to this browser before performing cleaning operations',
  批量分身清理: 'Batch cleanup',
  分身清理: 'Clean up in two places',
  导入Cookie: 'Import cookies',
  '将已有的 Cookie 导入至此分身': 'Import existing cookies into this browser',
  其他浏览器导入: 'Import from other browsers',
  '为您生成一个链接，一键采集其它浏览器的指纹与Cookie数据':
    'Generate a link for you to collect fingerprints and Cookie data from other browsers with one click',
  VPS克隆: 'VPS browser',
  '将VPS/云主机中的环境整体克隆至花漾，包括浏览器指纹、Cookie数据、网站密码、IP地址等':
    'Clone the entire environment in the VPS/cloud host to Huayang, including browser fingerprints, Cookie data, website passwords, IP addresses, etc.',
  分身迁移与合并: 'Split migration and merger',
  '将某个分身的Cookie等数据迁移/合并至另外一个分身':
    'Migrate/merge data such as cookies from one browser to another browser',
  迁移成功: 'Migration success',
  请选择分身: 'Please choose a browser',
  '将当前浏览器分身的Cookie、指纹、网站密码等数据迁移至另一个目标分身':
    'Migrate data such as cookies, fingerprints, and website passwords from the current browser to another target browser',
  目标分身: 'Target browser',
  迁移并合并目标分身的Cookie数据: 'Migrate and merge Cookie data from target browsers',
  Cookie冲突策略: 'Cookie conflict policy',
  覆盖: 'Coverage',
  清空目标分身的Cookie数据: 'Clear the Cookie data of the target browser',
  迁移并替换目标分身的IndexedDB: 'Migrate and replace the IndexedDB of the target browser',
  迁移并替换目标分身的LocalStorage: "Migrate and replace the target browser's LocalStorage",
  迁移并替换目标分身的浏览器指纹:
    'Migrate and replace the browser fingerprint of the target browser',
  迁移并替换目标分身的网站密码: 'Migrate and replace the website password of the target browser',
  分身列表字段自定义: 'Customize browser list fields',
  团队成员分身字段同步: 'Synchronization of team member browser fields',
  '一旦开启此选项，意味着当前团队所有成员都会采用相同的分身字段设置（只有超管与BOSS才能够开启/关闭此选项）':
    'Once this option is enabled, it means that all members of the current team will use the same browser field setting (only Supervisors and Bosses can turn this option on/off)',
  '确定要开启同步分身字段的选项吗？':
    'Are you sure you want to turn on the option to synchronize the browser field?',
  '一旦开启意味着当前是团队其它成员的分身字段都会以当前的字段设置为准，请确认是否继续':
    'Once enabled, it means that the current field settings for other team members will govern. Please confirm whether to continue',
  导入成功: 'Import is successful',
  登录链接: 'Login link',
  '针对个别已知平台，系统为您提供该平台的默认登录链接，如果该链接与预期不符，您可将其修订成正确的登录链接；针对未知平台，您可在这自行输入该平台的登录链接，如果没有，可置为空':
    'For individual known platforms, the system provides you with a default login link for that platform. If the link does not meet expectations, you can revise it to the correct login link; for unknown platforms, you can enter the login link for that platform here. If not, you can set it blank',
  Cookie数据: 'Cookie data',
  '解析Cookie失败，请检测Cookie格式是否正确':
    'Failed to parse the Cookie. Please check whether the Cookie format is correct',
  导入分身的认证信息与Cookie数据:
    'Import authentication information and Cookie data of the browser',
  名称前缀: 'Name prefix',
  '名称已存在（请注意检查分身回收站的分身名称）':
    'The name already exists (please check the name of the browser in the browser recycling bin)',
  IP隔离设置: 'IP isolation settings',
  '针对一些敏感网站（如电商店铺），如果多个分身使用同一个IP访问有可能造成关联风险':
    'For some sensitive websites (such as e-commerce stores), if multiple browsers use the same IP to access, it may cause association risks.',
  更改: 'Change',
  分身数量: 'Number of browsers',
  团队前: 'Before the team',
  '个分身免费，': 'Each person is free,',
  一次最多创建: 'Create at most once',
  '个分身）': ' browser)',
  创建无痕浏览器分身: 'Create a traceless browser browser',
  '无痕分身相当于“浏览器的隐身模式”，本身不存储Cookie、LocalStorage等数据，且会动态变化浏览器指纹，每次打开都是一个全新的花漾浏览器':
    'Traceless browser is equivalent to "browser stealth mode". It does not store data such as Cookies and LocalStorage, and dynamically changes the browser fingerprint. Every time it opens, it will be a brand new browser.',
  所属平台: 'Platform',
  其它平台: 'Other platforms',
  浏览器指纹: 'Browser fingerprint',
  分身配置: 'Split configuration',
  拟创建的电脑浏览器分身数量: 'Number of computer browser browsers to be created',
  '每个浏览器分身都是一个相互隔离、互不干扰的浏览器运行实例，分身需要绑定浏览器指纹（必需）和IP地址（可选）后才能打开对应的浏览器，此处指绑定电脑浏览器指纹':
    'Each browser is a browser running instance that is isolated and non-interfering with each other. The browser needs to bind the browser fingerprint (required) and IP address (optional) before opening the corresponding browser. This refers to binding computer browser fingerprint',
  最小需要创建1个演示分身: 'Minimum of 1 presentation browser is needed',
  '出于演示目的，最多只能创建10个演示分身':
    'For presentation purposes, a maximum of 10 presentation browsers can be created',
  拟创建的手机浏览器分身数量: 'Number of mobile browser browsers to be created',
  '每个浏览器分身都是一个相互隔离、互不干扰的浏览器运行实例，分身需要绑定浏览器指纹（必需）和IP地址（可选）后才能打开对应的浏览器，此处指模拟Android手机浏览器指纹':
    'Each browser is a browser running instance that is isolated and non-interfering with each other. The browser needs to bind the browser fingerprint (required) and IP address (optional) before opening the corresponding browser. This refers to simulating Android mobile browser fingerprint',
  浏览器分身命名前缀: 'Browser naming prefix',
  '系统将为您创建以“命名前缀”-“数字”作为名称的演示分身':
    'The system will create a presentation browser named "Naming Prefix"-"Number" for you',
  请输入浏览器分身命名前缀: 'Please enter the browser naming prefix',
  不能包含emoji表情符号: 'Cannot contain emoji emoticons',
  浏览器分身命名前缀最多20个字符: 'Browser naming prefix can be up to 20 characters',
  创建指定数量的浏览器分身: 'Create a specified number of browser browsers',
  为浏览器分身创建不同的浏览器指纹: 'Create different browser fingerprints for browser browsers',
  为浏览器分身采用本机IP直连: 'Use local IP direct connection for browser browser',
  浏览器分身创建成功: 'Browser created successfully',
  '现在将为您导航至某个分身的详情页面，您可以打开此分身所对应的花漾浏览器':
    'Now you will be navigated to the details page of a certain browser, and you can open the Huayang browser corresponding to this browser.',
  创建多个分身: 'Create multiple browsers',
  '我们将为您下载指纹模板、创建指纹实例、创建多个浏览器分身、为分身绑定指纹实例并开启本地代理（暨采用本机IP直连），方便您快速体验':
    'We will download a fingerprint template for you, create a fingerprint instance, create multiple browsers, bind the fingerprint instance to the browsers, and open a local proxy (and use local IP direct connection) to facilitate your quick experience',
  该动态IP不支持会话保持: 'This dynamic IP does not support session persistence',
  动态策略: 'Dynamic policy',
  '一旦开启会话保持特性，则在访问分身打开的花漾灵动的会话周期内，将尽可能保证动态IP不要切换（前提是动态IP要支持）':
    'Once the session hold feature is enabled, during the session period when the access browser opens, the dynamic IP will be ensured as much as possible not to switch (provided that dynamic IP supports it)',
  '会话保持是指：在一个新的浏览器会话创建时，获取一个动态IP的出口IP值，在此浏览器会话期间内，一直保持这个出口IP值不变':
    'Session retention refers to obtaining the egress IP value of a dynamic IP when a new browser session is created, and keeping the egress IP value unchanged during the browser session',
  分身批量导出: 'Batch export of browsers',
  分身导出: "Export in one's own body",
  '请选择拟导出到Excel表格的字段列表：':
    'Please select the list of fields you want to export to Excel:',
  所属站点: 'Site',
  店铺类型: 'Store type',
  本土店: 'Local store',
  跨境店: 'Cross-border store',
  '如果您的跨境店支持多个站点，您需要为其创建多个分身并分别指定不同的站点':
    'If your cross-border store supports multiple sites, you need to create multiple doppelgangers for it and designate different sites respectively',
  请选择所属站点: 'Please select your site',
  经营品类: 'Business categories',
  分身数量已经达到团队最大配额: "The number of browsers has reached the team's maximum quota",
  当前团队只允许创建: 'Currently, the team only allows creation',
  '个分身，无法创建新的分身，请联络官网客服咨询进一步信息':
    'You cannot create a new browser. Please contact the official website customer service for further information.',
  请联络官网客服咨询进一步信息:
    'Please contact the official website customer service for further information',
  批量创建分身: 'Create browsers in batches',
  新建分身: 'New browser',
  '请指定分身类型，系统将为您批量创建指定数量的分身':
    'Please specify the type of browser, and the system will create a specified number of browser for you in batches',
  '将您的电商平台、社交媒体等分身纳入到花漾灵动里管理':
    'Integrate your e-commerce platform, social media and other browsers into Huayang Smart Management',
  选择分身类型: 'Select the browser type',
  如无合适的分身平台请选择其它:
    'If you do not have a suitable browser platform, please choose another',
  输入基本信息: 'Enter basic information',
  店铺名称: 'Store name',
  浏览器指纹代表浏览器所在电脑的软硬件信息:
    'The browser fingerprint represents the software and hardware information of the computer where the browser is located',
  '输入登录信息，花漾会为您进行密码代填（登录信息会被高强度加密存储）':
    'Enter the login information and Huayang will fill in the password for you (the login information will be stored with high intensity encryption)',
  登录信息: 'Login information',
  打开花漾浏览器时的默认首页设置: 'Default home page settings when opening Huayang browser',
  浏览器首页: 'Browser home page',
  跟随浏览器分身缺省设置: 'Follow browser default settings',
  '浏览器分身缺省设置约定了新建一个浏览器分身的默认值，可通过{{action}}进行调整':
    'The default settings for browser stipulate the default values for creating a new browser browser, which can be adjusted through {{action}}',
  浏览器分身缺省设置: 'Default settings for browser browser',
  电商平台首页: 'E-commerce platform homepage',
  '针对不同电商平台，有不同的首页':
    'There are different home pages for different e-commerce platforms',
  浏览器检测页: 'Browser detection page',
  '浏览器检测页会对当前分身绑定的IP以及使用的加速通道进行各种检测，默认情况下建议使用此选项':
    'The browser detection page will perform various tests on the IP bound to the current browser and the acceleration channel used. This option is recommended by default',
  继续浏览上次打开的网页: 'Continue browsing the last page you opened',
  默认打开上次浏览器关闭前打开的网页:
    'The web page opened before the last browser was closed opens by default',
  最长512个字符: 'Maximum 512 characters',
  '凡能够看到此分身的用户均可查看备注，最长512字符':
    'Anyone who can see this browser can view the comments, which is 512 characters long',
  分身批量创建成功: 'Batch creation of browsers successfully',
  '对分身进行克隆，需要拥有以下几个权限项：IP列表查看权、IP新增、购买与删除权、对已有分身的属性设置权、对已有分身浏览器指纹的管理权、对已有分身IP地址的管理权':
    'To browser an browser, you need to have the following permissions: IP list viewing rights, IP addition rights, purchase and delete rights, attribute setting rights for existing browser, management rights for existing browser fingerprints, management rights for existing browser IP addresses',
  分身创建成功: 'The browser was created successfully',
  全新分身: 'A new browser',
  关闭向导页面后将直接进入分身详情页面:
    'After closing the wizard page, you will directly enter the browser details page',
  分身迁移: 'Separate migration',
  将其它浏览器中的环境信息迁移至花漾:
    'Migrate environmental information from other browsers to Huayang',
  重命名: 'Rename',
  冲突策略: 'Conflict policy',
  会更新表格内所有分身的冲突策略: 'Conflict policies for all browsers in the table are updated',
  IP名称: 'IP name',
  代理: 'Agent',
  否: 'No',
  自动: 'Automatic',
  批量设置分身的平台: 'Platform for setting up browser in batches',
  批量设置: 'Batch setting',
  '{{action}}成功': '{{action}} succeeded',
  为导入的分身批量打标签: 'Tag imported browsers in batches',
  '正在为您批量导入分身，请稍候...':
    'Browsers are being imported in batches for you. Please wait...',
  导入失败: 'Import failed',
  导入结果: 'Import results',
  成功: 'Successful',
  失败: 'Failure',
  '导入成功{{successCount}}个，失败{{errorCount}}个':
    '{{successCount}} imported successfully, {{errorCount}} failed',
  分身导入失败: 'Import of browser failed',
  测试结果: 'Test results',
  确认导入内容: 'Confirm imported content',
  未获取到IP值: 'IP value not obtained',
  请选择您要导入的分身: 'Please select the browser you want to import',
  选择模板类型: 'Select a template type',
  花漾通用模板: 'Flower Yang General Template',
  '支持对分身认证信息、Cookie数据以及绑定的IP地址进行批量导入（如果IP地址不存在则自动创建）':
    'Support batch import of browser authentication information, Cookie data, and bound IP addresses (automatically created if the IP address does not exist)',
  AdsPowerExcel模板: 'AdsPowerExcel template',
  '以AdsPower的账号导入/导出文件作为模板，支持一键导入AdsPower的账号列表':
    'Using AdsPower account import/export files as a template, support one-click import of AdsPower account list',
  暂未支持: 'Not supported yet',
  上传模板文件: 'Upload template file',
  '支持对分身、分身所属平台、认证信息、Cookie数据以及绑定的IP地址进行批量导入（如果IP地址不存在则自动创建，已存在则忽略）':
    'Support batch import of browsers, platforms to which they belong, authentication information, Cookie data and bound IP addresses (automatically created if the IP address does not exist, and ignored if it already exists)',
  '以AdsPower的分身导入/导出文件作为模板，支持一键导入AdsPower的分身列表；导入内容包括：分身名称、用户名、密码、Cookie数据等；如果IP基于Socks5、Http等代理协议，会自动创建IP':
    "Using AdsPower's browser import/export file as a template, it supports one-click import of AdsPower's browser list; the import content includes: browser name, user name, password, Cookie data, etc.; if the IP is based on proxy protocols such as Socks5 and Http, the IP will be automatically created",
  下载模板文件: 'Download template file',
  '正在解析Excel文件，请稍候...': 'Please wait while parsing Excel file...',
  请选择文件: 'Please select a file',
  选择导入内容: 'Select import content',
  批量导入分身: 'Import browsers in batches',
  通过Excel文件批量导入分身: 'Import browsers in batches through Excel files',
  不能在同一个团队中进行分身的联合运营或转让:
    'Joint operation or transfer of browsers cannot be carried out in the same team',
  分身的联合运营或者转让只能发生在不同的团队之间:
    'Joint operation or transfer of browsers can only occur between different teams',
  '根据您填写的信息，我们查询到受让方信息如下：':
    'According to the information you filled in, we found the following information of the transferee:',
  联系人手机号: "Contact person's mobile phone number",
  联系人昵称: 'Contact nickname',
  团队ID: 'Team ID',
  信息准确: 'Information is accurate',
  信息有误: 'Information is incorrect',
  联系人在指定团队中的权限不够:
    'The contact does not have enough privileges in the designated team',
  指定的联系人在指定的团队中必须是超管或者BOSS角色:
    'The specified contact must be in the supervisor or BOSS role in the specified team',
  您的信息输入有误: 'Your information entered incorrectly',
  '根据您的输入并没有查询到符合条件的信息，请确认您的输入是否准确':
    'No qualified information was found based on your input. Please confirm whether your input is accurate.',
  受让方: 'Transferee',
  合作方: 'Partners',
  联营: 'Associated',
  '为了确保{{target}}{{optTag}}操作的正确性，请填写':
    'To ensure the correctness of the {{target}} {{optTag}} operation, please fill in',
  信息: 'Information',
  请输入对方的手机号码: "Please enter the other party's mobile phone number",
  请输入联系人手机号: 'Please enter your contact phone number',
  '请输入{{tag}}团队的ID': 'Please enter the ID of the {{tag}} team',
  如何获得: 'How to get',
  '团队ID？': 'Team ID?',
  联系人在团队中必须是超管或者BOSS角色: 'Contacts must be super managers or bosses in the team',
  添加新的合作团队: 'Add a new partner team',
  无法在网页端中导入分身: 'Unable to import browsers into the web',
  '请下载花漾客户端，并在花漾客户端中完成分身的导入':
    'Please download the Huayang client and complete the import of browser in the Huayang client',
  正在为您检查分身配额: 'Checking your browser quota',
  新建浏览器分身: 'Create a new browser browser',
  新建无痕分身: 'Build a new traceless browser',
  批量创建浏览器分身: 'Create browsers in batches',
  批量导入浏览器分身: 'Batch import browsers',
  '创建时间：': 'Creation time:',
  '历史绑定分身次数：': 'Historical number of binding browsers:',
  IP类型: 'IP type',
  IP归属地: 'IP Home',
  该分身从未绑定过任何IP地址: 'This browser has never been bound to any IP address',
  IP绑定历史: 'IP binding history',
  '该IP当前的状态为失效，即便绑定后也无法正常使用，您可到IP详情中了解关于此IP的进一步信息':
    'The current status of this IP is invalid and cannot be used normally even after binding. You can go to the IP Details for further information about this IP',
  '该IP当前的状态为准备，即便绑定后也无法正常使用，您可到IP详情中了解关于此IP的进一步信息':
    'The current status of this IP is ready and cannot be used normally even after binding. You can go to the IP Details for further information about this IP',
  '确定要选择此IP吗？': 'Are you sure you want to select this IP?',
  只显示空闲IP: 'Only idle IPs are displayed',
  绑定历史: 'Binding history',
  '已选:': 'Selected:',
  '是否要对选择的IP按序轮流绑定？': 'Do you want to bind the selected IPs in turn?',
  您选择了: 'You have selected the',
  个分身: 'Browsers',
  '个IP，是否要对选中的IP进行按序轮流绑定？':
    'Each IP, do you want to bind the selected IP in turn?',
  请选择IP: 'Please select IP',
  选择IP地址: 'Select the IP address',
  没有符合条件的IP地址: 'No IP address matching the criteria',
  '缺少“查看IP列表”权限，无法获取IP列表':
    'Missing "View IP List" permission, unable to obtain IP list',
  正绑定此分身: 'Binding to this browser',
  当前绑定分身: 'Currently bound browser',
  缺少可用的IP地址: 'Missing available IP address',
  '当前团队尚未导入/购买IP地址，您需要先导入自有的IP地址或者采购平台提供的IP地址':
    'The current team has not imported/purchased IP addresses yet. You need to import your own IP address or the IP address provided by the procurement platform first.',
  查看IP地址: 'View IP address',
  选择IP: 'Select the IP',
  'TKShop（暨TikTok店铺账号）': 'TKShop (and TikTok store account)',
  'TKVideo（暨TikTok媒体端账号）': 'TKVideo (and TikTok media account)',
  '如果目前没有您希望的分身平台类型，请选择“其它”，并请通过在线客服反馈给我们':
    'If there is currently no type of browser platform you want, please select "Other" and please give us feedback through online customer service',
  不限平台: 'Unlimited platforms',
  '（意味着任意浏览器分身都可以执行此流程定义）':
    '(This means that any browser can execute this process definition)',
  '恭喜，浏览器克隆成功': 'Congratulations, the browser browser was successful',
  已将浏览器信息同步至此分身: 'Browser information has been synchronized to this browser',
  浏览器分身克隆: 'Browser browser',
  '我们为您生成了浏览器分身克隆链接，请您在希望采集指纹信息与Cookie数据的浏览器中访问下述链接':
    'We have generated a browser browser link for you. Please visit the following link in the browser where you want to collect fingerprint information and Cookie data.',
  '该链接10分钟之内有效，在分身克隆成功之前，请不要关闭此对话框':
    'This link is valid for 10 minutes. Please do not close this dialog box until the browser is successfully browserd',
  复制链接: 'Copy Link',
  '确定要关闭此对话框吗？': 'Are you sure you want to close this dialog box?',
  '如果克隆过程已经开始且尚未结束，请不要关闭此对话框':
    'If the cloning process has started and has not yet ended, do not close this dialog box',
  继续关闭: 'Continued closure',
  分身管理: 'Split management',
  '分身数量：': 'Number of browsers:',
  '只能输入英文字母、数字、下划线': 'Only English letters, numbers, and underscores can be entered',
  平台类型: 'Platform type',
  请选择平台类型: 'Please select platform type',
  保存成功: 'Saved successfully',
  修改平台: 'Modify a platform',
  '请输入分身描述以供备忘（凡能够看到此分身的用户均可查看备注内容），最长512字符':
    'Please enter a description of the browser for comment (anyone who can see this browser can view the comment content), with a maximum length of 512 characters',
  '分身属性会影响到访问此分身打开的花漾浏览器的参数设置，请谨慎修订':
    'The browser attribute will affect the parameter settings of the Huayang browser opened by this browser. Please revise it carefully',
  分身ID: 'Browser ID',
  最长128个字符: 'Maximum 128 characters',
  标识码: 'Identification code',
  '在花漾浏览器的任务栏图标上绘制标识符，帮助您更好的区分浏览器分身':
    'Draw identifiers on the taskbar icon of Huayang Browser to help you better distinguish between browser browsers',
  标识码底色: 'Identification code background color',
  分身类型: 'Browser type',
  无痕分身: 'Traceless browser',
  常规分身: 'Regular browser',
  无痕分身不保存Cookie数据: 'Traceless browsers do not store Cookie data',
  'Cookie是维持账号登录态的重要组成，针对Cookie数据，您可以备份、导入、也可以全部删除，一旦清空意味着相关登录态将被清理':
    'Cookies are an important part of maintaining account login status. For Cookie data, you can back up, import, or delete all of them. Once cleared, it means that the relevant login status will be cleared.',
  域名: 'Domain name',
  Cookie数量: 'Number of cookies',
  'Cookie 数据过多': 'Too much Cookie data',
  '选中的 Cookie 数据过多，建议您直接下载数据文件':
    'The selected Cookie data is too much. We recommend you download the data file directly',
  下载Cookie文件: 'Download Cookie file',
  继续文本展示: 'Continue text display',
  备份: 'Backup',
  导入: 'Import',
  退出: 'Exit',
  分身属性: 'Browser attribute',
  分享团队: 'Share team',
  无痕分身属性: 'Traceless browser attribute',
  敏感信息: 'Sensitive information',
  缓存数据: 'Cache data',
  网站密码: 'Website password',
  插件扩展: 'Plug-in extension',
  云端同步策略: 'Cloud synchronization strategy',
  资源加载策略: 'Resource loading strategy',
  首页与书签: 'Home Page and Bookmarks',
  私密网站: 'Private website',
  内置RPA流程: 'Built-in RPA process',
  其它访问策略: 'Other access strategies',
  GBS租户信息: 'GBS Tenant Information',
  '如果您希望由花漾浏览器为您进行密码代填，请在这里输入相应的链接与认证信息，目前仅支持Windows平台':
    'If you want Huayang browser to fill in your password for you, please enter the corresponding link and authentication information here. Currently, it only supports Windows platforms.',
  网站: 'Website',
  您设置的分身认证信息: 'The browser authentication information you set',
  您设置的关联邮箱平台: 'The associated email platform you set up',
  您设置的关联支付平台: 'The associated payment platform you set up',
  查看: 'Views',
  增加: 'Increase',
  内容不能为空: 'Content cannot be blank',
  该网站已存在: 'This website already exists',
  '针对一些敏感站点，可以控制是否显示其链接与标题':
    'For some sensitive sites, you can control whether to display their links and titles',
  标题栏显示文本: 'Title bar displays text',
  地址栏显示文本: 'Address bar displays text',
  默认加载: 'Default load',
  默认不加载: 'Not loaded by default',
  当图片大于: 'When the picture is greater than',
  KB禁止加载: 'KB disable load',
  请设置图片大小阈值: 'Please set a picture size threshold',
  KB时禁止加载: 'Disable loading when KB',
  '当打开该分身所对应的花漾浏览器在访问网站资源时针对视频与图片资源的加载策略，通过设置该策略能够有效降低IP或本地代理的流量':
    'When opening the loading policy for video and picture resources on the Huayang browser corresponding to the browser when accessing website resources, setting this policy can effectively reduce IP or local agent traffic',
  '请注意：此处并未展示所有分身的真实属性，只有当编辑保存后，才会按照您的输入修订所有的分身属性':
    'Please note: The real attributes of all browsers are not displayed here. Only after editing and saving will all browser attributes be revised according to your input.',
  加载视频: 'Loading video',
  加载图片: 'Load the picture',
  没有保存任何和此分身相关的敏感信息: 'No sensitive information related to this browser is kept',
  '可在此处保存和此分身相关的敏感信息，最长2048字符':
    'You can save sensitive information related to this browser here, with a maximum length of 2048 characters',
  '拥有“对已有分身的属性设置权”权限的用户才能查看/编辑敏感信息（超管与BOSS默认拥有此权限）':
    'Only users with the "Attribute Set Rights for Existing Browsers" permission can view/edit sensitive information (Hypervisors and Bosses have this permission by default)',
  '理论上，无痕分身不应保存任何缓存数据，但可根据使用场景适当调整，可在':
    'In theory, a traceless browser should not store any cached data, but it can be adjusted appropriately according to the usage scenario.',
  无痕分身缺省设置: 'Default settings for traceless browser',
  进行统一设置: 'Make unified settings',
  '确定要开启此选项的云端同步吗？':
    'Are you sure you want to turn on cloud synchronization with this option?',
  '无论是LocalStorage还是IndexedDB，一旦开启云端同步策略，意味着会消耗较大的带宽，且会导致打开花漾浏览器过慢':
    'Whether it is LocalStorage or IndexedDB, once the cloud synchronization policy is enabled, it will consume a large amount of bandwidth and cause the browser to be opened too slowly.',
  同步: 'Synchronization',
  不同步: 'Out of sync',
  '标识为“同步”的数据，打开浏览器时会将数据从云端下载到本地，关闭浏览器时会将本地的最新数据同步到云端，云端数据会占据网盘存储空间并可能产生花瓣的消耗':
    'Data marked as "synchronized" will be downloaded from the cloud to the local area when opening the browser. When closing the browser, the latest local data will be synchronized to the cloud. Cloud data will occupy the network disk storage space and may consume petals.',
  'Cookie数据维持着分身的各种状态，强烈建议必须要开启Cookie数据同步':
    'Cookie data maintains various states of the browser, and it is strongly recommended that you must turn on Cookie data synchronization',
  'LocalStorage是一种高阶的浏览器数据缓存技术，开启此选项意味着云端同步会消耗较高的带宽':
    'LocalStorage is a high-end browser data caching technology. Turning on this option means that cloud synchronization consumes higher bandwidth',
  LocalStorage数据: 'LocalStorage data',
  'IndexedDB是一种高阶的浏览器数据缓存技术，开启此选项意味着云端同步会消耗较高的带宽':
    'IndexedDB is a high-end browser data caching technology. Enabling this option means that cloud synchronization consumes higher bandwidth',
  IndexedDB数据: 'IndexedDB data',
  历史记录: 'History',
  历史记录是指访问各个网站留下的历史痕迹:
    'Historical records refer to the historical traces left by visiting various websites',
  团队全局书签: 'Team Global Bookmarks',
  '名为“团队全局书签”中的内容会在所有团队成员之间共享':
    'Content called "Team Global Bookmarks" is shared among all team members',
  个人私有书签: 'Personal Private Bookmarks',
  '名为“个人私有书签”中的内容会在同一个用户之间进行同步':
    'Content called "Personal Private Bookmarks" is synchronized between the same user',
  其它收藏夹: 'Other favorites',
  '“其它收藏夹”是指不在“团队收藏夹”、“个人私有书签”中的书签，会在所有能够访问到该分身的人员之间共享':
    '"Other Favorites" refer to bookmarks that are not in "Team Favorites" or "Personal Private Bookmarks" and will be shared among all people who have access to the browser',
  '如果要开启在不同客户端下的密码代填，则需要开启网站密码的同步':
    'If you want to enable password filling under different clients, you need to enable website password synchronization',
  备份Cookie数据: 'Backup Cookie data',
  'Cookie备份-': 'Cookie backup-',
  另存为: 'Save as',
  Cookie复制成功: 'Cookie copied successfully',
  '文件大小超过限制（最大为10MB）': 'File size exceeds limit (maximum 10MB)',
  '导入的Cookie数据文件过大，不在这里显示具体内容，点击确定后将直接导入到分身属性':
    'The imported Cookie data file is too large, and the specific content will not be displayed here. After clicking OK, it will be imported directly into the browser attribute.',
  读取文件失败: 'Failed to read file',
  清空已有Cookie: 'Clear existing cookies',
  暂不支持在: 'Temporarily not supported in',
  平台上的密码代填特性: 'Password filling feature on the platform',
  '会在后续版本中予以支持，请稍候': 'It will be supported in subsequent versions, please wait',
  '当花漾浏览器访问此登录链接时，会自动列出指定的用户名和密码':
    'When Huayang Browser accesses this login link, the specified user name and password will be automatically listed',
  '系统为您提供当前电商平台的默认登录链接，如果该链接与您的预期不符，您可以将其修订成正确的登录链接':
    'The system provides you with the default login link for the current e-commerce platform. If the link does not meet your expectations, you can revise it into the correct login link',
  '系统为您提供所选支付平台的默认登录链接，如果该链接与您的预期不符，您可以将其修订成正确的登录链接':
    'The system provides you with a default login link for the selected payment platform. If the link does not meet your expectations, you can revise it to the correct login link',
  '系统为您提供所选邮箱平台的默认登录链接，如果该链接与您的预期不符，您可以将其修订成正确的登录链接':
    'The system provides you with a default login link for the selected email platform. If the link does not meet your expectations, you can revise it to the correct login link',
  清除: 'Clear',
  '登录链接与用户名已经存在，确认要替换吗？':
    'The login link and user name already exist. Are you sure you want to replace it?',
  '当前输入的登录链接与用户名与系统中已有的记录重复，点击确认将替换旧有的记录':
    'The currently entered login link and user name duplicate existing records in the system. Clicking Confirm will replace the old records.',
  请输入用户名: 'Please enter your username',
  选择合作类型: 'Select the type of cooperation',
  分身转让: 'Transfer from one person',
  '将分身完全转让给其它团队，可以在转让过程中决定是否转让分身的指纹与IP地址，一旦转让完毕后，分身将不再归属于您':
    "If you completely transfer the browser to another team, you can decide whether to transfer the browser's fingerprint and IP address during the transfer process. Once the transfer is completed, the browser will no longer belong to you.",
  联合运营: 'Joint operation',
  '在免交密码的情况下，您当前的团队和其它指定团队一起联合运营分身，但分身的归属权依然在您手里，您也可以随时中断联合运营':
    'Without the password being waived, your current team and other designated teams jointly operate the browser, but the ownership rights of the browser are still in your hands, and you can also interrupt the joint operation at any time.',
  取消联营: 'Cancellation of joint ventures',
  '处于联营状态（含联营确认状态）的分身将中断和所有团队的联营；未处于联营状态的分身则自动忽略':
    'Browsers that are in joint venture status (including joint venture confirmation status) will be suspended from joining with all teams; browsers that are not in joint venture status will be automatically ignored',
  所选分身中有他人联营给您的分身:
    'Among the selected browsers, there is an browsers that are jointly assigned to you by others',
  请取消勾选这类分身后再进行操作: 'Please uncheck this type of browser before operating',
  '当前分身转让中，无法继续操作': 'The browser is currently being transferred and cannot continue',
  所选分身中有转让中的分身:
    'Among the selected browsers, there are browsers that are being transferred',
  请取消转让后再进行操作: 'Please cancel the transfer before proceeding',
  '当前分身绑定了IP池，请解绑后再操作':
    'The current browser is bound to the IP pool. Please unbind it before operating',
  所选分身中有绑定了IP池的分身:
    'Among the selected browsers, there are browsers bound to the IP pool',
  请解绑对应分身的IP池后再进行操作:
    'Please unbind the IP pool of the corresponding browser before proceeding',
  无法对联营分身进行转让: 'Unable to transfer the joint venture',
  '选中的分身中包含有联营分身，无法对联营分身进行转让':
    'The selected browser includes a joint browser, and the joint browser cannot be transferred',
  当前团队的花瓣余额不够: "The current team's petal balance is not enough",
  当前团队的花瓣余额必须大于0才能够转让分身:
    "The current team's petal balance must be greater than 0 to transfer browsers",
  无法对他人的联营分身进行联合运营: "Unable to jointly operate other people's joint ventures",
  '选中的分身中包含有他人联营给您的分身，您无法对该联营分身再次进行联合运营':
    'The selected browsers include browsers that are jointly affiliated to you by others. You cannot jointly operate the affiliated browsers again',
  '独占式访问下的分身，无法开启分身联营':
    'Clone pool cannot be opened for browsers under exclusive access',
  '请重新修订此分身的访问策略，将访问方式由独占式访问更改为协同式访问，只有协同式访问的分身才允许进行分身联营':
    'Please revise the access policy of this browser and change the access method from exclusive access to collaborative access. Only browsers with collaborative access are allowed to associate with browser',
  未绑定IP地址的分身无法进行联合运营:
    'Browsers without binding IP addresses cannot operate jointly',
  所选分身中有未绑定IP地址的分身:
    'The selected browsers have browsers that are not bound to IP addresses',
  '所谓联合运营是指将分身交给其它团队合作管理，因此，分身必须要绑定一个公网IP地址才能够联合运营，否则，客队无法正常访问此分身':
    'The so-called joint operation refers to handing over the browser to other teams for cooperation and management. Therefore, the browser must be bound to a public IP address before it can be jointly operated. Otherwise, the visiting team will not be able to access the browser normally.',
  分身要开启安全策略后才允许进行联合运营:
    'The browsers must activate a security policy before they are allowed to operate jointly',
  所选分身中有未开启安全策略的分身:
    'Among the selected browsers, there are browsers that have not activated security policies',
  '如果要对分身进行联合运营，需要事先开启分身的安全策略才允许进行联合运营':
    'If you want to conduct joint operations of browsers, you need to activate the security policy of the browsers in advance before allowing joint operations.',
  立即开启: 'Immediately turn on',
  填写受让方信息: 'Fill in the transferee information',
  填写合作方信息: 'Fill in partner information',
  明确策略: 'Clear strategy',
  转让分身的浏览器指纹: "Transfer your browser's browser fingerprint",
  转让分身的Cookie数据: 'Transfer of Cookie Data of the Browser',
  '（请确认有无其它敏感信息）': '(Please confirm if there is any other sensitive information)',
  转让分身的网站密码: "Transfer your browser's website password",
  '（请确认有无和分身无关的网站密码）':
    '(Please confirm whether there is a website password that has nothing to do with the browser)',
  '转让分身的 LocalStorage 与 IndexedDB': 'Transfer of LocalStorage and IndexedDB',
  '（其它缓存数据）': '(Other cached data)',
  同步分身的首页设置与快捷方式: 'Sync home page settings and shortcuts for browser',
  '（仅限于浏览器检测页中的快捷方式）': '(Limited to shortcuts on browser detection page only)',
  同步分身的插件扩展: 'Plug-in extension for synchronized browser',
  '（仅限于通过花漾插件市场中安装的插件）':
    '(Only available through plug-ins installed in Huayang Plug-in Market)',
  转让分身的IP地址: 'Transfer the IP address of the browser',
  '（请确认有没有绑定其它分身）': '(Please confirm whether you are bound to other browsers)',
  '针对分身的联营方，您可以指定一份“分身联营安全策略”，该联营安全策略在所有联营团队中共享，并将约束所有联营团队的所有人员的访问行为；您可以在联营的过程中随时修订针对“分身联营安全策略”':
    'For split joint parties, you can specify a "split joint security policy", which is shared among all joint teams and will restrict the access behavior of all personnel of all joint teams; you can revise the "split joint security policy" at any time during the joint operation.',
  复制当前分身的安全策略: 'Copy the security policy of the current browser',
  创建一份新的联营安全策略: 'Create a new consortium security policy',
  将绑定的IP地址一并联营给客队: 'Co-operate the bound IP address to the visiting team',
  '如果绑定的IP地址是平台IP，可将IP地址联营给客队，客队可以对此IP地址进行续费，但无法执行其它操作':
    'If the bound IP address is the platform IP, you can associate the IP address with the visiting team. The visiting team can renew the IP address, but cannot perform other operations',
  绑定的IP地址一并联营: 'The bound IP addresses are associated together',
  '（仅限于平台IP，适用于需要客队对IP续费的场景）':
    '(Limited to platform IP only, applicable to scenarios where visiting teams need to renew IP)',
  请再次确认是否要转让此分身: 'Please confirm again whether you want to transfer this browser',
  '1、所有访问分身的行为将被强行中断': '1. All access to the browser will be forcibly interrupted',
  '2、如果IP被转让，当对方确认后IP将从当前团队中移除':
    '2. If the IP is transferred, the IP will be removed from the current team after confirmation by the other party',
  '3、当对方确认后分身也将从当前团队中移除':
    '3. When the other party confirms, the browser will also be removed from the current team',
  '当前分身所绑定的主IP还绑定了其它分身，请先移除其它绑定关系再进行转让':
    'The primary IP bound to the current browser is also bound to other browsers. Please remove other binding relationships before transferring them',
  分身已取消联合运营: 'The browser has cancelled joint operations',
  '已经联营的分身被取消联营，未联营的分身自动忽略':
    'The browsers that have been affiliated are cancelled, and the browsers that have not been affiliated are automatically ignored',
  '分身转让中，等待接收方确认': 'Transfer of browser, waiting for confirmation from recipient',
  '您指定的联系人将收到一笔消息，一旦他确认后，该分身将从您的团队中移除':
    'The contact you designate will receive a message and once he confirms it, the browser will be removed from your team',
  '24小时内，如果您指定的联系人没有确认分身交接，本次分身转让将被置为失败，分身将重新置为正常状态':
    'Within 24 hours, if the contact person you designate does not confirm the handover of the browser, this transfer of browser will be deemed a failure and the browser will be reset to normal status',
  分身已处于联合运营的状态: 'The browsers are already in joint operation',
  '您指定的联系人将收到分身联营消息，当他确认后，就可以和您一起联合运营此分身':
    'The contact person you designate will receive a message from the browser alliance. After he confirms it, he can jointly operate the browser with you',
  '您指定的联合运营团队在访问此分身时，会使用此分身的指纹与IP地址，并受您指定的分身联营安全策略的约束，拥有足够的安全性':
    "When the joint operation team you designate accesses this browser, it will use the browser's fingerprint and IP address, and will be bound by the browser's consortium security policy you designate and have sufficient security.",
  '此分身的归属权依然是您，您可以随时中断与其它团队的联营行为':
    'The ownership rights of this browser are still yours, and you can interrupt your joint venture with other teams at any time.',
  立即修订分身联营安全策略: 'Immediately revise the security strategy for the split pool',
  联合运营与转让: 'Joint operation and transfer',
  批量分身联营与转让: 'Batch joint venture and transfer',
  '在免交密码的情况下与其它团队一起联合运营分身，或者将分身直接转让给其它团队':
    'Jointly operate browsers with other teams without requiring passwords, or transfer browsers directly to other teams',
  联营分身无法直接删除: 'Associate members cannot be deleted directly',
  '该分身处于联营状态，请停止联营后方可删除':
    'This branch is in joint venture status. Please stop joint venture before deleting it',
  '选中的分身存在联营分身，需停止联营后方可删除':
    'The selected browser has a pool browser. You need to stop pool before deleting it.',
  '分身会被解除IP绑定关系并移动到回收站中，7天后会被彻底删除，在此期间内可随时恢复':
    'The browser will be un-bound and moved to the recycle bin. It will be completely deleted after 7 days and can be restored at any time during this period',
  彻底删除: 'Completely delete',
  '确定要批量删除选定的分身吗？': 'Are you sure you want to batch delete the selected browsers?',
  确定要删除分身吗: 'Are you sure you want to delete the browser?',
  '正在为您批量删除选定的分身，请稍候...':
    'Please wait while the selected browser is being deleted in batches for you...',
  '正在为您删除分身，请稍候...': 'Please wait while you are deleting your browser...',
  '（已选': '(Selected',
  绑定IP: 'Bind IP',
  您需要在花漾客户端内完成IP隔离设置:
    'You need to complete IP isolation settings in Huayang client',
  打开客户端: 'Open the client',
  最近访问时间: 'Recent access time',
  请先选中1至多个分身: 'Please select 1 or more browsers first',
  一次最多打开30个分身: 'Open up to 30 browsers at a time',
  请重新选择您要打开的分身: 'Please re-select the browser you want to open',
  依据名称或IP检索: 'By name or IP',
  无法查看进一步信息: 'Unable to view further information',
  '该分身由他人分享给当前团队联合运营，您无法查看进一步信息':
    'This browser is shared by others to the current team for joint operation. You cannot view further information',
  联营中: 'Lianjianzhong',
  转让中: 'In the transfer of',
  自动化流程数据统计: 'Statistics',
  MM月DD日: 'MM-DD',
  抓取的主播达人: 'Live creators',
  抓取的视频达人: 'Video creators',
  抓取的普通用户: 'Regular users',
  经纪人私信数量: 'Backstage msgs',
  手机账号私信数量: 'Mobile msgs',
  官方账号私信数量: 'Official account msgs',
  员工绩效统计: 'Staff performance stats',
  已分配的达人: 'Assigned',
  已触达的达人: 'Sent',
  已建联的达人: 'Responded',
  '浏览器分身统计（': 'Account statistics (',
  经纪人筛选账号数量: 'Backstage filter accounts',
  '手机私信账号数量（正常）': 'Mobile accounts (normal)',
  '手机私信账号数量（封禁）': 'Mobile accounts (blocked)',
  经纪人私信账号数量: 'Backstage msgs',
  官方账号数量: 'Official accounts',
  子任务: 'Subtasks',
  读取最新私信: 'Read the latest private messages',
  同步联系人: 'Sync contacts',
  同步联系人至手机: 'Sync contacts to mobile phones',
  调度中: 'Scheduling',
  运行中: 'Running',
  已调度: 'Scheduled',
  任务类型: 'Task type',
  主要参数: 'Main parameters',
  '分身/手机账号': 'Browser/mobile account',
  等待调度: 'Awaiting scheduling',
  执行开始时间: 'Execution start time',
  确定要取消当前任务吗: 'Are you sure you want to cancel the current task',
  任务取消成功: 'Task cancelled successfully',
  请输入达人ID: 'Creator ID',
  关于任务池: 'About the task pool',
  '任务池是“创建任务”、“分配任务”、“执行任务”的一种调度机制，用来将海量任务分摊至若干个时间片段依次处理。':
    'The task pool is a scheduling mechanism for "creating tasks","assigning tasks", and "executing tasks". It is used to allocate a large number of tasks to several time slices for processing in turn.',
  '举例，您同时创建了向2000个达人发送手机私信的任务，但只有两部手机，如果不间断执行，手机账号很容易被封，此时，就需要通过任务池将这2000个私信任务分摊到不同的时间、不同的手机账号依次执行。':
    'For example, you have created a task to send mobile private messages to 2000 people at the same time, but there are only two mobile phones. If you execute it continuously, your mobile phone account can easily be blocked. At this time, you need to allocate these 2000 private message tasks through the task pool. Execution at different times and different mobile phone accounts in turn.',
  请选中后操作: 'Please select and operate',
  '确定要取消选中的任务吗？': 'Are you sure you want to cancel the selected task?',
  '任务一旦取消不可恢复，只能重新发起':
    'Once a task is cancelled, it cannot be restored and can only be restarted.',
  选中的任务已取消: 'The selected task has been cancelled',
  批量取消: 'Batch revoke',
  '确定要取消所有的任务吗？': 'Are you sure you want to cancel all tasks?',
  所有任务已取消: 'All tasks have been cancelled',
  全部取消: 'All cancelled',
  执行体列表: 'Executors',
  任务ID: 'Task ID',
  发起时间: 'Initiation time',
  达人列表: 'Creators',
  任务详情: 'Task details',
  '任务ID：': 'Task ID: ',
  执行设备: 'Device',
  结果汇总: 'Results are summarized',
  执行结束时间: 'Execution end time',
  只保留最近30天的历史任务: 'Only historical tasks from the last 30 days are retained',
  屏蔽成功: 'Block successful',
  屏蔽原因: 'Block reason',
  导入用户: 'Import',
  导入大哥: 'Import',
  状态已修改: 'Status has been modified',
  请指定状态: 'Please specify status',
  批量更改状态: 'Batch change status',
  更改状态: 'Change status',
  取消关注: 'Unfavorite',
  '关注/取消关注': 'Favorite/Unfavorite',
  '会更新该达人的头像、粉丝数、视频数、视频点赞数':
    "The creator's browser, number of fans, number of videos, and number of video likes will be updated",
  '个刷榜大哥进行信息更新：': 'The gifter who swipes the list updates the information:',
  '会更新该刷榜大哥的头像、粉丝数、视频数、视频点赞数':
    "Will update the gifter's browser, number of fans, number of videos, and number of video likes",
  '个普通用户进行信息更新：': 'Regular users update information:',
  '会更新该用户的头像、粉丝数、视频数、视频点赞数':
    "The user's browser, number of fans, number of videos, and number of video likes will be updated",
  达人AI分析: 'Creator AI analysis',
  '通过AI对达人进行分析与画像，受限于AI的能力，分析结果仅供参考':
    "Analyzing and portraying creators through AI is limited by AI's capabilities, and the analysis results are for reference only.",
  'AI分析的内容：': 'Content of AI analysis:',
  年龄: 'Age',
  性别: 'Gender',
  肤色: 'Skin color',
  可能的宗教信仰: 'Possible religious beliefs',
  露脸开播: 'Show your face in live',
  '只有达人正在直播时才能够进行此项的分析，建议您通过“正在开播的直播达人”入口进行分析':
    'This analysis can only be performed when the creator is streaming. I suggest you conduct the analysis through the "Live Creator Who is in Live" portal',
  带货能力: 'Ability to carry goods',
  '根据达人的视频类型进行分析，建议仅对从事电商业务的用户开启此选项':
    'Based on the analysis based on the video type of creator, it is recommended to only enable this option for users engaged in e-commerce business.',
  AI分析: 'AI analysis',
  导出达人至Excel: 'Export creators to Excel',
  '请选择字段：': 'Please select a field:',
  '导出达人形成@ID的文本': 'Export the text to form @ID',
  '形成@ID的文本后，您可以在发布视频的时候@这些达人':
    'After forming the text of @ID, you can @ these creators when publishing the video',
  '分隔符：': 'Separator:',
  换行符号: 'Line feed symbol',
  英文逗号: 'English comma',
  仅导出达人ID: 'Export only creator ID',
  未发送过任何私信: 'Never sent any private messages',
  已成功发送过私信: 'Successfully sent a private message',
  主播回复过私信: 'The creator replied to you',
  手工维护此状态: 'Manually maintain',
  所有人都可发送: 'Anyone can send',
  仅好友可发送: 'Only friends can send',
  不接受任何人的消息: 'Not accepting message from anyone',
  推荐的好友可发送: 'Recommended friends can send',
  当前状态: 'Status',
  全部: 'All',
  开通橱窗: 'Showcase',
  未开通: 'No',
  已开通: 'Yes',
  私信设置: 'PM set',
  新消息: 'New message',
  有新消息: 'Any news',
  无新消息: 'No new news',
  邀约类型: 'Elite invites',
  金牌邀约: 'Elite invitations',
  普通邀约: 'Regular invitation',
  自定义状态: 'Custom status',
  自定义等级: 'Custom level',
  不限送礼者等级: 'All gifter level',
  '送礼者等级>=': 'Gifter Level>=',
  '单场最高打赏>=': 'Highest tip >=',
  '历史总打赏>=': 'Total tip >=',
  多个条件是并且关系: 'AND-related',
  不限打赏主播: 'All creators',
  打赏的主播ID: 'Creator ID',
  已成功导入: 'Successfully imported ',
  个_空格: ' ',
  '您可以按“导入时间”进行排序，查看最近导入的':
    'You can sort by "import time" to view the most recently imported',
  '；如果您打了标签，可以按照标签进行筛选；如果执行了信息更新流程，则稍候会查看到':
    '; If you tag it, you can filter it according to the tag; if you perform the information update process, you will view the',
  的详情信息: 'Details of',
  按照导入时间筛选: 'Filter by import time',
  进一步了解什么是: 'Learn more about what is ',
  文本框直接导入: 'Import text boxes directly',
  'ID，支持英文逗号分隔或者每行一个':
    'ID, supporting English comma separation or one for each line',
  花漾Excel模板文件: 'Huayang Excel template file',
  您可以通过Excel文件批量导入: 'You can import in batches via Excel files ',
  及其UID: ' And its UID',
  为导入的: 'For the imported ',
  打上标签: ' Be taged',
  '请输入标签名称，多个标签通过逗号分隔': 'Please enter the tag name, separated by commas',
  打标签的目的是为了更好的筛选: 'The purpose of taging is for better screening',
  '执行一遍“信息更新”流程': ' Perform the "Information Update" process once',
  导入的: 'Imported',
  '只包含“': 'Contains only "',
  'ID”等基础信息，执行“信息更新”流程能够获取到达人的详细信息，此流程会使用到带有标签“信息更新”的浏览器分身':
    'ID "and other basic information. Detailed information about the person arriving can be obtained by executing the" Information Update "process. This process will use a browser with the tag" Information Update "',
  尚未进行过任何沟通: 'No data',
  '最近联系的账号：': 'Recently contacted accounts:',
  更改联系账号: 'Change contact account',
  分配联系账号: 'Assign contact account',
  '最近联系的手机账号：': 'Mobile account:',
  沟通记录: 'Communication record',
  自定义流水: 'Custom pipeline',
  不限流水: 'All coins',
  '历史最高小时流水：': 'Highest hourly coins in history:',
  最小流水: 'Min coins',
  最大流水: 'Max coins',
  '或者历史最高日流水：': 'Or the highest daily coins in history:',
  '或者历史最高周流水：': 'Or the highest weekly coins in history:',
  关键词: 'Keywords',
  '通过关键词检索视频并进行浏览，可以为空':
    'Retrieving videos by keywords and browsing, can be blank',
  视频观看数量: 'Video views',
  '收藏视频的概率，最小值0（暨0%，不收藏），最大值100（暨100%，必收藏）':
    'The chance of collecting a video, the minimum value is 0 (cum 0%, no collection), the maximum value is 100 (cum 100%, mandatory collection)',
  '“喂给”AI进行参考的视频评论的数量，系统会删除个别无意义的以及字符长度小于5个字符的评论，该数量越大，AI生成的评论越精准，但需要扣除的花瓣也会相应增长':
    'For the number of video comments "fed" to AI for reference, the system will delete individual meaningless comments and comments whose character length is less than 5 characters. The larger the number, the more accurate the comments generated by AI, but the petals that need to be deducted will also increase accordingly.',
  参考的评论数量: 'Number of reviews referenced',
  自定义评论列表: 'Custom comment list',
  '执行流程时会随机选取一条对视频进行评论，如存在多条自定义评论，用换行进行分隔；不指定自定义评论列表则使用ChatGPT生成评论':
    'When executing the process, a random comment will be selected to comment on the video. If there are multiple custom comments, they will be separated by line feed; if a custom comment list is not specified, ChatGPT will be used to generate comments',
  '当超过指定时长后，则终止养号流程':
    'When the specified period of time exceeds, the number maintenance process is terminated',
  养号时长: 'Number maintenance duration',
  请输入养号时长: 'Please enter the number maintenance duration',
  '（分钟）': '(minutes)',
  '已为您创建了一笔“TikTok手机账号养号”的任务':
    ' task of "TikTok Mobile Account Maintenance" has been created for you',
  执行养号流程: 'Maintenance account',
  '已为您创建＂分享名片＂任务': ' "Share Business Card" task has been created for you',
  '把选中大哥的名片分享给旗下主播，让旗下主播和刷榜大哥保持好的互动':
    'Share the business card of the selected eldest brother with your creators, so that your creators can maintain good interaction with the eldest brother who swipes the list',
  '把选中的名片分享给旗下主播，让旗下主播与其保持好的互动':
    'Share the selected business card with your creators so that your creators can maintain good interaction with them',
  旗下主播ID: 'Creator ID',
  请输入旗下主播ID: 'Please enter your creator ID',
  '请输入达人ID，支持英文逗号分隔或者每行一个':
    'Please enter the Creator ID, which supports English comma separation or one per line',
  请选择手机账号: 'Please select a mobile account',
  该手机账号一定要和旗下主播互为好友: 'This mobile account must be friends with the creator',
  经纪人: 'Backstage',
  官方号: 'Official',
  '手机：': 'Phone: ',
  用户ID: 'User ID',
  粉丝数: 'Followers',
  送礼者等级: 'Gifter level',
  认领人: 'Assigned',
  单场最高打赏: 'Highest tip',
  历史总打赏: 'Total tip',
  视频数: 'Videos',
  点赞数: 'Likes',
  最近交互时间: 'Recent interaction time',
  复制达人ID: 'Copy Creator ID',
  ID已复制到剪切板: 'ID copied to clipboard',
  在新标签页中查看刷榜大哥详情:
    'Check the details of the gifter who swipes the list in the new tab',
  打开此刷榜大哥的TikTok首页: 'Open the TikTok homepage of this gifter',
  屏蔽: 'Block',
  '是否删除当前用户？': 'Delete the current user?',
  该操作不可恢复: 'This operation is not recoverable',
  恢复: 'Recovery',
  关注: 'Favorite',
  '已取消认领“': 'Claim has been cancelled "',
  '”，将其归还至团队公海': '", return it to team\'s public creators',
  依据ID检索: 'Search by ID',
  '高级查询：': 'Advanced:',
  共设置了: '',
  个查询条件: ' query conditions set',
  高级查询: 'Advanced',
  最: '',
  小: 'Min',
  大: 'Max',
  '值（可为空）': ' (can be null)',
  只看团队关注的: 'Just look at what the team is focusing on',
  达人昵称: 'Nickname',
  请输入达人昵称: 'Enter nickname',
  粉丝数位于: 'Followers',
  '请指定标签（可多选）': 'Please specify a tag (multiple choices can be used)',
  标签条件: 'Tag condition',
  '当选择多个标签时，标签之间的关系': 'When selecting multiple tags, the relationship between tags',
  包含以上标签: 'Includes all selected tags',
  不包含以上标签: 'Excludes any of the selected tags',
  标签关系: 'Tag relationship',
  '当选择包含多个标签时，标签之间的关系':
    'When selecting multiple tags, the relationship between tags',
  视频及刷榜数据: 'Video data',
  '（多个区间查询条件之间为【或者】的关系）': "(Multiple query conditions are connected by 'OR')",
  送礼者等级位于: 'Gifter level',
  单场最高打赏大于: 'Highest tip',
  历史总打赏大于: 'Total tip',
  视频数量位于: 'Videos ',
  视频点赞数位于: 'Likes',
  交互及状态: 'Interaction and status',
  指定时间: 'Specified time',
  '（留空忽略此条件）': '(Leave blank to ignore this condition)',
  最近: 'Recent',
  目标达人数量及排序: 'Number and ranking of target creators',
  目标达人数量: 'Limit',
  符合上述条件的: '',
  笔达人: ' creators meeting the above conditions',
  符合上述条件的全部达人: 'All creators who meet the above conditions',
  排序字段: 'Sort field',
  排序顺序: 'Sort order',
  正序: 'Positive sequence',
  倒序: 'Reverse order',
  请至少设置一个筛选条件: 'Please set at least one filter criterion',
  查询: 'Query',
  '条件可以为空（但必须指定一个条件），多个条件之间是“并且”关系':
    'Conditions can be null (but one condition must be specified), and multiple conditions have a "and" relationship',
  '信息更新时间：': 'Information update time:',
  用户昵称: 'User nickname',
  粉丝数量: 'Followers',
  视频数量: 'Videos',
  视频点赞数: 'Likes',
  修改状态: 'Modified status',
  主播状态已修改: 'Creator status has been modified',
  历史打赏数据: 'Historical reward data',
  主播ID: 'Creator ID',
  开播时间: 'Live time',
  支付的打赏: 'Tip',
  直播间等级: 'Live room level',
  确定恢复选中的: 'Are you sure you want to restore the selected ',
  达人恢复成功: 'Creator recovered successfully',
  达人删除成功: 'Creator deleted successfully',
  已取消认领: '',
  '个达人，将其归还至公海达人': " creators has been unclaimed, return it to team's public creators",
  已取消分配: 'Allocation cancelled',
  取消分配: 'Unassign',
  批量恢复: 'Batch recovery',
  请选择达人: 'Please choose a creator',
  '是否删除当前达人？': 'Do you want to delete this creator?',
  '是否恢复当前达人？': 'Do you want to restore this creator?',
  '该达人将重新归类到“{{label}}”列表中':
    "This creator will be reclassified to the team's {{label}}",
  '已取消认领达人“': 'The claim of creator has been cancelled "',
  '”，将其归还至公海达人': '", return it to team\'s public creators',
  在新标签页中查看达人详情: 'View the details of the creator in the new tab',
  打开此达人的TikTok首页: "Open this creator's TikTok homepage",
  针对当前搜索条件下的所有达人进行操作: 'Operate for all creators under the current conditions',
  请至少选择一个达人: 'Please select at least one creator',
  '已屏蔽的达人将不会被再次抓取，也不会被纳入到信息更新等自动化流程（彻底删除后的达人如果被抓取将成为新的达人）':
    'The blocked creators will not be crawled again, nor will they be included in automated processes such as information updates (the completely deleted creators will become new creators if they are crawled)',
  橱窗: 'Showcase',
  流水及榜单: 'Coins and Rank',
  历史最高小时流水位于: 'Highest hourly coins',
  历史最高日流水位于: 'Highest daily coins',
  历史最高周流水位于: 'Highest weekly coins',
  历史最高人气榜位于: 'Highest popular rank',
  历史最高游戏榜位于: 'Highest game rank',
  历史最高观看人数位于: 'Highest viewers',
  最近交互轨迹: 'Recent interaction trajectory',
  达人ID: 'Creator ID',
  主播昵称: 'Nickname',
  最新人气榜: 'Popularity rank',
  根据获得的团队积分进行排名: 'Ranking based on team points earned',
  历史最高人气榜: 'Highest popular rank',
  最新游戏榜: 'Game rank',
  '根据收到的钻石数量以及每日观看直播的时长(以分钟为单位)进行排名':
    'Ranked based on the number of diamonds received and the daily duration of viewing the live (in minutes)',
  历史最高游戏榜: 'Highest game rank',
  最新小时流水: 'Hourly coins',
  '按每小时收到的钻石数获得排名，在每小时结束后会重置':
    'Ranking is based on the number of diamonds received per hour and will be reset at the end of each hour',
  历史最高小时流水: 'Highest hourly',
  最新日流水: 'Daily coins',
  '在每天24:00重置，根据自重置起收到的钻石数量排名':
    'Reset at 24:00 every day, ranked based on the number of diamonds received since reset',
  历史最高日流水: 'Highest daily',
  最新周流水: 'Weekly coins',
  '根据每周领取的钻石数量获得排名，每7天更新一次':
    'Rankings are obtained based on the number of diamonds received per week and are updated every 7 days',
  历史最高周流水: 'Highest weekly',
  最新观看人数: 'Viewers',
  历史最高观看人数: 'Highest viewers',
  查看更多: 'View more',
  未获取到交互轨迹: 'No interaction trajectory obtained',
  交互轨迹: 'Interactive trajectory',
  已手动标记为: 'Manually marked as',
  有: 'Have',
  '主播最近交互时间：': "Creator's recent interaction time:",
  最高日流水: 'Max daily coins',
  最高周流水: 'Max weekly coins',
  最高小时流水: 'Max hourly coins',
  已开通橱窗: 'Showcase',
  历史直播数据: 'Live history',
  人气榜: 'Popularity rank',
  游戏榜: 'Game rank',
  小时流水: 'Hourly coins',
  日流水: 'Daily coins',
  周流水: 'Weekly coins',
  观看人数: 'Viewers',
  手机账号属性: 'Account detail',
  所属手机: 'Phone',
  请选择所属手机: 'Please select mobile phone',
  修改手机: 'Change the phone',
  '可选，最多支持{{max}}个字符': 'Optional, supports up to {{max}} characters',
  全部客户端: 'Total client',
  手机名称: 'Phone name',
  花漾客户端: 'Huayang client',
  添加手机: 'Add phone',
  选择手机: 'Choose phone',
  '该手机没有与客户端连接，无法访问':
    'The phone is not connected to the client and cannot be accessed',
  不再检查: 'No longer check',
  新消息检查: 'Check new msgs',
  '检查手机中所有的TikTok账号是否有达人回复新消息，如有新消息会通过站内信和微信通知到指定人员':
    'Check whether all TikTok accounts on your mobile phone have anyone who replies to new messages. If there is any new message, you will notify the designated person through in-station letter and WeChat.',
  '（会查检该手机已登录的所有TikTok账号）':
    '(All TikTok accounts logged in to the mobile phone will be checked)',
  请设置工作时间范围: 'Please set the working time range',
  检查频率: 'Frequency',
  请设置检查频率: 'Please set the inspection frequency',
  每: 'Each',
  '（最小10分钟，最大1000分钟）': '(Minimum 10 minutes, maximum 1000 minutes)',
  微信接收人: 'Recipient',
  微信已绑定: 'Bound',
  微信未绑定: 'Not bound',
  '如何绑定？': 'How to bind?',
  您选择的手机正在执行其它流程: 'The phone you have selected is performing other processes',
  '您需要等待其它流程执行完毕后再执行手机账号同步流程，否则会导致流程执行失败':
    'You need to wait until other processes are completed before executing the mobile account synchronization process, otherwise the process execution will fail.',
  弹出手机: 'Eject phone',
  同步手机账号_: 'Sync mobile accounts_',
  '正在为您同步手机账号，请稍候...': 'Your mobile account is being synchronized, please wait...',
  '执行流程期间，请不要操作手机':
    'Please do not operate your mobile phone during the execution of the process',
  '1.在手机TikTok中登录的账号，已经同步到系统中':
    '1. The account logged in to mobile phone TikTok has been synchronized to the system',
  '2.系统中存在但手机中不存在的账号，已经从系统中删除':
    '2. The account that exists in the system but does not exist in the mobile phone has been deleted from the system',
  '执行流程遇到了问题，本次手机账号同步失败，请您查看相应的流程日志，如有需要，请找在线客服寻求帮助':
    'The execution process encountered a problem. The mobile account synchronization failed this time. Please check the corresponding process log. If necessary, please contact the online customer service for help.',
  同步手机账号结束: 'Synchronization of mobile accounts ends',
  同步手机账号: 'Sync mobile accounts',
  '该流程会将手机TikTok中已经登录的所有账号全部读取下来并同步到系统中，如果有新增账号，会添加；如果已有账号不存在，会自动删除':
    'This process will read all registered accounts in the mobile phone TikTok and synchronize them to the system. If there is a new account, it will be added; if the existing account does not exist, it will be automatically deleted',
  '请选择拟同步账号的手机：': 'Please select the mobile phone you want to sync with:',
  '（离线）': '(Offline)',
  '（在线）': '(Online)',
  您的名下没有已授权的手机: 'There is no authorized mobile phone in your name',
  '请咨询系统管理员，由其将手机分配给您':
    'Please consult your system administrator to assign your phone to you',
  未找到可用的同步手机账号流程: 'No available sync mobile account process found',
  您选择的手机已离线: 'The mobile phone you selected is offline',
  '已离线，请确保该手机已正常连接至花漾客户端':
    'It is offline. Please ensure that the phone is connected to the Huayang client normally.',
  '正在执行其它流程，这会导致检测账号可用性流程执行失败，请确保在该手机中结束其它所有流程后再重新执行检测账号可用性流程':
    'Other processes are being executed, which will cause the process of checking account availability to fail. Please ensure that all other processes are completed on this phone before re-executing the process of checking account availability',
  检测账号可用性_: 'Check account',
  检测账号可用性流程已触发: 'The process of checking account availability has been triggered',
  检测账号可用性: 'Check account',
  '选中的TikTok账号会对指定的目标达人发送私信，并根据发送私信的结果检测账号是否被封禁，如果被封禁，则会自动打上标签“账号封禁”，或者“私信过快”；反之，如果一切正常则会删除“账号封禁”、“私信过快”等标签':
    'The selected TikTok account will send a private message to the specified target person, and test whether the account has been blocked based on the result of sending the private message. If it is blocked, it will automatically be taged "Account Blocked" or "Private Message Too Fast"; On the contrary, if everything is normal, tags such as "Account Blocked" and "Private Message Too Fast" will be deleted',
  '该目标达人默认情况下只能接受3条私信，这意味着同一个待检测账号只能检测3次，超过3次后，需要您自行准备目标达人账号以供检测（设置目标达人私信状态为任何人均可发送）':
    "By default, this target person can only accept 3 private messages, which means that the same account to be tested can only be tested 3 times. After more than 3 times, you need to prepare your own target person account for testing (Set the target person's private message status to anyone can send)",
  请输入目标达人昵称: 'Please enter the nickname of the target person',
  '正在获取自动化流程配置....': 'Getting automated process configuration...',
  未找到可用的检测账号可用性流程: 'No available check account availability process found',
  跟随默认值: 'Follow defaults',
  该账号每日最多私信条数: 'The maximum number of private messages per day for this account',
  请输入每日最多私信条数: 'Please enter the maximum number of private messages per day',
  '在此处指定的私信数量，优先级高于系统设置中的配置':
    'The number of private messages specified here has a higher priority than the configuration in the system settings',
  依据名称检索: 'Search by name',
  编辑账号: 'Edit account',
  当前执行的流程: 'Process currently being implemented',
  单日最大私信条数: 'Max PM per day',
  消息检查: 'Message check',
  打开手机: 'Open the mobile phone',
  '确定要删除该手机账号吗？': 'Are you sure you want to delete this mobile account?',
  请选手机账号: 'Please select a mobile account',
  直播间流水: 'Live stream',
  进入直播间: 'Enter the live room',
  流水位于: 'The coins is located',
  人气榜位于: 'Popularity rank is located in',
  游戏榜位于: 'Game rank is located in',
  观看人数位于: 'Viewers are located in',
  点赞数位于: 'The likes are located in',
  请输入标签名称: 'Please enter the tag name',
  共找到: 'Found ',
  个带有此标签的浏览器分身: ' browsers with this tag',
  未找到任何带有此标签的浏览器分身: 'No browser browsers were found with this tag',
  正在查找带有此标签的浏览器分身: 'Looking for browser browsers with this tag',
  使用的分身标签: 'Browser tag',
  '运行“直播广场主播在线守候流程“流程':
    'Run the "Live Square Creator Online Waiting Process" process',
  到直播广场中抓取正在直播的主播: 'Go to the live square to grab the creators',
  直播间流水最低: 'Min coins',
  '（低于此值的主播将被忽略）': '(Creators below this value will be ignored)',
  请指定主播类型: 'Please specify the creator type',
  请指定邀约类型: 'Please specify the type of invitation',
  守候的主播数量: 'Max creators',
  最多200: 'Up to 200',
  直播广场在线守候: 'Live square waiting online',
  '运行“团队关注主播在线守候流程“流程':
    'Run the "Team Focus on Creator Online Waiting Process" process',
  查看当前团队已有主播是否在线开播: 'Check whether the current team has a creator online',
  团队关注在线守候: 'Team favorites waiting online',
  个小时后自动结束此流程: 'This process ends automatically in 1 hour',
  '为避免账号被封禁，运行一段时间后需要停止流程，如有需要，再次重启即可':
    'In order to avoid the account being blocked, the process needs to be stopped after a period of operation. If necessary, restart it again',
  当前团队未开启此功能: 'The current team does not enable this function',
  '请和在线客服联系，确认开启了正在开播的主播达人等相关功能':
    'Please contact the online customer service service to confirm that relevant functions such as the creators that are currently live creator have been activated',
  当前已经开启了主播在线守候流程: 'Currently, the creator online waiting process has been started',
  同时只能运行一份主播在线守候流程:
    'At the same time, only one creator online waiting process can be run',
  '找不到＂直播广场主播在线守候流程＂':
    'Unable to find the "Live Square Creator Online Waiting Process"',
  '找不到＂团队关注主播在线守候流程＂':
    'Can\'t find "Team pays favorites to the creator\'s online waiting process"',
  请前往系统设置绑定RPA执行设备: 'Please go to System Settings to bind RPA execution equipment',
  团队还没有绑定设备: 'The team has not bound the equipment yet',
  '已经在您指定的设备”': 'Already started process on the device you specified: ',
  '“中开启相关流程': '',
  '请注意，一个团队只能开启一个在线守候流程，当流程结束后，开播的主播将不再实时监管':
    'Please note that a team can only start one online waiting process. When the process ends, the creators who are starting the live will no longer be supervised in real time.',
  '确定要停止主播在线守候流程吗？':
    "Are you sure you want to stop the creator's online waiting process?",
  '系统会停止相关流程，关闭相应的浏览器窗口':
    'The system will stop the relevant process and close the corresponding browser window',
  您还没有开启主播守候任务: 'You have not activated the creator waiting task yet',
  开启在线守候流程: 'Start the online waiting process',
  停止在线守候流程: 'Stop the online waiting process',
  当前搜索条件未查询到达人: 'The current search criteria did not find the person who arrived',
  正在等待流程汇报开播的主播: 'Waiting for the process report creator',
  '请稍候，流程正在抓取符合您要求的且正在开播的主播':
    'Please wait, the process is grabbing creators',
  正在开播的主播达人: 'The creator who is in live',
  '您可以通过开启“主播在线守候”的相关流程，抓取当前正在直播的主播达人，并进入其直播间与其互动。':
    'You can start the relevant process of "Creator Online Waiting" to capture the current live creators and enter their live room to interact with them.',
  分配用户: 'Assign user',
  请选择主播: 'Please select an creator',
  打开此用户的TikTok首页: "Open this user's TikTok home page",
  复制用户ID: 'Copy user ID',
  '已取消认领用户“': 'The user has been cancelled "',
  用户类型: 'User type',
  用户私信设置已修改: 'User private message settings have been modified',
  用户状态已修改: 'User status has been modified',
  请输入用户昵称: 'Please enter user nickname',
  视频数及点赞数: 'Number of videos and likes',
  目标用户数量及排序: 'Number of target users and ranking',
  目标用户数量: 'Number of target users',
  笔用户: 'Pen user',
  符合上述条件的全部用户: 'All users who meet the above conditions',
  '个用户吗？': ' user?',
  恢复成功: 'Recovery success',
  '个用户，将其归还至团队公海': " users return to the team's public creators",
  请选择用户: 'Please select the user',
  达人私信设置已修改: 'Creator private message settings have been modified',
  达人状态已修改: 'Creator status has been modified',
  没有更多轨迹: 'No more trajectories',
  加载更多轨迹: 'Load more traces',
  当前检索条件下无数据: 'No data under current search conditions',
  交互行为: 'Interactive behavior',
  交互时间: 'Interaction time',
  检索结果: 'Search result',
  轨迹数量: 'Number of traces',
  交互内容: 'Interactive content',
  交互状态: 'Interactive state',
  '服务器升级中，预计一小时后完成，请稍候':
    'Server upgrade is underway and is expected to complete in one hour. Please wait',
  '热线电话：{{hotline}}': 'Tel: {{hotline}}',
  切换至PC版: 'Switch to PC version',
  注册新用户: 'Registering new users',
  邀请码: 'Invitation code',
  '系统检测到此微信号并没有绑定任何账号，请输入您的昵称，系统将为您默认创建一个新用户':
    'The system detects that this WeChat signal is not bound to any account. Please enter your nickname, and the system will create a new user for you by default.',
  '密码重置成功，请重新登录': 'Password reset successfully, please log in again',
  重置密码: 'Reset your password',
  返回: 'Return',
  请验证您的手机号码或邮箱后重置您的密码:
    'Please verify your mobile number or email and reset your password',
  通过手机号找回密码: 'Recover password through mobile phone number',
  通过邮箱找回密码: 'Recover password via email',
  请输入手机号: 'Please enter your mobile number',
  请输入邮箱: 'Please enter email',
  请输入您的新密码: 'Please enter your new password',
  '长度为8~40个字符，需至少包含以下四类字符中的三类：大写字母、小写字母、数字、特殊符号':
    'The length is 8 to 40 characters and must contain at least three of the following four types of characters: upper case letters, lower case letters, numbers, and special symbols',
  请输入重复密码: 'Please enter a duplicate password',
  账号密码: 'Account password',
  '手机号码既是您的登录账号，请为您的登录账号设置一个密码；登录系统后，可绑定微信，下次可通过微信扫码登录':
    'The mobile phone number is your login account. Please set a password for your login account. After logging in to the system, you can bind to WeChat, and you can scan the code next time to log in through WeChat',
  新密码: 'New password',
  确认密码: 'Confirm Password',
  '该手机号已经绑定其它账号，不可重复绑定':
    'This mobile phone number has been bound to another account and cannot be bound repeatedly',
  '该邮箱已经绑定其它账号，不可重复绑定':
    'This mailbox has been bound to another account and cannot be bound again',
  您的信息: 'Your information',
  '切换至{{type}}': 'Switch to {{type}}',
  请输入您的邮箱: 'Please enter your email',
  请阅读并同意协议: 'Please read and agree to the agreement',
  阅读并接受: 'Read and accept',
  您需要下载花漾指纹浏览器客户端App才能够获得花漾指纹浏览器的所有功能:
    'You need to download the Huayang Fingerprint Browser client App to get all the functions of Huayang Fingerprint Browser',
  立即下载: 'Download now',
  您的昵称: 'Your nickname',
  进入团队: 'Entering the team',
  等待团队审核: 'Waiting for team review',
  '您的团队加入申请已提交，正在等待团队审核，审核通过后，会以短信形式通知到您刚才注册使用的手机':
    'Your application to join the team has been submitted and is waiting for team review. After the review is approved, the mobile phone you just registered for will be notified by text message',
  您的申请被拒绝: 'Your application was rejected',
  '请您联络邀请您加入团队的好友，向其咨询进一步信息':
    'Please contact the friends who invited you to join the team and ask them for further information',
  进入我的团队: 'Join my team',
  您已经在当前团队中了: 'You are already in the current team',
  '您无需重复加入同一个团队，请检查您的输入是否正确，如有疑问请联系在线客服':
    "You don't need to join the same team again. Please check whether your input is correct. If you have any questions, please contact online customer service.",
  团队加入邀请: 'Team Join Invitation',
  您的好友邀请您加入到Ta的团队中一起协作:
    "Your friends invite you to join Ta's team and collaborate together",
  团队创建者: 'Team creators',
  '请使用微信扫一扫完成注册/登录': 'Please use WeChat to scan to complete registration/login',
  登录成功: 'Login success',
  '请使用Kakao Talk扫一扫完成注册/登录':
    'Please use Kakao Talk to scan to complete registration/login',
  验证码错误: 'Captcha error',
  请输入您的登录密码: 'Please enter your login password',
  '使用{{oauthType}}账号登录': 'Sign in using {{oauthType}} account',
  '系统会打开本机默认浏览器访问{{oauthType}}网站，请您在打开的浏览器中完成{{oauthType}}账号的登录':
    'The system will open the local default browser to visit the {{oauthType}} website. Please complete the login of the {{oauthType}} account in the opened browser',
  第三方账号登录: 'Third-party account login',
  立即注册: 'Sign up',
  '7天内自动登录': 'Automatic login within 7 days',
  '忘记密码？': 'Forgot password?',
  手机登录: 'Mobile phone',
  邮箱登录: 'Email',
  账户或密码错误: 'Wrong account or password',
  密码登录: 'Password',
  验证码快捷登录: 'SMS code quick login',
  手机注册: 'Register with Phone Number',
  邮箱注册: 'Register with Email',
  注册失败: 'Registration failure',
  '已有账号，直接登录': 'Existing account, log in directly',
  关于邀请码: 'About the invitation code',
  '为保证广大卖家的安全运营，花漾指纹浏览器采取邀请注册制，必须获得邀请码才能成功注册，请向您的推荐人咨询进一步信息，或者通过右侧的微信公众号、400电话获取邀请码':
    'In order to ensure the safe operation of sellers, Huayang Fingerprint Browser adopts an invitation registration system. You must obtain an invitation code to successfully register. Please consult your recommender for further information, or obtain the invitation code through the Weixin Official Accounts and 400 phone number on the right',
  微信公众号: 'Weixin Official Accounts',
  '请扫码关注我们的公众号，': 'Please scan the code and follow our Wechat.',
  '点击菜单“邀请码”即可获取': 'Click on the menu "Invitation Code" to get it',
  '请拨打我们的热线电话，': 'Please call our hotline to',
  通过人工客服即可获取邀请码: 'You can obtain the invitation code through manual customer service',
  关于邀请码的使用说明: 'Instructions for using invitation codes',
  '如果您是通过我们的合作伙伴介绍而来，强烈建议您使用您的介绍人给予的邀请码完成注册，这会给您和您的介绍人带来后续相关的商务便利；':
    'If you are introduced through our partners, we strongly recommend that you use the invitation code given by your introducer to complete the registration, which will bring subsequent related business convenience to you and your introducer;',
  '如果您并不知晓相关信息，现阶段可使用花漾热线电话':
    'If you do not know relevant information, you can use the Huayang hotline at this stage',
  作为邀请码: 'As an invitation code',
  设置登录密码: 'Set login password',
  '手机号码/邮箱即是您的登录账号，建议您设置一个登录密码':
    'Mobile phone number/email is your login account. It is recommended that you set a login password.',
  尾号: 'Tail number',
  的用户: 'User',
  设置密码: 'Set a password',
  请输入登录密码: 'Please enter your login password',
  请输入邀请码: 'Please enter the invitation code',
  '请输入邀请码（': 'Please enter the invitation code (',
  必填: 'Required',
  可选: 'Optional',
  编辑小红书账号: 'Edit Xiaohongshu account',
  创建小红书账号: 'Create Xiaohongshu account',
  请输入账号名称: 'Please enter account name',
  账号类型: 'Account type',
  社媒账号: 'Social media accounts',
  辅助账号: 'Auxiliary account',
  小红书账号: 'Xiaohongshu account',
  关注数: 'Followers',
  获赞与收藏: 'Praise and Collection',
  打开小红书账号: 'Open Xiaohongshu account',
  名称已复制到剪切板: 'Name copied to clipboard',
  '是否删除当前账号？': 'Delete the current account?',
  发布新的内容: 'Publish new content',
  送礼者等级及视频数据: 'Gifter rating and video data',
  日常养号: 'Daily number maintenance',
  图文加热: 'Picture heating',
  '选择文件（夹）': 'Select file (folder)',
  RPA任务: 'RPA task',
  审计录像: 'Audit video',
  分身数据: 'Browser data',
  '"文件内无文件可下载': '"There are no files in the file to download',
  您要下载的文件包含文件夹: 'The file you want to download contains folders',
  '请下载花漾客户端并通过花漾客户端完成文件的上传和下载，这样会带来更好的传输速率与操作体验':
    'Please download the Huayang client and complete the upload and download of files through the Huayang client, which will bring better transfer rates and operating experience.',
  '文件体积过大，不支持预览': 'File size is too large and preview is not supported',
  '文件体积大于20M无法预览，请您下载后再自行查看':
    'File size larger than 20M cannot be previewed. Please download it and then view it yourself.',
  不支持预览的文件类型: 'File types that are not supported for preview',
  '该文件类型不支持预览，请您下载后再自行查看':
    'Preview is not supported for this file type. Please download it and then view it yourself.',
  '客户端版本不支持该功能，请升级客户端':
    'The client version does not support this feature. Please upgrade the client',
  浏览器: 'Browser',
  检测超时: 'Detection timeout',
  '花漾客户端没有启动/安装': 'Huayang client is not started/installed',
  '正在唤醒花漾客户端，请稍候...': 'Waking up Huayang client, please wait...',
  '系统正在为您唤醒花漾客户端使其按照您的指令行事，唤醒成功后此对话框会自动消失':
    'The system is waking up the Huayang client for you to make it follow your instructions. This dialog box will automatically disappear after the awakening is successful',
  花漾客户端当前登录者的身份不一致:
    'The identities of the current registrants on Huayang client are inconsistent',
  '花漾客户端当前登录者的身份与您的身份不一致，如果确信要切换登录者身份，请点击“重新登录”，请注意，此举会中断旧有登录者当前的工作':
    'The identity of the current login on Huayang client is inconsistent with yours. If you are sure you want to switch the identity of the login, please click "Log in again". Please note that this will interrupt the current work of the old login.',
  重新登录: 'Re-login',
  '花漾客户端没有启动（或没有安装）': 'Huayang client is not started (or not installed)',
  '花漾客户端没有启动或者没有安装，如果您已经正确安装，请点击“打开花漾客户端”，否则，请下载并重新安装花漾客户端':
    'The Huayang client has not been started or installed. If you have installed it correctly, please click "Open Huayang Client". Otherwise, please download and reinstall Huayang Client',
  打开花漾客户端: 'Open Huayang client',
  下载: 'Download',
  未能解析teamId: 'Failed to resolve teamId',
  分: 'Points',
  请输入正确的IPv4地址: 'Please enter the correct IPv4 address',
  请输入正确的手机号: 'Please enter the correct mobile phone number',
  '您可能需要输入国际电话区号，例如+1':
    'You may need to enter an international telephone area code, such as +1',
  请输入正确的邮箱: 'Please enter the correct email address',
  请输入正确的URL: 'Please enter the correct URL',
  '不能包含？或*': "Can't include? or *",
  长度不能大于128个字符: 'The length cannot be greater than 128 characters',
  请输入站点: 'Please enter site',
  请输入正确的站点: 'Please enter the correct site',
  请输入您的: 'Please enter your',
  '（不小于3个字符）': '(No less than 3 characters)',
  请输入正确的端口号: 'Please enter the correct port number',
  您输入的变量不合法: 'The variable you entered is illegal',
  请输入正确的IPv6地址: 'Please enter the correct IPv6 address',
  请输入正确的IP地址: 'Please enter the correct IP address',
  请输入正确的手机号或邮箱: 'Please enter the correct mobile phone number or email address',
  全部收起: 'Collapse all',
  全部展开: 'Expand all',
  将所选达人分配给下属员工: 'Assign',
  '（员工可在“分配给我的”达人中查看）': '(Employees can be viewed in the "Assigned to Me" list)',
  将所选达人取消分配: 'Unassign the selected creator',
  '（达人将回归至公海）': "(Creator will return to the team's public creators)",
  '您希望对这些{{label}}：': 'You would like to provide these {{label}}:',
  '您希望对前{{total}}个{{label}}：': 'You want to check the first {{total}} {{label}}:',
  '当前条件下，共检索出{{total}}个{{label}}':
    'Under current conditions, a total of {{total}} {{label}} were retrieved',
  '全量操作一次最多操作{{limit}}个{{label}}':
    'Maximum of {{limit}} {{label}} can be operated at one time',
  '一次最多导出{{total}}个{{label}}': 'Export up to {{total}} {{label}} at a time',
  '请确认是否继续导出前{{total}}个{{label}}':
    'Please confirm whether to continue exporting the first {{total}} {{label}}',
  '对筛选出的{{label}}的信息进行更新': 'Update the information of the filtered {{label}}',
  '系统会对所选的{{total}}个{{label}}进行信息更新：':
    'The system will update the information of the selected {{total}} {{label}}:',
  '1.会更新{{label}}的头像、粉丝数、视频数、视频点赞数':
    "1.{{label}}'s browser, fan count, video count, and video likes count will be updated",
  '2.如果{{label}}正在直播，会更新该场直播的观看人数、流水':
    '2. If {{label}} is in live, the number of viewers and coins will be updated',
  '确定屏蔽搜索出的 {{count}} 个{{label}}吗？':
    'Are you sure you will block the {{count}} {{label}} found?',
  刷榜大哥: 'Gifter',
  指定关注数: 'Specify followers',
  不限关注数: 'Unlimited followers',
  最小关注数: 'Min followers',
  最大关注数: 'Max followers',
  手机账号关注: 'Mobile follow',
  手机账号视频分享: 'Video share',
  检测手机账号可用性: 'Check mobile account availability',
  全部账号: 'All accounts',
  依据时区关键词检索: 'Search based on time zone keywords',
  普通用户: 'Normal users',
  视频达人: 'Video creators',
  直播达人: 'Live creators',
  尚未创建: 'Not yet created',
  标签可用于对: 'Tags can be used to identify',
  '进行标记、分类、筛选等用途': 'For taging, classification, screening, etc.',
  标签中的数字代表关联的: 'The number in the tag represents the associated ',
  数量: '',
  '没有找到 “{{bizCode}}” 对应的流程': 'No process corresponding to "{{bizCode}}" was found',
  '流程 “{{name}}” 不需要额外指定输入变量':
    "Process '{{name}}' does not require additional input variables to be specified",
  执行流程: 'Execution flow',
  '执行 KOL 内置的一些流程': 'Implement some processes built into KOL',
  选择流程: 'Selection process',
  现阶段为您提供以下自动化流程:
    'At this stage, we provide you with the following automated processes',
  浏览器自动化流程: 'Browser automation process',
  直播间加热: 'Heating in the live room',
  通过若干浏览器账号对直播间进行加热: 'Heating the live room through several browser accounts',
  视频加热: 'Video heating',
  通过若干浏览器账号对指定的视频进行加热:
    'Heating the specified videos through several browser accounts',
  '手机 App 自动化流程': 'Mobile App Automation Process',
  通过若干手机账号对直播间进行加热: 'Heating the live room through several mobile accounts',
  通过若干手机账号对指定的视频进行加热: 'Heating specified videos through several mobile accounts',
  选择执行体: 'Select executor',
  请选择您要执行流程的分身: 'Please select the browser you want to perform the process',
  请选择您要执行流程的手机账号:
    'Please select the mobile account you want to perform the process on',
  输入变量: 'Input variables',
  任务调度已传递给手机: 'Task schedule has been passed to mobile phone',
  任务调度已传递给本地客户端: 'Task schedule passed to local client',
  任务准确就绪: 'The task is exactly ready',
  '任务准确就绪，点击"完成“将为您执行任务':
    'The task is exactly ready, and clicking "Finish" will execute the task for you',
  任务池可容纳的最大任务数量: 'The maximum number of tasks the task pool can hold',
  任务池当前已有任务: 'Task pool Currently existing tasks',
  本次操作拟创建任务: 'Task to be created in this operation',
  实际创建任务: 'Actual creation task',
  任务池: 'Task pool',
  '正在为您创建“{{type}}”任务': 'Creating a "{{type}}" task for you',
  '已为您创建“{{type}}”任务': ' "{{type}}" task has been created for you',
  '关闭（': 'Close (',
  '确定要删除此代金券吗？': 'Are you sure you want to delete this voucher?',
  贴子数: 'Number of posts',
  最近同步时间: 'Latest synchronization time',
  打开Instagram账号: 'Open Instagram account',
  复制用户名称: 'Copy user name',
  Instagram账号基本信息: 'Instagram account basic information',
  打开此达人的Instagram首页: "Open this creator's Instagram page",
  达人名称: 'Creator name',
  帖子数量: 'Number of posts',
  达人范围: 'Range',
  公海的达人: 'All creators',
  团队关注的达人: 'Favorited creators',
  已建联: 'Responded',
  '暂无数据，请确认管理员是否有给您分配达人':
    'No data yet. Please confirm whether the administrator has assigned you a creator.',
  唤醒Instagram: 'Wake up Instagram',
  'Instagram唤醒方式：': 'How to wake up on Instagram:',
  Instagram协议唤醒: 'Instagram protocol wake-up',
  直接唤醒Instagram: 'Wake up Instagram directly',
  唤醒Instagram时自动将达人设置为已触达:
    'Automatically set the creator as reached when you wake up Instagram',
  TikTok唤醒页面: 'TikTok wake-up page',
  达人首页: 'Top page',
  达人直播间: 'Creator Live Room',
  '（取前{{count}}个）': '(Take the first {{count}})',
  分配给我的: 'Mine',
  '当前条件可查询到 {{count}} 个达人': 'You can find {{count}} people for the current conditions',
  '取筛选过的前 {{count}} 个': 'Take the first {{count}} filtered',
  '共{{count}}个筛选条件': 'There are {{count}} filters in total',
  '普通员工可以创建”我的自动计划“，但创建的自动计划只能针对“分配给我的达人”':
    'Employees can create "my automated plans", but the automated plans you create can only be targeted at "people assigned to me"',
  '系统自动计划只能由超管或BOSS创建，可针对全部的公海达人，以及全部的经纪人账号与手机账号；普通员工可以创建自己的自动计划':
    'System automatic plans can only be created by supermanagers or bosses, and can target all high-seas creators, as well as all backstage accounts and mobile accounts; Employees can create their own automatic plans',
  建联手段: 'Invite method',
  养号目标: 'Goal',
  账号权重: 'Wt.',
  单账号养号时长: 'Duration',
  评论话术: 'Commentary',
  自定义话术: 'Custom script',
  AI生成: 'AI-generated',
  观看视频数量: 'Videos view',
  分享视频位置: 'Share video location',
  '第{{index}}个': '{{index}}',
  守候手段: 'Means of waiting',
  更新手段: 'Update means',
  待检测账号: 'Account to be tested',
  '{{total}} 个': '{{total}}',
  重复周期: 'Repeat  period',
  创建新的自动计划: 'Create a new auto plan',
  '官方{{addOn}}账号': 'Official {{addOn}} account',
  '经纪人{{addOn}}账号': 'Backstage {{addOn}} account',
  '普通{{addOn}}账号': 'Browser {{addOn}} account',
  我的自动计划: 'My auto plan',
  手机视频分享: 'Video share',
  '修改{{who}}自动计划': 'Modify the {{who}} auto plan',
  我的: 'My',
  '创建{{who}}自动计划': 'Create {{who}} auto plan',
  手机私信账号: 'Mobile phone private message account',
  您可以针对不同的手机账号使用不同的邀约话术:
    'You can use different script for different mobile accounts',
  '您可以针对不同的经纪人账号使用不同的邀约话术（请注意，仅针对打有标签“{{tag}}”的浏览器分身才有作用）':
    'You can use different script for different backstage accounts (please note that it only works for browser browsers marked with "{{tag}}")',
  '指定话术分组  ：': 'Specify script groups:',
  请指定分组: 'Please specify grouping',
  每台手机账号数量: 'Number of accounts per mobile phone',
  手机数量: 'Number of mobile phones',
  '您只需输入{{label}}和所选日期，系统会根据左边的配置参数，自动计算出来一天最多可发送的私信数量':
    'You only need to enter {{label}} and the selected date, and the system will automatically calculate the maximum number of private messages that can be sent in one day based on the configuration parameters on the left',
  全天候: 'All-weather',
  私信发送过快时的标签: 'Tag when PM sent too fast',
  账号被封控时的标签: 'Temp blocked tag',
  账号被封禁时的标签: 'Blocked tag',
  账号未登录时的标签: 'Not logged in tag',
  需要调整隐私设置时的标签: 'Privacy issue tag',
  只有在蓝色方块的时间内才允许执行邀约建联相关流程:
    'The relevant procedures for inviting are allowed only during the time specified in the blue square',
  '如果有多个经纪人账号可以发送私信，最多同时允许多少个经纪人账号同时发送，建议设置为1，避免出现私信过快等情况，如果设置为0则不限制':
    'If there are multiple backstage accounts that can send private messages, the maximum number of backstage accounts allowed to send private messages at the same time is recommended to set it to 1 to avoid situations such as too fast private messages. If it is set to 0, there is no limit.',
  '每批最多可以发送多少私信，发送完后需要间隔一定时间才允许继续发送':
    'The maximum number of private messages can be sent in each batch, and a certain interval will be needed after sending before sending can be continued',
  '发送完一批私信后，需要间隔多长时间才允许下一批次的发送':
    'After sending a batch of private messages, how long does it take to allow the next batch to be sent?',
  每一个经纪人账号每天最多可以发送多少私信:
    'How many private messages can each backstage account send per day?',
  '每次执行经纪人账号后台私信的邀约建联流程时，会从含有此标签的浏览器分身中选取':
    'Every time you execute the solicitation process of a private message from your backstage account background, you will select it from the browser containing this tag.',
  '当发送私信过快时，会给浏览器分身打上此标签，可自行解除此标签':
    'When sending a private message too fast, this tag will be marked to the browser browser, and you can remove this tag yourself',
  '当经纪人账号被封控时，会给相应的浏览器分身打上此标签，可自行解除此标签':
    'When a backstage account is blocked, the corresponding browser browser will be marked with this tag, and this tag can be removed by itself',
  '当经纪人账号被封禁时，会给相应的浏览器分身打上此标签，可自行解除此标签':
    'When a backstage account is blocked, the corresponding browser will be taged with this tag, and you can cancel this tag yourself',
  '如果经纪人账号未登录，会给相应的浏览器分身打上此标签，需要您自行登录':
    'If the backstage account is not logged in, the corresponding browser will be marked with this tag and you will need to log in yourself',
  每一个手机账号每天最多可以发送多少私信:
    'How many private messages can each mobile account send per day?',
  '每次执行手机号相关流程时，如发送私信、分享视频、账号关注等，会从含有此标签的手机账号中选取 ':
    'Every time you perform mobile phone-related processes, such as sending private messages, sharing videos, following accounts, etc., you will select them from the mobile phone account that contains this tag. ',
  '当发送私信过快时，会给手机账号打上此标签，可自行解除此标签，或通过“检测账号可用性”流程自动解除':
    'When a private message is sent too fast, this tag will be marked to the mobile phone account, which can be removed by yourself or automatically cancelled through the "Check Account Availability" process',
  '当账号被封控时，会给手机账号打上此标签，可自行解除此标签，或通过“检测账号可用性”流程自动解除':
    'When an account is blocked, the mobile account will be marked with this tag, which can be removed by itself or automatically through the "Check Account Availability" process',
  '当账号被封禁时，会给手机账号打上此标签，可自行解除此标签，或通过“检测账号可用性”流程自动解除':
    'When an account is blocked, the mobile account will be taged with this tag, which can be removed by yourself or automatically through the "Check Account Availability" process',
  '如果TikTok未登录，会给手机账号打上此标签，需要您自行登录':
    'If TikTok is not logged in, this tag will be marked to your mobile account, and you will need to log in yourself',
  '当账号未设置成“允许所有人都可以私信我”时，会给手机账号打上此标签，需要您自行设置手机账号的隐私设置':
    'When the account is not set to "Allow everyone to trust me privately", the mobile account will be marked with this tag, and you need to set the privacy settings of the mobile account yourself.',
  '“手机账号私信”流程与“手机账号视频分享”流程均会参考下述设置，':
    'Both the "Mobile Account Private Message" process and the "Mobile Account Video Sharing" process will refer to the following settings,',
  私信普通账号_浏览器: 'Browser Account for Private Message',
  私信官方账号_浏览器: 'Official Account for Private Message',
  私信经纪人账号_浏览器: 'Backstage Account for Private Message',
  私信普通账号_手机: 'Mobile Account for Private Message',
  最近开播时间: 'Last live',
  请设置最近开播时间: 'Please choose the last live time',
  '最近开播时间晚于上述时间的主播才允许抓取到您的团队，最小值为1天，最大值为30天，请注意，最近天数要求的越小，抓取的主播数量越少':
    'Only creators whose recent launch time is later than the above time are allowed to crawl to your team. The minimum value is 1 day and the maximum value is 30 days. Please note that the smaller the most recent number of days, the fewer creators will be crawled',
  请同步更换抓取主播ID流程所使用的浏览器分身的节点IP地址:
    'Please simultaneously change the node IP address of the browser used in the process of grabbing creator ID',
  请同步更换用来筛选主播达人是否能够被邀约的经纪人账号:
    'Please simultaneously change the agent account used to screen whether creators can be invited',
  请同步更换用来抓取主播ID流程所使用的TikTok账号:
    'Please simultaneously change the TikTok account used to grab the creator ID process',
  '请注意，一个月只能更改{{count}}次':
    'Please note that you can only change {{count}} times a month',
  使用经纪人后台的话术模板: "Use the backstage's script template",
  AI生成评论: 'AI generates comments',
  '从视频的评论内容中抓取10条评论，喂给 ChatGPT 生成相应评论，如果评论内容少于10条，则不生成任何评论':
    'Grab 10 comments from the comment content of the video and feed them to ChatGPT to generate corresponding comments. If the comment content is less than 10, no comments will be generated',
  使用在花漾中设置的话术模板: 'Use the script template set up in Hua Yang',
  '优先使用经纪人账号设置的话术，否则使用下述话术：':
    'Use the words set by the backstage account first, otherwise use the following script:',
  '优先使用手机账号设置的话术，否则使用下述话术：':
    'Give priority to the words set by your mobile account, otherwise use the following script:',
  话术分组: 'Script group',
  请选择分组: 'Please select group',
  可选话术: 'Script',
  请选择话术: 'Please choose your script',
  在发送私信时: 'When sending a private message',
  通过经纪人账号发送私信时: 'When sending a private message through a backstage account',
  在发送手机私信时: 'When sending a private message on your mobile phone',
  通过普通账号发送私信时: 'When sending a private message through a normal account',
  通过官方账号发送私信时: 'When sending a private message through an official account',
  '{{prefix}}，通过对达人进行浏览、点赞、收藏等行为，降低封号风险：':
    '{{prefix}}, reduce the risk of being banned by browsing, liking, and collecting creators:',
  '除了关注达人以外，您还可以决定是否进行以下辅助行为：':
    'Besides following influencers, you can also choose to perform the following actions:',
  '请注意，只有关注达人后，才能够分享视频给他/她：':
    'Please note that you can only share videos with someone after following him/her:',
  '取值范围1 - 6': 'Value range 1 - 6',
  '首页的第几个视频：': 'Video position:',
  达人关注概率: 'Follow chance',
  每视频观看时长: 'View in',
  '{{count}}秒': '{{count}} seconds',
  请指定观看时长: 'Please specify the viewing duration',
  视频点赞概率: 'Likes chance',
  视频收藏概率: 'Favorite chance',
  视频评论概率: 'Comment chance',
  评论辅助表情: 'Comment emoji',
  '在内容中添加一些辅助表情（使内容产生差异化）的概率':
    'The chance of adding auxiliary emoticons to the content (making the content differentiated)',
  视频评论内容: 'Video comment',
  指定话术分组: 'Designated script grouping',
  '从视频的评论内容中抓取10条评论，喂给ChatGPT生成相应评论，如果评论内容少于10条，则不生成任何评论':
    'Grab 10 comments from the comment content of the video and feed them to ChatGPT to generate corresponding comments. If the comment content is less than 10, no comments will be generated',
  '原则上，官方账号有且只有一个，您需要为相应的浏览器分身打上标签“{{tag}}”':
    'In principle, there is only one official account, and you need to tag the corresponding browser "{{tag}}"',
  使用的账号: 'Account',
  使用与该达人最近沟通用到的账号: 'Use the account used to communicate with the person recently',
  '如果从未有账号联系过或者最近一次用到的账号包含有标签{{tags}}，则分配新的账号':
    'If no account has ever been contacted or the last account used contains tags {{tags}}, assign a new account',
  强制分配新的账号: 'Force the allocation of new accounts',
  强制指定账号: 'Mandatory account designation',
  '如果强制使用某个账号，请不要选择过多的达人，否则，受到私信发送频率的限制，会导致发送私信的间隔过长':
    'If you are forced to use a certain account, please do not select too many people. Otherwise, due to the restriction on the frequency of sending private messages, the interval between sending private messages will be too long',
  分配原则: 'Allocation',
  '带有标签“{{tag}}”的账号': 'Account with tag "{{tag}}"',
  '请注意：会忽略带有标签{{tags}}的账号':
    'Please note: Accounts with tags {{tags}} will be ignored',
  '带有标签“{{tag}}”和以下标签的账号': 'Accounts with the tag "{{tag}}" and ',
  '如果该达人已经与某个账号联系过，则使用联系过的账号，如果该达人从未联系过，则使用下述原则：':
    'If the person has already contacted an account, the contacted account will be used. If the person has never contacted, the following principles will be used:',
  '从带有标签“{{tag}}”的账号中随机选择': 'Select randomly from accounts with the tag "{{tag}}"',
  '强制使用指定账号：': 'Mandatory use of specified account:',
  经纪人账号分配原则: 'Backstage account allocation principles',
  官方账号分配原则: 'Official account allocation principles',
  普通账号分配原则: 'Common account allocation principles',
  关注行为: 'Concerning behavior',
  分享内容: 'Share content',
  辅助行为: 'Assistant behavior',
  Instagram达人邀约话术: 'Instagram creator invitation script',
  TikTok评论话术: 'TikTok commentary',
  话术分组名称: 'Script group name',
  请输入话术分组名称: 'Please enter the script group name',
  '确定要删除此话术分组吗？': 'Are you sure you want to delete this script group?',
  删除话术分组会同步删除此分组下的所有话术:
    'Deleting a speech group will simultaneously delete all script under this group',
  新分组: 'New group',
  创建新的话术分组: 'Create new script groups',
  新话术: 'New script',
  达人信息更新: 'Creator information update',
  每分身每次更新达人数: 'Creators updated per clone',
  KOL私域管理系统: 'KOL Private Domain Management System',
  系统自动计划: 'System automatic plans',
  Instagram配置: 'Instagram configuration',
  Instagram达人: 'Instagram Creator',
  无法打开未设置IP隔离的浏览器分身: 'Unable to open a browser without IP isolation set',
  请在花漾客户端内完成对浏览器分身的IP隔离设置:
    'Please complete the IP isolation settings for the browser in Huayang client',
  手机私信数量: 'Mobile account msgs',
  浏览器私信数量: 'Browser msgs',
  '您也无需担心同一时间同时执行若干个不同类型的流程任务而产生的干扰问题，此时会由任务池根据不同流程任务的优先级自行调度。':
    "You also don't need to worry about the interference caused by executing several different types of process tasks at the same time. At this time, the task pool will schedule itself according to the priorities of different process tasks.",
  '更进一步信息，请您阅读 {{link}} 一文': 'For more information, please read the article {{link}}',
  任务池与历史任务: 'Task pool and historical tasks',
  '会更新该达人的头像、粉丝数、关注数、帖子数':
    'The browser, number of fans, number of followers, and number of posts will be updated',
  '自动计划最多同时处理 10000 个达人':
    'Automatic planning can handle up to 10000 creators at the same time',
  最近开播: 'Last live',
  最近3天开过播: 'Last three days',
  最近7天开过播: 'Last 7 days',
  最近30天开过播: 'Last 30 days',
  手机账号私信: 'Mobile account private message',
  '通过手机中的 {{name}} App 向达人发送私信':
    'Send private messages to creatored people through the {{name}} App on your mobile phone',
  经纪人账号后台私信: 'Backstage account background private message',
  通过经纪人账号登录公会后台向达人发送邀约请求:
    'Log in to the guild backstage through your backstage account and send invitation requests to the creator',
  官方账号浏览器私信: 'Official account browser private message',
  '官方账号是指公会授权的 TikTok 账号，代表公会品牌标识':
    'The official account refers to the TikTok account authorized by the guild and represents the guild brand identity',
  普通账号浏览器私信: 'Browser account browser private message',
  '用浏览器发送私信（不推荐）': 'Send private messages using your browser (not recommended)',
  '通过手机中的 {{name}} App 关注达人并打招呼':
    'Follow and say hello through the {{name}} App on your mobile phone',
  '将您的视频分享给达人，以引起达人的注意':
    'Share your video with creatored people to attract their attention',
  '与所选达人建立联系，引起达人的注意与回复':
    'Establish contact with selected creator and attract their attention and response',
  与所选达人邀约建联: 'Invite with selected creators',
  建联方式: 'Invite way',
  请选择适合的建联方式: 'Please choose the right method for invite',
  流程任务已创建: 'Process task created',
  您可到任务池中查看详细信息: 'You can check the details in the task pool',
  '拟对 {{count}} 个达人创建“邀约建联”的流程任务':
    'The process task of creating an "invitation" for {{count}} individuals',
  '点击“完成”按钮后，系统会在 {{link}} 中创建 {{count}} 个流程任务，这些任务会按照流程的优先级、在允许执行的时间段内分批调度、依次执行':
    'After clicking the "Finish" button, the system will create {{count}} process tasks in {{link}}. These tasks will be scheduled in batches and executed in sequence according to the priority of the process and within the allowed execution time period',
  消息发送成功将这批达人的状态更改为:
    'The message was successfully sent, changing the status of this group of creators to',
  将这批达人的状态更改为: 'Change the status of this group of creators to',
  '为这批达人打上标签，方便日后查找': 'Tag this group of creators for easy search in the future',
  请输入标签: 'Please enter the tag',
  关键词列表: 'Keyword list',
  '请输入多个和您希望养成类别相关的标签，如您希望养成和宠物相关的账号，则请输入：cute cats,cats,dog,pet 等多个关键词':
    'Please enter multiple tags related to the category you want to develop. If you want to develop an account related to pets, please enter multiple keywords such as cute cats,cats,dog,pet, etc.',
  请指定关键词: 'Please specify keywords',
  多个关键词以逗号分割: 'Multiple keywords are separated by commas',
  养号流程已触发: 'The number maintenance process has been triggered',
  可在TikTok账号属性开启自动养号计划:
    'You can open the automatic account maintenance plan in TikTok account attributes',
  TikTok账号属性: 'TikTok account properties',
  '账号标签：': 'Account tag:',
  '让 TK 平台给指定的手机账号打上特定标签': 'Let the TK platform tag specific mobile accounts',
  多个关键词以英文逗号分割: 'Multiple keywords are separated by English commas',
  '账号权重：': '',
  '提升账号权重，尽可能避免账号封控的风险':
    'Increase account weight and avoid the risk of account lockdown as much as possible',
  未找到可用的养号流程: 'No available number maintenance process found',
  '账号名称：': 'Account: ',
  最近联系的手机账号: 'Mobile accounts',
  分配我的刷榜大哥: 'Assign my gifter list',
  修改私信设置: 'Modify private message settings',
  主播私信设置已修改: 'Creator private message settings have been modified',
  针对当前搜索条件下的所有刷榜大哥进行操作:
    'Operate for all gifters who swipe the list under the current search conditions',
  最近联系的经纪人账号: 'Backstage accounts',
  '最近更新时间：': 'Last update time: ',
  主播私信已修改: "The creator's private message has been modified",
  账号清理流程已触发: 'Account cleanup process has been triggered',
  '手机 TikTok 账号清理': 'Mobile TikTok account cleanup',
  '当手机 TikTok 账号关注过多达人时会影响邀约效率，此时可对手机账号进行清理':
    'When your mobile TikTok account follow too much creators, it will affect the efficiency of the invitation. You can clean up your mobile account at this time.',
  取消所有非粉丝达人的关注: 'Cancel follow from all non-fan celebrities',
  取消所有粉丝的关注: 'Cancel all fan follow',
  清空所有的聊天记录: 'Empty all chats',
  未找到可用的清理流程: 'No available cleanup process found',
  'TikTok 账号标签有着具体的含义，可在 {{link}} 中进行定义':
    'The TikTok account tag has specific meaning, which can be defined in {{link}}',
  '当开启自动养号后，会在指定的时间内自动执行养号流程':
    'When automatic number maintenance is enabled, the number maintenance process will be automatically executed within a specified time',
  '对 TikTok 账号进行批量设置，会影响所选账号的具体设置':
    'Batch settings for TikTok accounts will affect the specific settings of the selected account',
  '当开启自动养号后，在手机允许的养号时间段内，会自动执行养号流程':
    'When automatic number maintenance is enabled, the number maintenance process will be automatically executed during the number maintenance time allowed by the mobile phone',
  自动养号: 'Auto maintenance',
  '您需要根据账号的权重，自行调整下述参数':
    'You need to adjust the following parameters according to the weight of your account',
  每日最多私信条数: 'Max PM per day',
  '每一个手机账号每天最多可以发送多少私信（包括手机视频分享）':
    'The maximum number of private messages per mobile account can send per day (including mobile video sharing)',
  '{{count}}条': '{{count}}',
  每批次最多私信条数: 'Max PM per batch',
  '每批最多可以发送多少私信（包括手机视频分享），发送完后需要间隔一定时间才允许继续发送，间隔时间在“手机属性-->工作间隔”中指定，请注意，间隔时间是以手机为单位的，换言之，如果a账号和b账号位于同一部手机，当a账号发送完一批私信后，即便需要b账号发送私信，也需要等待指定的时间间隔后才允许发送':
    'The maximum number of private messages (including mobile phone video sharing) can be sent in each batch. After sending, a certain interval is required before continuing sending. The interval is specified in "Mobile Phone Properties--> Work Interval". Please note that the interval is based on mobile phones. In other words, if account a and account b are located on the same mobile phone, when account a has sent a batch of private messages, even if account b needs to send private messages, it will need to wait for the specified interval before sending them.',
  '可为 TikTok 账号指定不同的话术': 'Different script can be specified for your TikTok account',
  邀约话术分组: 'Script group',
  'TikTok 账号设置': 'TikTok account settings',
  'TikTok 账号批量设置': 'Batch setup of TikTok accounts',
  养号设置: 'Account setting',
  发送私信设置: 'PM settings',
  分享名片设置: 'Share card',
  每日最多分享条数: 'Max shares per day',
  每一个手机账号每天最多可以分享多少达人名片:
    'How many business cards can each mobile account share every day?',
  每批次最多分享条数: 'Max shares per batch',
  '每批最多可以分享多少名片，分享完后需要间隔一定时间才允许继续分享，间隔时间在“手机属性-->工作间隔”中指定，请注意，间隔时间是以手机为单位的，换言之，如果a账号和b账号位于同一部手机，当a账号分享完一批名片后，即便需要b账号分享名片，也需要等待指定的间隔时间后才允许继续分享':
    'The maximum number of business cards can be shared in each batch. A certain interval is required after sharing before continuing sharing. The interval is specified in "Mobile Phone Properties--> Work Interval". Please note that the interval is based on mobile phones. In other words, if account a and account b are located on the same mobile phone, when account a shares a batch of business cards, even if account b needs to share business cards, it will need to wait for the specified interval before continuing sharing is allowed.',
  '对手机进行批量设置，会影响所选手机的具体设置':
    'Batch settings on mobile phones will affect the specific settings of the selected mobile phone',
  可用性检查: 'Availability check',
  检查时间: 'Inspection time',
  请设置检查时间: 'Please set the inspection time',
  '每 {{min}} 分钟': 'Every {{min}} minute',
  不检查: 'Not check',
  '机器人会给指定的目标达人发送私信，如果发送成功则清理相关标签，反之则打上相关标签':
    'The robot will send a private message to the designated target person. If the message is successful, the relevant tags will be cleared; otherwise, the relevant tags will be stamped',
  当前Tiktok账号的第一个好友: 'First friend on your current Tiktok account',
  在当前团队公会主播中随机找一个主播:
    'Find an creator randomly among the current team guild creators',
  指定达人: 'Designated creator',
  请指定目标达人: 'Please specify a target',
  手机ID: 'Mobile phone ID',
  请输入手机名称: 'Please enter your mobile phone name',
  微信绑定状态: 'WeChat binding status',
  '（只有在指定时间范围内才会检查是否有新消息）':
    '(Check for new messages only within the specified time frame)',
  请选择接收人: 'Please select the recipient',
  工作间隔设置: 'Work interval',
  手机相关设置: 'Mobile phone settings',
  手机批量设置: 'Mobile phone settings',
  设置手机执行对应流程的间隔:
    'Set the interval at which the phone executes the corresponding process',
  发送私信每批次间隔: 'Send PM interval',
  请输入发送私信间隔: 'Please enter the interval between sending private messages',
  '{{count}}分钟': '{{count}} minutes',
  请输入分享卡片间隔: 'Please enter the card sharing interval',
  分享名片每批次间隔: 'Share card interval',
  '分享完一批名片后，需要间隔多长时间才允许下一批次的名片分享':
    'After sharing a batch of business cards, how long does it take to allow the next batch of business cards to be shared?',
  '允许手机发送私信、手机账号关注、手机视频分享、手机名片分享等流程的执行时间':
    'Execution time for processes such as allowing mobile phones to send private messages, mobile phone account follow, mobile phone video sharing, and mobile phone business card sharing',
  '执行手机养号流程的时间（需要开启自动养号）':
    'Time to execute the mobile phone number maintenance process (automatic number maintenance needs to be enabled)',
  '在此时间内不执行上述自动化流程，一般设置为员工工作时间段':
    'The above automated processes are not executed during this time period, and are generally set as employee working hours',
  账号可用性检查流程已触发: 'Account availability check process has been triggered',
  '机器人会给指定的目标达人发送私信，如果发送成功则清理相关标签，反之则打上相关标签；如果在此处指定目标达人，要求该手机中登录的所有TikTok号都要和该达人互为好友；如果留空，机器人会寻找一个好友发送私信':
    'The robot will send a private message to the designated target person. If the message is successful, the relevant tags will be cleared; otherwise, the relevant tags will be stamped; if the target person is specified here, all TikTok accounts logged in on the mobile phone are required to be friends with the target person. If you leave it blank, the robot will find a friend to send a private message',
  '（最小10分钟，最大20000分钟）': '(Minimum 10 minutes, maximum 20000 minutes)',
  '确定删除选中的 {{total}} 个账号吗？':
    'Are you sure you want to delete the selected {{total}} accounts?',
  '手机账号删除后，可通过同步手机账号流程予以恢复':
    'After the mobile account is deleted, it can be restored through the process of synchronizing the mobile account',
  '手机名称: {{mobileName}}': 'Mobile phone name: {{mobileName}}',
  手机属性: 'Phone detail',
  手机微信通知: 'WeChat notification',
  '每日最多{{count}}': 'Max {{count}} per day',
  正在执行的流程: 'Processing',
  账号清理: 'Account cleanup',
  分配给我的在线主播: 'My online creators',
  '运行“直播广场主播在线守候流程“': 'Run the "Live Square Creator Online Waiting Process"',
  最多1000: 'Up to 1000',
  忽略当前团队已有的主播: 'Ignore existing creators on the current team',
  '运行“团队已有主播在线守候流程”': 'Run the "Team Creator Online Watching Process"',
  运行时长: 'Running time',
  给主播打上标签: 'Tag the creator',
  '抓取到的正在开播的主播会自动进入到团队的公海达人，给这些主播打上标签方便您事后筛选':
    'Capture creators that are currently in live to your team, and tag these creators.',
  只有超管与BOSS才允许执行此操作:
    'Only Superintendents and Bosses are allowed to perform this operation',
  针对当前搜索条件下的所有用户进行操作: 'Operate for all users under the current search conditions',
  '正在执行其它流程，这会导致检测账号可用性流程执行失败，请确保在该手机中结束其它所有流程后再重新执行＂':
    'Other processes are being executed, which will cause the process of checking account availability to fail. Please ensure that all other processes are completed on this phone before executing again."',
  正在刷新审批结果: 'Refreshing approval results',
  查看申请的审批结果: 'View the approval results of the application',
  未知错误: 'Unknown error',
  启用_计划: ' been activated',
  禁用_计划: ' been disabled',
  英文空格: ' ',
  主播CID: 'CID',
  主播UID: 'UID',
  用户UID: 'UID',
  主播链接: 'Link',
  用户链接: 'Link',
  区域: 'Region',
  私信状态: 'PM set',
  总打赏: 'Total tip',
  历史最高人数: 'Viewers',
};

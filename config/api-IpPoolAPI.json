{"openapi": "3.0.3", "info": {"title": "Ip Pool API", "version": "App version 1.0,Spring Boot Version:2.7.14"}, "servers": [{"url": "http://dev.thinkoncloud.cn", "description": "Inferred Url"}], "tags": [{"name": "IP池API", "description": "Ip Pool Controller"}], "paths": {"/api/ipp/countries": {"get": {"tags": ["IpPoolController"], "summary": "获取团队所有IP池的国家（地区）列表", "operationId": "ippCountriesGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«CountryDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ipp/ipp/providers": {"get": {"tags": ["IpPoolController"], "summary": "查询IP池供应商", "operationId": "ippIppProvidersGet", "parameters": [{"name": "valid", "in": "query", "description": "valid", "required": false, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«IppProviderDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ipp/paramDefs": {"get": {"tags": ["IpPoolController"], "summary": "查询供应商的参数定义列表", "operationId": "ippParamDefsGet", "parameters": [{"name": "provider", "in": "query", "description": "provider", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«IppParamDefDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ipp/pool": {"post": {"tags": ["IpPoolController"], "summary": "创建IP池", "operationId": "ippPoolPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateIpPoolRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«IpPoolDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ipp/pool/page": {"get": {"tags": ["IpPoolController"], "summary": "分页查询IP池", "operationId": "ippPoolPageGet", "parameters": [{"name": "provider", "in": "query", "description": "供应商", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "country", "in": "query", "description": "国家", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "city", "in": "query", "description": "城市", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "tagIds", "in": "query", "description": "按标签id过滤", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "name", "in": "query", "description": "name", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "pageNum", "in": "query", "description": "pageNum", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "sortFiled", "in": "query", "description": "排序字段", "required": false, "style": "form", "schema": {"type": "string", "enum": ["capacity", "createTime", "lastApiTime", "lifetime", "provider"]}}, {"name": "sortOrder", "in": "query", "description": "顺序", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«IppPoolVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ipp/pool/{ippId}": {"get": {"tags": ["IpPoolController"], "summary": "查询IP池详情", "operationId": "ippPoolByIppIdGet", "parameters": [{"name": "ippId", "in": "path", "description": "ippId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«IppPoolWithParamsVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "delete": {"tags": ["IpPoolController"], "summary": "删除IP池", "operationId": "ippPoolByIppIdDelete", "parameters": [{"name": "ippId", "in": "path", "description": "ippId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ipp/pool/{ippId}/allocIppIp": {"get": {"tags": ["IpPoolController"], "summary": "分配一个ip池中的IP", "operationId": "ippPoolByIppIdAllocIppIpGet", "parameters": [{"name": "ippId", "in": "path", "description": "ippId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«AllocIppIpResult»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ipp/pool/{ippId}/basicInfo": {"put": {"tags": ["IpPoolController"], "summary": "修改IP池属性", "operationId": "ippPoolByIppIdBasicInfoPut", "parameters": [{"name": "ippId", "in": "path", "description": "ippId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateIpPoolBasicInfoRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ipp/pool/{ippId}/ips": {"get": {"tags": ["IpPoolController"], "summary": "分页查询IP池IP列表", "operationId": "ippPoolByIppIdIpsGet", "parameters": [{"name": "ippId", "in": "path", "description": "ippId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "minActiveSessions", "in": "query", "description": "minActiveSessions", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "valid", "in": "query", "description": "valid", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "ip", "in": "query", "description": "ip", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "host", "in": "query", "description": "host", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "pageNum", "in": "query", "description": "pageNum", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "sortFiled", "in": "query", "description": "排序字段", "required": false, "style": "form", "schema": {"type": "string", "enum": ["areaCode", "createTime", "directDownTraffic", "directUpTraffic", "expireTime", "host", "invalidTime", "lastAllocTime", "outboundIp", "proxyType", "status", "transitDownTraffic", "transitUpTraffic", "username", "valid"]}}, {"name": "sortOrder", "in": "query", "description": "顺序", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«IppIpDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "delete": {"tags": ["IpPoolController"], "summary": "批量删除IP池IP", "operationId": "ippPoolByIppIdIpsDelete", "parameters": [{"name": "ippId", "in": "path", "description": "ippId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "ippIpIds", "in": "query", "description": "ippIpIds", "required": true, "style": "form", "explode": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ipp/pool/{ippId}/parseIppIp": {"post": {"tags": ["IpPoolController"], "summary": "解析Ip池生产结果", "operationId": "ippPoolByIppIdParseIppIpPost", "parameters": [{"name": "ippId", "in": "path", "description": "ippId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClientProduceIpResult"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«long»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ipp/pool/{ippId}/providerParam": {"put": {"tags": ["IpPoolController"], "summary": "修改IP池属性", "operationId": "ippPoolByIppIdProviderParamPut", "parameters": [{"name": "ippId", "in": "path", "description": "ippId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateIpPoolProviderParamRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ipp/pool/{ippId}/strategy": {"put": {"tags": ["IpPoolController"], "summary": "修改IP池属性", "operationId": "ippPoolByIppIdStrategyPut", "parameters": [{"name": "ippId", "in": "path", "description": "ippId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateIpPoolStrategyRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ipp/pools": {"delete": {"tags": ["IpPoolController"], "summary": "批量删除IP池", "operationId": "ippPoolsDelete", "parameters": [{"name": "ippIds", "in": "query", "description": "ippIds", "required": true, "style": "form", "explode": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ipp/whitelistConfig": {"get": {"tags": ["IpPoolController"], "summary": "查询白名单配置", "operationId": "ippWhitelistConfigGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«IppWhitelistConfig»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}}, "components": {"schemas": {"AllocIppIpResult": {"title": "AllocIppIpResult", "type": "object", "properties": {"ippIpId": {"type": "integer", "description": "如果分配成功，则返回分配的ip池IP ID", "format": "int64"}, "produceIpSpecVo": {"description": "如果需要生产IP，则用下面的规则去调用", "$ref": "#/components/schemas/ProduceIpSpecVo"}}}, "ClientProduceIpResult": {"title": "ClientProduceIpResult", "type": "object", "properties": {"httpCode": {"type": "integer", "description": "http响应代码", "format": "int32"}, "produceIpSpecVo": {"description": "生产IP时的", "$ref": "#/components/schemas/ProduceIpSpecVo"}, "responseBody": {"type": "string", "description": "http响应流"}, "responseHeaders": {"type": "object", "description": "http响应头"}}}, "CountryDto": {"title": "CountryDto", "type": "object", "properties": {"code": {"type": "string"}, "continentCode": {"type": "string"}, "continentName": {"type": "string"}, "continentNameEn": {"type": "string"}, "geonameId": {"type": "integer", "format": "int32"}, "id": {"type": "integer", "format": "int64"}, "inEu": {"type": "boolean"}, "name": {"type": "string"}, "nameEn": {"type": "string"}, "show": {"type": "boolean"}}}, "CreateIpPoolRequest": {"title": "CreateIpPoolRequest", "type": "object", "properties": {"allocateStrategy": {"type": "string", "enum": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ByLoad", "ByOrder", "ByRandom"]}, "connectTransitIds": {"type": "array", "description": "白名单连接的接入点", "items": {"type": "integer", "format": "int64"}}, "countryCode": {"type": "string"}, "description": {"type": "string"}, "domestic": {"type": "boolean"}, "enableWhitelist": {"type": "boolean", "description": "是否开启白名单", "example": false}, "exclusive": {"type": "boolean"}, "lifetime": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "params": {"type": "object", "additionalProperties": {"type": "string"}}, "produceFromTransit": {"type": "boolean"}, "produceStrategy": {"type": "string", "enum": ["ProduceOnEmpty", "ProduceOnHand", "ProduceOnRequest"]}, "provider": {"type": "string"}, "releaseStrategy": {"type": "string", "enum": ["Discard", "Reuse"]}, "transitIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "transitType": {"type": "string", "description": "切换策略", "enum": ["Auto", "Direct", "Transit"]}, "tunnelTypes": {"type": "array", "items": {"type": "string", "enum": ["clash", "direct", "jump", "localFrontend", "platform", "transit"]}}}}, "DepartmentDto": {"title": "DepartmentDto", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "hidden": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "invitingAuditEnabled": {"type": "boolean"}, "invitingEnabled": {"type": "boolean"}, "name": {"type": "string"}, "parentId": {"type": "integer", "format": "int64"}, "sortNumber": {"type": "integer", "format": "int32"}, "teamId": {"type": "integer", "format": "int64"}}}, "FingerprintDto": {"title": "FingerprintDto", "type": "object", "properties": {"browser": {"type": "string", "description": "指纹对应浏览器名称，如chrome", "enum": ["Chrome", "Edge", "Safari"]}, "configId": {"type": "string", "description": "mongo里的id"}, "createType": {"type": "string", "description": "备用字段，指纹类型，抓取or生成", "enum": ["<PERSON>tch", "Gen", "HyGen", "MKLong", "Temp", "Template", "Transfer"]}, "creatorId": {"type": "integer", "description": "创建者。有可能可能为空!", "format": "int64"}, "description": {"type": "string", "description": " 指纹描述"}, "id": {"type": "integer", "description": "id", "format": "int64"}, "majorVersion": {"type": "integer", "description": "浏览器主版本号，如 109", "format": "int32"}, "md5sum": {"type": "string"}, "name": {"type": "string", "description": "指纹名称"}, "operateStatus": {"type": "string", "enum": ["shared", "sharing", "sole", "transferring"]}, "platform": {"type": "string", "description": "指纹对应平台", "enum": ["Android", "IOS", "Linux", "<PERSON>", "Windows"]}, "stateless": {"type": "boolean"}, "teamId": {"type": "integer", "description": "团队ID", "format": "int64"}, "templateId": {"type": "integer", "description": "指纹所属模板id", "format": "int64"}, "updateTime": {"type": "string", "description": "最后更新时间", "format": "date-time"}}}, "FingerprintTemplateDto": {"title": "FingerprintTemplateDto", "type": "object", "properties": {"browser": {"type": "string", "enum": ["Chrome", "Edge", "Safari"]}, "configId": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "createType": {"type": "string", "enum": ["<PERSON>tch", "Gen", "HyGen", "MKLong", "Temp", "Template", "Transfer"]}, "creatorId": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "majorVersion": {"type": "integer", "description": "如果不为空则说明该模板生成的实例固定使用该版本号，否则使用随机版本", "format": "int32"}, "maxIns": {"type": "integer", "format": "int32"}, "md5sum": {"type": "string"}, "name": {"type": "string"}, "platform": {"type": "string", "enum": ["Android", "IOS", "Linux", "<PERSON>", "Windows"]}, "teamId": {"type": "integer", "format": "int64"}, "usedIns": {"type": "integer", "format": "int32"}}}, "IdNameVo": {"title": "IdNameVo", "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}}}, "IpLocationDto": {"title": "IpLocationDto", "type": "object", "properties": {"city": {"type": "string"}, "cityEn": {"type": "string"}, "continent": {"type": "string"}, "continentCode": {"type": "string"}, "continentEn": {"type": "string"}, "country": {"type": "string"}, "countryCode": {"type": "string"}, "countryEn": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "geonameId": {"type": "integer", "format": "int32"}, "id": {"type": "integer", "format": "int64"}, "inEu": {"type": "boolean"}, "latitude": {"type": "number", "format": "double"}, "level": {"type": "string", "enum": ["City", "Continent", "Country", "District", "None", "Province", "Unknown"]}, "locale": {"type": "string"}, "longitude": {"type": "number", "format": "double"}, "postalCode": {"type": "string"}, "province": {"type": "string"}, "provinceCode": {"type": "string"}, "provinceEn": {"type": "string"}, "show": {"type": "boolean"}, "teamId": {"type": "integer", "format": "int64"}, "timezone": {"type": "string"}}}, "IpPoolDto": {"title": "IpPoolDto", "type": "object", "properties": {"allocateStrategy": {"type": "string", "enum": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ByLoad", "ByOrder", "ByRandom"]}, "capacity": {"type": "integer", "format": "int32"}, "connectTransits": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "creator": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "domestic": {"type": "boolean"}, "enableWhitelist": {"type": "boolean"}, "exclusive": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "lastApiTime": {"type": "string", "format": "date-time"}, "lifetime": {"type": "integer", "format": "int32"}, "locationId": {"type": "integer", "format": "int64"}, "minApiInterval": {"type": "integer", "format": "int32"}, "minApiNum": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "produceFromTransit": {"type": "boolean"}, "produceStrategy": {"type": "string", "enum": ["ProduceOnEmpty", "ProduceOnHand", "ProduceOnRequest"]}, "produced": {"type": "integer", "format": "int32"}, "provider": {"type": "string"}, "releaseStrategy": {"type": "string", "enum": ["Discard", "Reuse"]}, "teamId": {"type": "integer", "format": "int64"}, "transitType": {"type": "string", "enum": ["Auto", "Direct", "Transit"]}, "transits": {"type": "string"}, "tunnelTypes": {"type": "string"}}}, "IppIpDto": {"title": "IppIpDto", "type": "object", "properties": {"activeSessions": {"type": "integer", "format": "int32"}, "areaCode": {"type": "integer", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}, "directDownTraffic": {"type": "integer", "format": "int64"}, "directUpTraffic": {"type": "integer", "format": "int64"}, "expireTime": {"type": "string", "format": "date-time"}, "host": {"type": "string"}, "hostDomestic": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "invalidTime": {"type": "string", "format": "date-time"}, "ippId": {"type": "integer", "format": "int64"}, "lastAllocTime": {"type": "string", "format": "date-time"}, "lastProbeTime": {"type": "string", "format": "date-time"}, "locationId": {"type": "integer", "format": "int64"}, "outboundIp": {"type": "string"}, "password": {"type": "string"}, "port": {"type": "integer", "format": "int32"}, "probeError": {"type": "string"}, "proxyType": {"type": "string"}, "refTeamIp": {"type": "integer", "format": "int64"}, "status": {"type": "string", "enum": ["Available", "Pending", "Unavailable"]}, "teamId": {"type": "integer", "format": "int64"}, "transitDownTraffic": {"type": "integer", "format": "int64"}, "transitUpTraffic": {"type": "integer", "format": "int64"}, "username": {"type": "string"}, "valid": {"type": "boolean"}}}, "IppParamDefDto": {"title": "IppParamDefDto", "required": ["<PERSON><PERSON><PERSON><PERSON>", "paramLabel", "paramType", "provider", "required", "sortNo", "uiShow", "userInput"], "type": "object", "properties": {"description": {"type": "string", "description": "描述"}, "id": {"type": "integer", "format": "int64"}, "paramKey": {"type": "string", "description": "参数名"}, "paramLabel": {"type": "string", "description": "参数标签"}, "paramType": {"type": "string", "description": "参数类型", "enum": ["ApiEndpoint", "ApiEndpointRaw", "A<PERSON>H<PERSON><PERSON>", "ApiHttpMethod", "ApiPassword", "Api<PERSON>ueryP<PERSON><PERSON>", "ApiSignKey", "ApiUsername", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "GlobalWhitelist", "MinApiInterval", "MinApiNum", "ProxyPassword", "ProxyType", "ProxyUsername", "Proxy<PERSON><PERSON><PERSON><PERSON>", "Unknown"]}, "provider": {"type": "string", "description": "供应商"}, "required": {"type": "boolean", "description": "是否必填参数", "example": false}, "sortNo": {"type": "integer", "description": "排序号", "format": "int32"}, "uiShow": {"type": "boolean", "description": "是否向用户展示", "example": false}, "userInput": {"type": "boolean", "description": "是否由用户输入", "example": false}, "value": {"type": "string", "description": "参数值"}}}, "IppParamDetailVo": {"title": "IppParamDetailVo", "type": "object", "properties": {"def": {"$ref": "#/components/schemas/IppParamDefDto"}, "id": {"type": "integer", "format": "int64"}, "paramKey": {"type": "string"}, "paramType": {"type": "string", "enum": ["ApiEndpoint", "ApiEndpointRaw", "A<PERSON>H<PERSON><PERSON>", "ApiHttpMethod", "ApiPassword", "Api<PERSON>ueryP<PERSON><PERSON>", "ApiSignKey", "ApiUsername", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "GlobalWhitelist", "MinApiInterval", "MinApiNum", "ProxyPassword", "ProxyType", "ProxyUsername", "Proxy<PERSON><PERSON><PERSON><PERSON>", "Unknown"]}, "poolId": {"type": "integer", "format": "int64"}, "provider": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}, "value": {"type": "string"}}}, "IppPoolVo": {"title": "IppPoolVo", "type": "object", "properties": {"allocateStrategy": {"type": "string", "enum": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ByLoad", "ByOrder", "ByRandom"]}, "capacity": {"type": "integer", "format": "int32"}, "connectTransits": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "creator": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "domestic": {"type": "boolean"}, "enableWhitelist": {"type": "boolean"}, "exclusive": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "lastApiTime": {"type": "string", "format": "date-time"}, "lifetime": {"type": "integer", "format": "int32"}, "location": {"$ref": "#/components/schemas/IpLocationDto"}, "locationId": {"type": "integer", "format": "int64"}, "minApiInterval": {"type": "integer", "format": "int32"}, "minApiNum": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "produceFromTransit": {"type": "boolean"}, "produceStrategy": {"type": "string", "enum": ["ProduceOnEmpty", "ProduceOnHand", "ProduceOnRequest"]}, "produced": {"type": "integer", "format": "int32"}, "provider": {"type": "string"}, "providerDto": {"$ref": "#/components/schemas/IppProviderDto"}, "releaseStrategy": {"type": "string", "enum": ["Discard", "Reuse"]}, "shops": {"type": "array", "items": {"$ref": "#/components/schemas/ShopDetailVo"}}, "size": {"type": "integer", "format": "int32"}, "tags": {"type": "array", "items": {"$ref": "#/components/schemas/TagDto"}}, "teamId": {"type": "integer", "format": "int64"}, "transitType": {"type": "string", "enum": ["Auto", "Direct", "Transit"]}, "transits": {"type": "string"}, "tunnelTypes": {"type": "string"}}}, "IppPoolWithParamsVo": {"title": "IppPoolWithParamsVo", "type": "object", "properties": {"allocateStrategy": {"type": "string", "enum": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ByLoad", "ByOrder", "ByRandom"]}, "capacity": {"type": "integer", "format": "int32"}, "connectTransits": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "creator": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "domestic": {"type": "boolean"}, "enableWhitelist": {"type": "boolean"}, "exclusive": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "lastApiTime": {"type": "string", "format": "date-time"}, "lifetime": {"type": "integer", "format": "int32"}, "location": {"$ref": "#/components/schemas/IpLocationDto"}, "locationId": {"type": "integer", "format": "int64"}, "minApiInterval": {"type": "integer", "format": "int32"}, "minApiNum": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "params": {"type": "array", "items": {"$ref": "#/components/schemas/IppParamDetailVo"}}, "produceFromTransit": {"type": "boolean"}, "produceStrategy": {"type": "string", "enum": ["ProduceOnEmpty", "ProduceOnHand", "ProduceOnRequest"]}, "produced": {"type": "integer", "format": "int32"}, "provider": {"type": "string"}, "providerDto": {"$ref": "#/components/schemas/IppProviderDto"}, "releaseStrategy": {"type": "string", "enum": ["Discard", "Reuse"]}, "shops": {"type": "array", "items": {"$ref": "#/components/schemas/ShopDetailVo"}}, "size": {"type": "integer", "format": "int32"}, "tags": {"type": "array", "items": {"$ref": "#/components/schemas/TagDto"}}, "teamId": {"type": "integer", "format": "int64"}, "transitType": {"type": "string", "enum": ["Auto", "Direct", "Transit"]}, "transits": {"type": "string"}, "tunnelTypes": {"type": "string"}}}, "IppProviderDto": {"title": "IppProvider<PERSON>to", "type": "object", "properties": {"api": {"type": "boolean"}, "description": {"type": "string"}, "hasExpire": {"type": "boolean"}, "homeUrl": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "produceFromTransit": {"type": "boolean"}, "promoUrl": {"type": "string"}, "provider": {"type": "string"}, "rangeType": {"type": "string", "enum": ["Domestic", "Global", "Oversea"]}, "valid": {"type": "boolean"}}}, "IppWhitelistConfig": {"title": "IppWhitelistConfig", "type": "object", "properties": {"ippDomesticWhitelist": {"type": "array", "items": {"type": "string"}}, "ippOverseasWhitelist": {"type": "array", "items": {"type": "string"}}}}, "MemberVo": {"title": "MemberVo", "type": "object", "properties": {"account": {"type": "string", "description": "账号"}, "avatar": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "email": {"type": "string", "description": "邮箱"}, "gender": {"type": "string", "enum": ["FEMALE", "MALE", "UNSPECIFIC"]}, "id": {"type": "integer", "format": "int64"}, "nickname": {"type": "string"}, "partnerId": {"type": "integer", "format": "int64"}, "phone": {"type": "string", "description": "手机"}, "residentCity": {"type": "string"}, "roleCode": {"type": "string", "enum": ["boss", "manager", "staff", "superadmin"]}, "signature": {"type": "string"}, "status": {"type": "string", "enum": ["ACTIVE", "BLOCK", "DELETED", "INACTIVE"]}, "teamNickname": {"type": "string"}, "tenant": {"type": "integer", "format": "int64"}, "userType": {"type": "string", "enum": ["NORMAL", "PARTNER", "SHADOW"]}, "weixin": {"type": "string"}}}, "PageResult«IppIpDto»": {"title": "PageResult«IppIpDto»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/IppIpDto"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«IppPoolVo»": {"title": "PageResult«IppPoolVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/IppPoolVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "ProduceIpSpecVo": {"title": "ProduceIpSpecVo", "type": "object", "properties": {"headers": {"type": "object", "additionalProperties": {"type": "string"}, "description": "需要追加的请求头"}, "method": {"type": "string", "description": "请求方法，比如GET/POST等"}, "url": {"type": "string", "description": "请求URL，携带参数"}}}, "ShopChannelVo": {"title": "ShopChannelVo", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "dynamicStrategy": {"type": "string", "enum": ["Off", "<PERSON><PERSON><PERSON>", "SwitchOnSession"]}, "id": {"type": "integer", "format": "int64"}, "ip": {"description": "通道的IP", "$ref": "#/components/schemas/TeamIpVo"}, "ipId": {"type": "integer", "format": "int64"}, "ipPool": {"description": "通道的IP池", "$ref": "#/components/schemas/IpPoolDto"}, "ippId": {"type": "integer", "format": "int64"}, "locationId": {"type": "integer", "format": "int64"}, "locationLevel": {"type": "string", "enum": ["City", "Continent", "Country", "District", "None", "Province", "Unknown"]}, "officialChannelId": {"type": "integer", "format": "int64"}, "primary": {"type": "boolean"}, "shopId": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}}}, "ShopChatDto": {"title": "ShopChatDto", "type": "object", "properties": {"chatAccount": {"type": "string"}, "chatType": {"type": "string", "enum": ["None", "email", "facebookMessager", "line", "qq", "skype", "wechat", "whatsapp", "zalo"]}, "id": {"type": "integer", "format": "int64"}, "shopId": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}}}, "ShopDetailVo": {"title": "ShopDetailVo", "type": "object", "properties": {"accessCount": {"type": "integer", "description": "访问账户次数", "format": "int32"}, "account": {"type": "string", "description": "账户账号"}, "allowExtension": {"type": "boolean", "description": "是否允许用户自行安装插件", "example": false}, "allowMonitor": {"type": "boolean", "description": "会话是否允许监视", "example": false}, "allowSkip": {"type": "boolean", "description": "是否允许跳过敏感操作", "example": false}, "autoFill": {"type": "boolean", "description": "是否自动代填", "example": false}, "bookmarkBar": {"type": "string"}, "channels": {"type": "array", "description": "通道列表", "items": {"$ref": "#/components/schemas/ShopChannelVo"}}, "chats": {"type": "array", "description": "联系方式", "items": {"$ref": "#/components/schemas/ShopChatDto"}}, "coordinateId": {"type": "string"}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time"}, "creator": {"description": "创建人信息", "$ref": "#/components/schemas/UserVo"}, "creatorId": {"type": "integer", "description": "创建者", "format": "int64"}, "deleteTime": {"type": "string", "format": "date-time"}, "deleting": {"type": "boolean"}, "description": {"type": "string", "description": "描述"}, "domainPolicy": {"type": "string", "enum": ["Blacklist", "None", "Whitelist"]}, "domains": {"type": "array", "description": "黑白名单", "items": {"$ref": "#/components/schemas/ShopDomainItem"}}, "downTraffic": {"type": "integer", "description": "下行流量", "format": "int64"}, "exclusive": {"type": "boolean", "description": "是否独占访问", "example": false}, "extraProp": {"type": "string"}, "fingerprintId": {"type": "integer", "description": "绑定的指纹Id", "format": "int64"}, "fingerprintTemplateId": {"type": "integer", "description": "绑定的指纹模板Id（只有无状态账号才允许绑定指纹模板）", "format": "int64"}, "fingerprintTemplateVo": {"description": "指纹模版", "$ref": "#/components/schemas/FingerprintTemplateDto"}, "fingerprintVo": {"description": "指纹", "$ref": "#/components/schemas/FingerprintDto"}, "frontUrl": {"type": "string"}, "googleTranslateSpeed": {"type": "boolean"}, "grantDepartmentList": {"type": "array", "description": "授权的部门", "items": {"$ref": "#/components/schemas/DepartmentDto"}}, "grantSource": {"type": "string", "description": "授权来源", "enum": ["FromDepartment", "FromUser"]}, "grantUserVoList": {"type": "array", "description": "授权给的用户", "items": {"$ref": "#/components/schemas/MemberVo"}}, "homePage": {"type": "string"}, "homePageSites": {"type": "string"}, "id": {"type": "integer", "description": "id", "format": "int64"}, "imageForbiddenSize": {"type": "integer", "format": "int64"}, "intranetEnabled": {"type": "boolean"}, "ipBindCount": {"type": "integer", "description": "IP绑定次数", "format": "int32"}, "ipBindTime": {"type": "string", "description": "IP绑定时间", "format": "date-time"}, "ipSwitchCheckInterval": {"type": "integer", "format": "int32"}, "ipSwitchStrategy": {"type": "string", "enum": ["Abort", "<PERSON><PERSON>", "Off"]}, "ipUnbindTime": {"type": "string", "description": "IP解绑时间", "format": "date-time"}, "lanProxy": {"description": "本地代理配置", "$ref": "#/components/schemas/ShopLanProxyDto"}, "lastAccessTime": {"type": "string", "format": "date-time"}, "lastAccessUser": {"type": "integer", "format": "int64"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "loginStatus": {"type": "string", "enum": ["Offline", "Online", "Unknown"]}, "markCode": {"type": "string", "description": "任务栏分身标记文字"}, "markCodeBg": {"type": "integer", "description": "任务栏分身标记背景颜色", "format": "int32"}, "monitorPerception": {"type": "boolean"}, "name": {"type": "string", "description": "账户名称"}, "nameBookmarkEnabled": {"type": "boolean"}, "operateStatus": {"type": "string", "enum": ["shared", "sharing", "sole", "transferring"]}, "operatingCategory": {"type": "string", "description": "经营品类", "enum": ["医药保健", "图书文具", "宠物用品", "家具建材", "家电电器", "工业用品", "户外运动", "手机数码", "手表眼镜", "护肤美妆", "母婴玩具", "汽车配件", "生活家居", "电商其他", "电脑平板", "艺术珠宝", "花园聚会", "计生情趣", "软件程序", "鞋服箱包", "音乐影视", "食品生鲜", "鲜花绿植"]}, "parentShop": {"description": "主账号", "$ref": "#/components/schemas/ShopDto"}, "parentShopId": {"type": "integer", "format": "int64"}, "parentTeam": {"description": "主团队", "$ref": "#/components/schemas/IdNameVo"}, "password": {"type": "string", "description": "密码"}, "platform": {"description": "平台信息", "$ref": "#/components/schemas/ShopPlatformVo"}, "platformId": {"type": "integer", "description": "平台ID", "format": "int64"}, "privateAddress": {"type": "string"}, "privateDomains": {"type": "array", "description": "私密网站", "items": {"type": "string"}}, "privateTitle": {"type": "string"}, "recordPerception": {"type": "boolean"}, "recordPolicy": {"type": "string", "enum": ["<PERSON><PERSON>", "Disabled", "Forced"]}, "requireIp": {"type": "boolean"}, "resourcePolicy": {"type": "integer", "format": "int32"}, "resourcePolicyVo": {"description": "资源策略", "$ref": "#/components/schemas/ShopResourcePolicyVo"}, "routers": {"type": "array", "description": "通道路由列表", "items": {"$ref": "#/components/schemas/ShopRouterDto"}}, "rpaFlowCount": {"type": "integer", "description": "内置RPA流程数", "format": "int32"}, "securityPolicyEnabled": {"type": "boolean"}, "securityPolicyUpdateTime": {"type": "string", "format": "date-time"}, "sharePolicyId": {"type": "string"}, "shopDataSize": {"type": "integer", "format": "int64"}, "shortcutCount": {"type": "integer", "description": "快捷方式个数", "format": "int32"}, "stateless": {"type": "boolean"}, "statelessChangeFp": {"type": "boolean"}, "statelessSyncPolicy": {"type": "integer", "format": "int32"}, "statelessSyncPolicyVo": {"description": "无痕云端同步策略", "$ref": "#/components/schemas/ShopSyncPolicyVo"}, "strategyDefault": {"type": "boolean", "description": "是否使用默认访问策略", "example": false}, "syncPolicy": {"type": "integer", "format": "int32"}, "syncPolicyVo": {"description": "云端同步策略", "$ref": "#/components/schemas/ShopSyncPolicyVo"}, "tags": {"type": "array", "description": "标签", "items": {"$ref": "#/components/schemas/TagDto"}}, "teamId": {"type": "integer", "description": "团队ID", "format": "int64"}, "trafficAlertStrategy": {"type": "string", "enum": ["Abort", "Off"]}, "trafficAlertThreshold": {"type": "integer", "format": "int32"}, "trafficSaving": {"type": "boolean"}, "type": {"type": "string", "enum": ["Global", "Local", "None"]}, "upTraffic": {"type": "integer", "description": "上行流量", "format": "int64"}, "webSecurity": {"type": "boolean"}}}, "ShopDomainItem": {"title": "ShopDomainItem", "type": "object", "properties": {"description": {"type": "string"}, "domain": {"type": "string"}}}, "ShopDto": {"title": "ShopDto", "type": "object", "properties": {"account": {"type": "string", "description": "账户账号"}, "allowExtension": {"type": "boolean", "description": "是否允许用户自行安装插件", "example": false}, "allowMonitor": {"type": "boolean", "description": "会话是否允许监视", "example": false}, "allowSkip": {"type": "boolean", "description": "是否允许跳过敏感操作", "example": false}, "autoFill": {"type": "boolean", "description": "是否自动代填", "example": false}, "bookmarkBar": {"type": "string"}, "coordinateId": {"type": "string"}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time"}, "creatorId": {"type": "integer", "description": "创建者", "format": "int64"}, "deleteTime": {"type": "string", "format": "date-time"}, "deleting": {"type": "boolean"}, "description": {"type": "string", "description": "描述"}, "domainPolicy": {"type": "string", "enum": ["Blacklist", "None", "Whitelist"]}, "downTraffic": {"type": "integer", "description": "下行流量", "format": "int64"}, "exclusive": {"type": "boolean", "description": "是否独占访问", "example": false}, "extraProp": {"type": "string"}, "fingerprintId": {"type": "integer", "description": "绑定的指纹Id", "format": "int64"}, "fingerprintTemplateId": {"type": "integer", "description": "绑定的指纹模板Id（只有无状态账号才允许绑定指纹模板）", "format": "int64"}, "frontUrl": {"type": "string"}, "googleTranslateSpeed": {"type": "boolean"}, "homePage": {"type": "string"}, "homePageSites": {"type": "string"}, "id": {"type": "integer", "description": "id", "format": "int64"}, "imageForbiddenSize": {"type": "integer", "format": "int64"}, "intranetEnabled": {"type": "boolean"}, "ipSwitchCheckInterval": {"type": "integer", "format": "int32"}, "ipSwitchStrategy": {"type": "string", "enum": ["Abort", "<PERSON><PERSON>", "Off"]}, "lastAccessTime": {"type": "string", "format": "date-time"}, "lastAccessUser": {"type": "integer", "format": "int64"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "loginStatus": {"type": "string", "enum": ["Offline", "Online", "Unknown"]}, "markCode": {"type": "string", "description": "任务栏分身标记文字"}, "markCodeBg": {"type": "integer", "description": "任务栏分身标记背景颜色", "format": "int32"}, "monitorPerception": {"type": "boolean"}, "name": {"type": "string", "description": "账户名称"}, "nameBookmarkEnabled": {"type": "boolean"}, "operateStatus": {"type": "string", "enum": ["shared", "sharing", "sole", "transferring"]}, "operatingCategory": {"type": "string", "description": "经营品类", "enum": ["医药保健", "图书文具", "宠物用品", "家具建材", "家电电器", "工业用品", "户外运动", "手机数码", "手表眼镜", "护肤美妆", "母婴玩具", "汽车配件", "生活家居", "电商其他", "电脑平板", "艺术珠宝", "花园聚会", "计生情趣", "软件程序", "鞋服箱包", "音乐影视", "食品生鲜", "鲜花绿植"]}, "parentShopId": {"type": "integer", "format": "int64"}, "password": {"type": "string", "description": "密码"}, "platformId": {"type": "integer", "description": "平台ID", "format": "int64"}, "privateAddress": {"type": "string"}, "privateTitle": {"type": "string"}, "recordPerception": {"type": "boolean"}, "recordPolicy": {"type": "string", "enum": ["<PERSON><PERSON>", "Disabled", "Forced"]}, "requireIp": {"type": "boolean"}, "resourcePolicy": {"type": "integer", "format": "int32"}, "securityPolicyEnabled": {"type": "boolean"}, "securityPolicyUpdateTime": {"type": "string", "format": "date-time"}, "sharePolicyId": {"type": "string"}, "shopDataSize": {"type": "integer", "format": "int64"}, "stateless": {"type": "boolean"}, "statelessChangeFp": {"type": "boolean"}, "statelessSyncPolicy": {"type": "integer", "format": "int32"}, "syncPolicy": {"type": "integer", "format": "int32"}, "teamId": {"type": "integer", "description": "团队ID", "format": "int64"}, "trafficAlertStrategy": {"type": "string", "enum": ["Abort", "Off"]}, "trafficAlertThreshold": {"type": "integer", "format": "int32"}, "trafficSaving": {"type": "boolean"}, "type": {"type": "string", "enum": ["Global", "Local", "None"]}, "upTraffic": {"type": "integer", "description": "上行流量", "format": "int64"}, "webSecurity": {"type": "boolean"}}}, "ShopLanProxyDto": {"title": "ShopLanProxyDto", "type": "object", "properties": {"enabled": {"type": "boolean"}, "host": {"type": "string"}, "hostDomestic": {"type": "boolean"}, "hostLocationId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "latitude": {"type": "number", "format": "double"}, "locale": {"type": "string"}, "locationId": {"type": "integer", "format": "int64"}, "longitude": {"type": "number", "format": "double"}, "networkType": {"type": "string", "enum": ["UseDirect", "UseProxy", "UseSystem"]}, "password": {"type": "string"}, "port": {"type": "integer", "format": "int32"}, "probeOnSession": {"type": "boolean"}, "proxyType": {"type": "string"}, "remoteIp": {"type": "string"}, "sshKey": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}, "timezone": {"type": "string"}, "updateTime": {"type": "string", "format": "date-time"}, "username": {"type": "string"}}}, "ShopPlatformVo": {"title": "ShopPlatformVo", "type": "object", "properties": {"area": {"type": "string", "enum": ["Argentina", "Australia", "Austria", "Belarus", "Belgium", "Bolivia", "Brazil", "Canada", "Chile", "China", "Colombia", "Costa_Rica", "Dominican", "Ecuador", "Egypt", "France", "Germany", "Global", "Guatemala", "Honduras", "HongKong", "India", "Indonesia", "Ireland", "Israel", "Italy", "Japan", "Kazakhstan", "Korea", "Malaysia", "Mexico", "Netherlands", "Nicaragua", "Panama", "Paraguay", "Peru", "Philippines", "Poland", "Portuguese", "Puerto_Rico", "Russia", "Salvador", "Saudi_Arabia", "Singapore", "Spain", "Sweden", "Switzerland", "Taiwan", "Thailand", "Turkey", "United_Arab_Emirates", "United_Kingdom", "United_States", "Uruguay", "Venezuela", "Vietnam"]}, "category": {"type": "string", "enum": ["IM", "Mail", "Other", "Payment", "Shop", "SocialMedia"]}, "frontUrl": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "loginUrl": {"type": "string"}, "name": {"type": "string"}, "typeName": {"type": "string"}}}, "ShopResourcePolicyVo": {"title": "ShopResourcePolicyVo", "type": "object", "properties": {"image": {"type": "boolean"}, "video": {"type": "boolean"}}}, "ShopRouterDto": {"title": "ShopRouterDto", "type": "object", "properties": {"channelId": {"type": "integer", "format": "int64"}, "channelType": {"type": "string", "enum": ["Direct", "None", "Official", "Primary", "Secondary"]}, "createTime": {"type": "string", "format": "date-time"}, "description": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "orderNo": {"type": "integer", "format": "int32"}, "rule": {"type": "string"}, "ruleType": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "DomainWildcard", "UrlPattern", "UrlWildcard"]}, "shopId": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}, "valid": {"type": "boolean"}}}, "ShopSyncPolicyVo": {"title": "ShopSyncPolicyVo", "type": "object", "properties": {"cookies": {"type": "boolean"}, "extensions": {"type": "boolean"}, "history": {"type": "boolean"}, "indexDb": {"type": "boolean"}, "localStorage": {"type": "boolean"}, "passwords": {"type": "boolean"}, "shopBookmarks": {"type": "boolean"}, "supportingCache": {"type": "boolean"}, "teamBookmarks": {"type": "boolean"}, "userBookmarks": {"type": "boolean"}}}, "TagDto": {"title": "TagDto", "type": "object", "properties": {"bizCode": {"type": "string"}, "color": {"type": "integer", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}, "expireTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "resourceType": {"type": "string", "enum": ["AK", "Activity", "Audit", "BlockElements", "Cloud", "CrsOrder", "CrsProduct", "DiskFile", "FingerPrint", "FingerPrintTemplate", "Gateway", "GhCreator", "GhGifter", "GhJobPlan", "GhUser", "GhVideoCreator", "GiftCardPack", "InsTeamUser", "InsUser", "Invoice", "Ip", "IpPool", "IppIp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "KakaoFriend", "KolCreator", "MobileAccount", "None", "Orders", "PluginTeamPack", "Record", "RpaFlow", "RpaTask", "RpaTaskItem", "RpaVoucher", "Shop", "ShopSession", "Tag", "TeamDiskRoot", "TeamMobile", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TkCreator", "TkTeamPack", "Tkshop", "<PERSON>ks<PERSON><PERSON>uyer", "TkshopCreator", "TrafficPack", "TunnelVps", "Users", "View", "Voucher", "XhsAccount"]}, "system": {"type": "boolean"}, "tag": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}}}, "TeamIpVo": {"title": "TeamIpVo", "type": "object", "properties": {"autoRenew": {"type": "boolean"}, "cloudProvider": {"type": "string"}, "cloudRegion": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "directDownTraffic": {"type": "integer", "format": "int64"}, "directUpTraffic": {"type": "integer", "format": "int64"}, "domestic": {"type": "boolean"}, "downTraffic": {"type": "integer", "format": "int64"}, "dynamic": {"type": "boolean"}, "eipId": {"type": "integer", "format": "int64"}, "enableWhitelist": {"type": "boolean"}, "expireStatus": {"type": "string", "description": "过期状态", "enum": ["Expired", "Expiring", "Normal"]}, "forbiddenLongLatitude": {"type": "boolean"}, "gatewayId": {"type": "integer", "format": "int64"}, "goodsId": {"type": "integer", "format": "int64"}, "goodsType": {"type": "string", "enum": ["Credit", "CreditPack", "ExclusiveIp", "FingerprintQuota", "IosDeveloperApprove", "Ip", "IpGo", "IpProxy", "MarketFlow", "None", "PluginPack", "PriceDifference", "ProxyTraffic", "RpaCaptcha", "RpaExecuteQuota", "RpaMobile", "RpaOpenAi", "RpaSendEmail", "RpaSendSms", "RpaSendWeChat", "Rpa_Voucher_Base", "Rpa_Voucher_Performance", "SharingIp", "ShopQuota", "ShopSecurityPolicy", "StorageQuota", "TeamMemberQuota", "Team<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TkPack", "TkPackTrail", "Tkshop", "TkshopEnterprise", "TkshopStandard", "Traffic", "TransitTraffic", "TransitTrafficV2", "UserExclusiveIp", "Voucher"]}, "id": {"type": "integer", "format": "int64"}, "importType": {"type": "string", "enum": ["Platform", "User"]}, "invalidTime": {"type": "string", "format": "date-time"}, "ip": {"type": "string"}, "lastProbeTime": {"type": "string", "format": "date-time"}, "latitude": {"type": "number", "format": "double"}, "locale": {"type": "string"}, "locationId": {"type": "integer", "format": "int64"}, "longitude": {"type": "number", "format": "double"}, "name": {"type": "string"}, "networkType": {"type": "string", "enum": ["cloudIdc", "mobile", "proxyIdc", "residential", "unknown", "unknownIdc"]}, "operateStatus": {"type": "string", "enum": ["shared", "sharing", "sole", "transferring"]}, "originalTeam": {"type": "integer", "format": "int64"}, "periodUnit": {"type": "string", "enum": ["Buyout", "Byte", "GB", "GB天", "个", "个天", "分钟", "周", "天", "年", "张", "无", "月", "次"]}, "pipeType": {"type": "string", "enum": ["None", "Proxy", "Tunnel", "TunnelFailToProxy"]}, "preferTransit": {"type": "integer", "format": "int64"}, "probeError": {"type": "string"}, "providerName": {"type": "string", "description": "供应商名称"}, "realIp": {"type": "string"}, "refreshUrl": {"type": "string"}, "remoteLogin": {"type": "boolean"}, "renewPrice": {"type": "number", "format": "bigdecimal"}, "source": {"type": "string"}, "speedLimit": {"type": "integer", "format": "int32"}, "status": {"type": "string", "enum": ["Available", "Pending", "Unavailable"]}, "sticky": {"type": "boolean"}, "teamId": {"type": "integer", "format": "int64"}, "testingTime": {"type": "integer", "format": "int32"}, "timezone": {"type": "string"}, "traffic": {"type": "number", "format": "double"}, "trafficCurrency": {"type": "string", "enum": ["CREDIT", "RMB", "USD"]}, "trafficPrice": {"type": "number", "format": "bigdecimal"}, "trafficUnlimited": {"type": "boolean"}, "transitType": {"type": "string", "enum": ["Auto", "Direct", "Transit"]}, "tunnelTypes": {"type": "string"}, "upTraffic": {"type": "integer", "format": "int64"}, "valid": {"type": "boolean"}, "validEndDate": {"type": "string", "format": "date-time"}, "vpsId": {"type": "integer", "format": "int64"}}}, "UpdateIpPoolBasicInfoRequest": {"title": "UpdateIpPoolBasicInfoRequest", "type": "object", "properties": {"connectTransitIds": {"type": "array", "description": "白名单连接的接入点", "items": {"type": "integer", "format": "int64"}}, "countryCode": {"type": "string"}, "description": {"type": "string"}, "domestic": {"type": "boolean"}, "enableWhitelist": {"type": "boolean", "description": "是否开启白名单", "example": false}, "name": {"type": "string"}, "transitType": {"type": "string", "enum": ["Auto", "Direct", "Transit"]}, "tunnelTypes": {"type": "array", "items": {"type": "string", "enum": ["clash", "direct", "jump", "localFrontend", "platform", "transit"]}}}}, "UpdateIpPoolProviderParamRequest": {"title": "UpdateIpPoolProviderParamRequest", "type": "object", "properties": {"params": {"type": "object", "additionalProperties": {"type": "string"}}}}, "UpdateIpPoolStrategyRequest": {"title": "UpdateIpPoolStrategyRequest", "type": "object", "properties": {"allocateStrategy": {"type": "string", "enum": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ByLoad", "ByOrder", "ByRandom"]}, "exclusive": {"type": "boolean"}, "lifetime": {"type": "integer", "format": "int32"}, "produceFromTransit": {"type": "boolean"}, "produceStrategy": {"type": "string", "enum": ["ProduceOnEmpty", "ProduceOnHand", "ProduceOnRequest"]}, "releaseStrategy": {"type": "string", "enum": ["Discard", "Reuse"]}, "transitIds": {"type": "array", "description": "提取接入点", "items": {"type": "integer", "format": "int64"}}}}, "UserVo": {"title": "UserVo", "type": "object", "properties": {"account": {"type": "string", "description": "账号"}, "avatar": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "email": {"type": "string", "description": "邮箱"}, "gender": {"type": "string", "enum": ["FEMALE", "MALE", "UNSPECIFIC"]}, "id": {"type": "integer", "format": "int64"}, "nickname": {"type": "string"}, "partnerId": {"type": "integer", "format": "int64"}, "phone": {"type": "string", "description": "手机"}, "residentCity": {"type": "string"}, "signature": {"type": "string"}, "status": {"type": "string", "enum": ["ACTIVE", "BLOCK", "DELETED", "INACTIVE"]}, "tenant": {"type": "integer", "format": "int64"}, "userType": {"type": "string", "enum": ["NORMAL", "PARTNER", "SHADOW"]}, "weixin": {"type": "string"}}}, "WebResult": {"title": "WebResult", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«AllocIppIpResult»": {"title": "WebResult«AllocIppIpResult»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/AllocIppIpResult"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«IpPoolDto»": {"title": "WebResult«IpPoolDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/IpPoolDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«IppPoolWithParamsVo»": {"title": "WebResult«IppPoolWithParamsVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/IppPoolWithParamsVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«IppWhitelistConfig»": {"title": "WebResult«IppWhitelistConfig»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/IppWhitelistConfig"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«CountryDto»»": {"title": "WebResult«List«CountryDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/CountryDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«IppParamDefDto»»": {"title": "WebResult«List«IppParamDefDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/IppParamDefDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«IppProviderDto»»": {"title": "WebResult«List«IppProviderDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/IppProviderDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«IppIpDto»»": {"title": "WebResult«PageResult«IppIpDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«IppIpDto»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«IppPoolVo»»": {"title": "WebResult«PageResult«IppPoolVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«IppPoolVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«long»": {"title": "WebResult«long»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "integer", "format": "int64"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}}, "securitySchemes": {"Authorization": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}, "x-dm-team-id": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-dm-team-id", "in": "header"}}}}
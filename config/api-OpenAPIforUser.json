{"openapi": "3.0.3", "info": {"title": "开放API", "version": "App version 1.0,Spring Boot Version:2.7.14"}, "servers": [{"url": "http://dev.thinkoncloud.cn", "description": "Inferred Url"}], "tags": [{"name": "1 OAuth Api", "description": "认证 API"}, {"name": "10 IP Api", "description": "IP API"}, {"name": "11 Shortcut Api", "description": "快捷方式 API"}, {"name": "12 JDEIP Api", "description": "住宅 IP API"}, {"name": "2 User Api", "description": "用户 API"}, {"name": "3 Task Api", "description": "任务 API"}, {"name": "4 Session Api", "description": "会话 API"}, {"name": "5 Account <PERSON><PERSON>", "description": "分身 API"}, {"name": "6 JDEIP Api", "description": "住宅IP API"}, {"name": "7 Rpa Flow Api", "description": "Rpa 流程 API"}, {"name": "8 Extension Api", "description": "插件 API"}, {"name": "9 <PERSON>ce Api", "description": "登录设备 API"}], "paths": {"/api/openapi/extension/page": {"get": {"tags": ["插件API"], "summary": "分页获取插件列表", "operationId": "openapiExtensionPageGet", "parameters": [{"name": "pageNum", "in": "query", "description": "pageNum", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "category", "in": "query", "description": "插件类别，为空表示全部类别", "required": false, "style": "form", "schema": {"type": "string", "enum": ["ChooseGoods", "General", "Marketing", "Productivity", "ShopOperator"]}}, {"name": "q", "in": "query", "description": "模糊匹配标题", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«ExtensionsVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/jdeip/probe": {"get": {"tags": ["住宅IPAPI"], "summary": "探测住宅IP获取remoteIp", "operationId": "openapiJdeipProbeGet", "parameters": [{"name": "ipId", "in": "query", "description": "ipId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/jdeip/refresh": {"get": {"tags": ["住宅IPAPI"], "summary": "重置住宅IP", "operationId": "openapiJdeipRefreshGet", "parameters": [{"name": "ipId", "in": "query", "description": "ipId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«OpenapiTaskDto»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/account": {"post": {"tags": ["分身API"], "summary": "创建分身", "operationId": "openapiAccountPost", "parameters": [{"name": "name", "in": "query", "description": "分身名称", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "platformType", "in": "query", "description": "分身平台", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "area", "in": "query", "description": "平台所在区域", "required": false, "style": "form", "schema": {"type": "string", "enum": ["Argentina", "Australia", "Austria", "Belarus", "Belgium", "Bolivia", "Brazil", "Canada", "Chile", "China", "Colombia", "Costa_Rica", "Dominican", "Ecuador", "Egypt", "France", "Germany", "Global", "Guatemala", "Honduras", "HongKong", "India", "Indonesia", "Ireland", "Israel", "Italy", "Japan", "Kazakhstan", "Korea", "Malaysia", "Mexico", "Netherlands", "Nicaragua", "Panama", "Paraguay", "Peru", "Philippines", "Poland", "Portuguese", "Puerto_Rico", "Russia", "Salvador", "Saudi_Arabia", "Singapore", "Spain", "Sweden", "Switzerland", "Taiwan", "Thailand", "Turkey", "United_Arab_Emirates", "United_Kingdom", "United_States", "Uruguay", "Venezuela", "Vietnam"]}}, {"name": "type", "in": "query", "description": "分身类型", "required": true, "style": "form", "schema": {"type": "string", "enum": ["Global", "Local", "None"]}}, {"name": "description", "in": "query", "description": "描述", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "stateless", "in": "query", "description": "是否无状态", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "autoBindFingerprint", "in": "query", "description": "是否自动绑定空闲指纹", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "mobile", "in": "query", "description": "mobile", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "tags", "in": "query", "description": "标签，可以用逗号分隔多个标签", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«AccountVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/account/batch": {"post": {"tags": ["分身API"], "summary": "批量创建分身", "operationId": "openapiAccountBatchPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateShopsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«AccountVo»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/account/batchTag": {"post": {"tags": ["分身API"], "summary": "批量给分身打标签", "operationId": "openapiAccountBatchTagPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountBatchTagRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/account/batchUntag": {"post": {"tags": ["分身API"], "summary": "批量取消分身的标签", "operationId": "openapiAccountBatchUntagPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountBatchTagRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/account/bindIp": {"post": {"tags": ["分身API"], "summary": "批量绑定IP", "operationId": "openapiAccountBindIpPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountBatchBindIpRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/account/bindIpPool": {"post": {"tags": ["分身API"], "summary": "批量绑定IP池", "operationId": "openapiAccountBindIpPoolPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountBatchBindIpPoolRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/account/byName": {"get": {"tags": ["分身API"], "summary": "根据名称获取分身详情", "operationId": "openapiAccountByNameGet", "parameters": [{"name": "name", "in": "query", "description": "分身名称", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«AccountVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/account/extensions": {"put": {"tags": ["分身API"], "summary": "批量设置分身的插件", "operationId": "openapiAccountExtensionsPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountBatchUpdateExtensionRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/account/grantToUsers": {"post": {"tags": ["分身API"], "summary": "授权给用户", "operationId": "openapiAccountGrantToUsersPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GrantToUsersRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/account/page": {"get": {"tags": ["分身API"], "summary": "分页获取分身列表", "operationId": "openapiAccountPageGet", "parameters": [{"name": "pageNum", "in": "query", "description": "pageNum", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "sortFiled", "in": "query", "description": "sortFiled", "required": false, "style": "form", "schema": {"type": "string", "enum": ["createTime", "id", "name"]}}, {"name": "sortOrder", "in": "query", "description": "sortOrder", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«AccountVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/account/pageByTag": {"get": {"tags": ["分身API"], "summary": "按标签查询分身", "operationId": "openapiAccountPageByTagGet", "parameters": [{"name": "tag", "in": "query", "description": "标签，支持多个标签，用逗号连接", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "lc", "in": "query", "description": "逻辑符号，查询多个标签时，支持AND或者OR，默认是AND", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "pageNum", "in": "query", "description": "pageNum", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "sortFiled", "in": "query", "description": "sortFiled", "required": false, "style": "form", "schema": {"type": "string", "enum": ["createTime", "id", "name"]}}, {"name": "sortOrder", "in": "query", "description": "sortOrder", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«AccountVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/account/platformList": {"get": {"tags": ["分身API"], "summary": "获取分身所属平台列表", "operationId": "openapiAccountPlatformListGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«ShopPlatformDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/account/tags": {"get": {"tags": ["分身API"], "summary": "返回分身标签列表", "operationId": "openapiAccountTagsGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«string»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/account/unbind": {"post": {"tags": ["分身API"], "summary": "批量解除绑定的IP或IP池", "operationId": "openapiAccountUnbindPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountBatchRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/account/{id}": {"get": {"tags": ["分身API"], "summary": "获取分身详情", "operationId": "openapiAccountByIdGet", "parameters": [{"name": "id", "in": "path", "description": "分身ID", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«AccountVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/account/{id}/cookie": {"post": {"tags": ["分身API"], "summary": "给特定分身上传Cookie信息", "operationId": "openapiAccountByIdCookiePost", "parameters": [{"name": "id", "in": "path", "description": "分身ID", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReportCookieRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/account/{id}/cookies": {"get": {"tags": ["分身API"], "summary": "获取分身的cookie数据", "operationId": "openapiAccountByIdCookiesGet", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«ShopCookieVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/account/{id}/name": {"put": {"tags": ["分身API"], "summary": "修改分身的名称", "description": "分身名称团队内唯一，如果重复，这个接口会抛出异常", "operationId": "openapiAccountByIdNamePut", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "name", "in": "query", "description": "name", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/account/{id}/tag": {"post": {"tags": ["分身API"], "summary": "给分身打标签", "operationId": "openapiAccountByIdTagPost", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "tag", "in": "query", "description": "tag", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TagDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "delete": {"tags": ["分身API"], "summary": "取消分身的标签", "operationId": "openapiAccountByIdTagDelete", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "tag", "in": "query", "description": "tag", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TagDto»"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/bindIp": {"post": {"tags": ["分身API"], "summary": "批量绑定IP", "operationId": "openapiBindIpPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountBatchBindIpRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/bindIpPool": {"post": {"tags": ["分身API"], "summary": "批量绑定IP池", "operationId": "openapiBindIpPoolPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountBatchBindIpPoolRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/copy": {"post": {"tags": ["分身API"], "summary": "复制分身", "operationId": "openapiCopyPost", "parameters": [{"name": "name", "in": "query", "description": "新分身的名称", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "sourceId", "in": "query", "description": "原分身ID", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«AccountVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/grantToUsers": {"post": {"tags": ["分身API"], "summary": "授权给用户", "operationId": "openapiGrantToUsersPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GrantToUsersRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/unbind": {"post": {"tags": ["分身API"], "summary": "批量解除绑定的IP或IP池", "operationId": "openapiUnbindPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountBatchRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/token": {"get": {"tags": ["认证API"], "summary": "获取请求token【请缓存token避免出现频繁调用异常】", "operationId": "openapiTokenGet", "parameters": [{"name": "accessKeyId", "in": "query", "description": "accessKeyId", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "accessKeySecret", "in": "query", "description": "accessKeySecret", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "expireSeconds", "in": "query", "description": "超时时间，单位秒，取值范围：[120,7200]", "required": true, "style": "form", "schema": {"maximum": 7200, "exclusiveMaximum": false, "minimum": 120, "exclusiveMinimum": false, "type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«AkAccessToken»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/device/list": {"get": {"tags": ["登录设备API"], "summary": "获取当前用户的登录设备", "description": "在线或离线但未登录其他用户时可查询", "operationId": "openapiDeviceListGet", "parameters": [{"name": "online", "in": "query", "description": "可指定查询离线或在线的设备", "required": false, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«DeviceVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/device/{deviceId}": {"get": {"tags": ["登录设备API"], "summary": "查询设备", "description": "登录了其他用户也可以查询", "operationId": "openapiDeviceByDeviceIdGet", "parameters": [{"name": "deviceId", "in": "path", "description": "deviceId", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«DeviceVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/ip/import": {"post": {"tags": ["IPAPI"], "summary": "导入一个IP", "operationId": "openapiIpImportPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ImportIpRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«IpVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/ip/page": {"get": {"tags": ["IPAPI"], "summary": "pageIp", "operationId": "openapiIpPageGet", "parameters": [{"name": "creatorId", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "dynamic", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "host", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "importType", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["Platform", "User"]}}, {"name": "pageNum", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "port", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "type", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["http", "socks5", "ssh"]}}, {"name": "username", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«IpVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/rpa/flow/byShareCode": {"post": {"tags": ["Rpa流程API"], "summary": "根据分享码创建流程", "operationId": "openapiRpaFlowByShareCodePost", "parameters": [{"name": "shareCode", "in": "query", "description": "shareCode", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«RpaFlowVo»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/rpa/flow/shareCode": {"post": {"tags": ["Rpa流程API"], "summary": "创建一个流程共享码", "operationId": "openapiRpaFlowShareCodePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateRpaFlowShareRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaFlowShareCodeResult»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/rpa/flow/{flowId}": {"get": {"tags": ["Rpa流程API"], "summary": "获取rpa流程信息", "operationId": "openapiRpaFlowByFlowIdGet", "parameters": [{"name": "flowId", "in": "path", "description": "flowId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaFlowVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/rpa/flows": {"get": {"tags": ["Rpa流程API"], "summary": "获取团队的流程列表", "operationId": "openapiRpaFlowsGet", "parameters": [{"name": "name", "in": "query", "description": "允许按名称模糊查找", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "rpaFlowType", "in": "query", "description": "过滤流程创建类型，为空表示查询所有", "required": false, "style": "form", "schema": {"type": "string", "enum": ["FileCopy", "Manual", "Market", "MarketCopy", "Shared", "TkPack", "Tkshop"]}}, {"name": "sharedFlowId", "in": "query", "description": "分享的源流程ID", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "pageNum", "in": "query", "description": "pageNum", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "groupId", "in": "query", "description": "groupId", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "orderBy", "in": "query", "description": "格式为[field_name asc|desc], field_name must in id,name,create_time,update_time,exec_count,last_exec_time,create_type,sort_no", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«RpaFlowVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/rpa/triggerTask": {"post": {"tags": ["Rpa流程API"], "summary": "触发一个流程task，在指定的机器上执行", "operationId": "openapiRpaTriggerTaskPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewRpaTaskRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpaTaskVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/session/closeByAccountId": {"put": {"tags": ["会话API"], "summary": "关闭账户所有打开的会话（除非指定设备）", "operationId": "openapiSessionCloseByAccountIdPut", "parameters": [{"name": "accountId", "in": "query", "description": "分身ID", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "备注", "in": "query", "description": "备注", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "deviceId", "in": "query", "description": "可以指定关闭特定设备上的会话", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«SessionVo»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/session/closeByAccountName": {"put": {"tags": ["会话API"], "summary": "根据账户名称关闭账户所有打开的会话（除非指定设备）", "operationId": "openapiSessionCloseByAccountNamePut", "parameters": [{"name": "accountName", "in": "query", "description": "分身名称", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "备注", "in": "query", "description": "备注", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "deviceId", "in": "query", "description": "可以指定关闭特定设备上的会话", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«SessionVo»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/session/open": {"post": {"tags": ["会话API"], "summary": "打开会话", "operationId": "openapiSessionOpenPost", "parameters": [{"name": "accountId", "in": "query", "description": "分身ID", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "deviceId", "in": "query", "description": "设备标识，可选", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "remoteDebugPort", "in": "query", "description": "远程调试端口，可选。不填写或取值范围[1024,65535]", "required": false, "style": "form", "schema": {"maximum": 65535, "exclusiveMaximum": false, "minimum": 1024, "exclusiveMinimum": false, "type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«OpenapiTaskDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/session/openByAccountName": {"post": {"tags": ["会话API"], "summary": "根据账户名称打开会话", "operationId": "openapiSessionOpenByAccountNamePost", "parameters": [{"name": "accountName", "in": "query", "description": "分身名称", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "deviceId", "in": "query", "description": "设备标识，可选", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "remoteDebugPort", "in": "query", "description": "远程调试端口，可选。不填写或取值范围[1024,65535]", "required": false, "style": "form", "schema": {"maximum": 65535, "exclusiveMaximum": false, "minimum": 1024, "exclusiveMinimum": false, "type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«OpenapiTaskDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/session/openV2": {"post": {"tags": ["会话API"], "summary": "打开会话V2", "operationId": "openapiSessionOpenV2Post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OpenSessionRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«OpenapiTaskDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/session/{sessionId}": {"get": {"tags": ["会话API"], "summary": "获取会话信息", "operationId": "openapiSessionBySessionIdGet", "parameters": [{"name": "sessionId", "in": "path", "description": "会话ID", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ShopSessionDto»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/session/{sessionId}/close": {"put": {"tags": ["会话API"], "summary": "关闭会话", "operationId": "openapiSessionBySessionIdClosePut", "parameters": [{"name": "sessionId", "in": "path", "description": "会话ID", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "remarks", "in": "query", "description": "备注,可选", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/sessions": {"get": {"tags": ["会话API"], "summary": "查询账户打开的会话", "operationId": "openapiSessionsGet", "parameters": [{"name": "accountId", "in": "query", "description": "分身ID", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«SessionVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/sessions/byAccountName": {"get": {"tags": ["会话API"], "summary": "根据账户名称查询打开的会话", "operationId": "openapiSessionsByAccountNameGet", "parameters": [{"name": "accountName", "in": "query", "description": "分身名称", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«SessionVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/sessions/byDeviceId": {"get": {"tags": ["会话API"], "summary": "根据设备ID查询打开的会话", "operationId": "openapiSessionsByDeviceIdGet", "parameters": [{"name": "deviceId", "in": "query", "description": "设备ID", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«SessionVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/shortcut": {"post": {"tags": ["快捷方式API"], "summary": "创建快捷方式", "operationId": "openapiShortcutPost", "parameters": [{"name": "accountId", "in": "query", "description": "分身ID", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "rpaEnabled", "in": "query", "description": "是否允许执行RPA", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "validDays", "in": "query", "description": "有效期天数，0=永久有效", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "name", "in": "query", "description": "名称", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ShopShortcutDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/shortcut/byToken": {"delete": {"tags": ["快捷方式API"], "summary": "删除快捷方式", "operationId": "openapiShortcutByTokenDelete", "parameters": [{"name": "token", "in": "path", "description": "快捷方式ID", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/shortcut/list": {"get": {"tags": ["快捷方式API"], "summary": "获取分身的所有快捷方式", "operationId": "openapiShortcutListGet", "parameters": [{"name": "accountId", "in": "query", "description": "分身ID", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«ShopShortcutDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/shortcut/{id}": {"delete": {"tags": ["快捷方式API"], "summary": "删除快捷方式", "operationId": "openapiShortcutByIdDelete", "parameters": [{"name": "id", "in": "path", "description": "快捷方式ID", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/task/{requestId}": {"get": {"tags": ["任务API"], "summary": "根据requestId查询任务", "operationId": "openapiTaskByRequestIdGet", "parameters": [{"name": "requestId", "in": "path", "description": "requestId", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«OpenapiTaskModel»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openapi/user/current": {"get": {"tags": ["用户API"], "summary": "获取当前用户信息", "operationId": "openapiUserCurrentGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«UserVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}}, "components": {"schemas": {"AccountBatchBindIpPoolRequest": {"title": "AccountBatchBindIpPoolRequest", "type": "object", "properties": {"ids": {"type": "array", "description": "分身ID列表", "items": {"type": "integer", "format": "int64"}}, "ipPoolId": {"type": "integer", "description": "IP池的ID，与ipPoolName二选一", "format": "int64"}, "ipPoolName": {"type": "string", "description": "IP池的名称，与ipPoolId二选一"}}}, "AccountBatchBindIpRequest": {"title": "AccountBatchBindIpRequest", "type": "object", "properties": {"ids": {"type": "array", "description": "分身ID列表", "items": {"type": "integer", "format": "int64"}}, "ipId": {"type": "integer", "description": "IP的ID，与ipName二选一", "format": "int64"}, "ipName": {"type": "string", "description": "IP的名称（静态IP，就是IP，动态IP是名称）"}}}, "AccountBatchRequest": {"title": "AccountBatchRequest", "type": "object", "properties": {"ids": {"type": "array", "description": "分身ID列表", "items": {"type": "integer", "format": "int64"}}}}, "AccountBatchTagRequest": {"title": "AccountBatchTagRequest", "type": "object", "properties": {"ids": {"type": "array", "description": "分身ID列表", "items": {"type": "integer", "format": "int64"}}, "tags": {"type": "array", "items": {"type": "string"}}}}, "AccountBatchUpdateExtensionRequest": {"title": "AccountBatchUpdateExtensionRequest", "type": "object", "properties": {"extensionIds": {"type": "array", "description": "插件ID列表", "items": {"type": "integer", "format": "int64"}}, "ids": {"type": "array", "description": "分身ID列表", "items": {"type": "integer", "format": "int64"}}}}, "AccountPasswordItemVo": {"title": "AccountPasswordItemVo", "type": "object", "properties": {"password": {"type": "string", "description": "密码"}, "platformId": {"type": "integer", "description": "所属平台ID，可选", "format": "int64"}, "signonRealm": {"type": "string", "description": "可选"}, "url": {"type": "string", "description": "密码所属站点URL"}, "username": {"type": "string", "description": "用户名"}}}, "AccountVo": {"title": "Account<PERSON>o", "type": "object", "properties": {"account": {"type": "string", "description": "账户账号"}, "allowExtension": {"type": "boolean", "description": "是否允许用户自行安装插件", "example": false}, "allowMonitor": {"type": "boolean", "description": "会话是否允许监视", "example": false}, "allowSkip": {"type": "boolean", "description": "是否允许跳过敏感操作", "example": false}, "autoFill": {"type": "boolean", "description": "是否自动代填", "example": false}, "bookmarkBar": {"type": "string"}, "coordinateId": {"type": "string"}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time"}, "creatorId": {"type": "integer", "description": "创建者", "format": "int64"}, "deleteTime": {"type": "string", "format": "date-time"}, "deleting": {"type": "boolean"}, "description": {"type": "string", "description": "描述"}, "domainPolicy": {"type": "string", "enum": ["Blacklist", "None", "Whitelist"]}, "downTraffic": {"type": "integer", "description": "下行流量", "format": "int64"}, "exclusive": {"type": "boolean", "description": "是否独占访问", "example": false}, "extraProp": {"type": "string"}, "fingerprintId": {"type": "integer", "description": "绑定的指纹Id", "format": "int64"}, "fingerprintTemplateId": {"type": "integer", "description": "绑定的指纹模板Id（只有无状态账号才允许绑定指纹模板）", "format": "int64"}, "frontUrl": {"type": "string"}, "googleTranslateSpeed": {"type": "boolean"}, "homePage": {"type": "string"}, "homePageSites": {"type": "string"}, "id": {"type": "integer", "description": "id", "format": "int64"}, "imageForbiddenSize": {"type": "integer", "format": "int64"}, "intranetEnabled": {"type": "boolean"}, "ipId": {"type": "integer", "description": "绑定的主IP ID", "format": "int64"}, "ipSwitchCheckInterval": {"type": "integer", "format": "int32"}, "ipSwitchStrategy": {"type": "string", "enum": ["Abort", "<PERSON><PERSON>", "Off"]}, "ippId": {"type": "integer", "description": "绑定的主IP池 ID", "format": "int64"}, "lastAccessTime": {"type": "string", "format": "date-time"}, "lastAccessUser": {"type": "integer", "format": "int64"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "loginStatus": {"type": "string", "enum": ["Offline", "Online", "Unknown"]}, "markCode": {"type": "string", "description": "任务栏分身标记文字"}, "markCodeBg": {"type": "integer", "description": "任务栏分身标记背景颜色", "format": "int32"}, "monitorPerception": {"type": "boolean"}, "name": {"type": "string", "description": "账户名称"}, "nameBookmarkEnabled": {"type": "boolean"}, "operateStatus": {"type": "string", "enum": ["shared", "sharing", "sole", "transferring"]}, "operatingCategory": {"type": "string", "description": "经营品类", "enum": ["医药保健", "图书文具", "宠物用品", "家具建材", "家电电器", "工业用品", "户外运动", "手机数码", "手表眼镜", "护肤美妆", "母婴玩具", "汽车配件", "生活家居", "电商其他", "电脑平板", "艺术珠宝", "花园聚会", "计生情趣", "软件程序", "鞋服箱包", "音乐影视", "食品生鲜", "鲜花绿植"]}, "parentShopId": {"type": "integer", "format": "int64"}, "password": {"type": "string", "description": "密码"}, "platformId": {"type": "integer", "description": "平台ID", "format": "int64"}, "privateAddress": {"type": "string"}, "privateTitle": {"type": "string"}, "recordPerception": {"type": "boolean"}, "recordPolicy": {"type": "string", "enum": ["<PERSON><PERSON>", "Disabled", "Forced"]}, "requireIp": {"type": "boolean"}, "resourcePolicy": {"type": "integer", "format": "int32"}, "securityPolicyEnabled": {"type": "boolean"}, "securityPolicyUpdateTime": {"type": "string", "format": "date-time"}, "sharePolicyId": {"type": "string"}, "shopDataSize": {"type": "integer", "format": "int64"}, "stateless": {"type": "boolean"}, "statelessChangeFp": {"type": "boolean"}, "statelessSyncPolicy": {"type": "integer", "format": "int32"}, "syncPolicy": {"type": "integer", "format": "int32"}, "teamId": {"type": "integer", "description": "团队ID", "format": "int64"}, "trafficAlertStrategy": {"type": "string", "enum": ["Abort", "Off"]}, "trafficAlertThreshold": {"type": "integer", "format": "int32"}, "trafficSaving": {"type": "boolean"}, "type": {"type": "string", "enum": ["Global", "Local", "None"]}, "upTraffic": {"type": "integer", "description": "上行流量", "format": "int64"}, "webSecurity": {"type": "boolean"}}}, "AkAccessToken": {"title": "AkAccessToken", "type": "object", "properties": {"expireTime": {"type": "string", "description": "Token超时时间", "format": "date-time"}, "token": {"type": "string", "description": "登录或请求的token"}}}, "CreateRpaFlowShareRequest": {"title": "CreateRpaFlowShareRequest", "type": "object", "properties": {"allowPushUpdate": {"type": "boolean", "description": "是否允许分享方主动推送更新，默认false", "example": false}, "allowRead": {"type": "boolean", "description": "是否可读，默认false", "example": false}, "groupNames": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}, "description": "具体要带上的分组信息，{1111: [分组1, 分组2], 2222: [分组2]}"}, "includeGroups": {"type": "boolean", "description": "是否包含流程的分组信息，默认false", "example": false}, "rpaFlowIds": {"type": "array", "description": "分享哪些流程", "items": {"type": "integer", "format": "int64"}}, "validMinutes": {"type": "integer", "description": "有效期（分钟）", "format": "int32"}}}, "CreateShopsRequest": {"title": "CreateShopsRequest", "type": "object", "properties": {"allowMonitor": {"type": "boolean", "description": "会话是否允许监视", "example": false}, "allowSkip": {"type": "boolean", "description": "允许跳过敏感操作", "example": false}, "count": {"type": "integer", "description": "创建PC指纹分身数量", "format": "int32"}, "description": {"type": "string", "description": "备注"}, "disableLongLatitude": {"type": "boolean"}, "disableWebrtc": {"type": "boolean"}, "exclusive": {"type": "boolean", "description": "是否独占访问", "example": false}, "homePageType": {"type": "string", "description": "浏览器首页类型", "enum": ["checker", "defaults", "frontUrl", "loginUrl", "restoreSession"]}, "ipId": {"type": "integer", "description": "绑定的IP", "format": "int64"}, "ippId": {"type": "integer", "description": "绑定的IP池", "format": "int64"}, "mobileCount": {"type": "integer", "description": "创建手机指纹分身数量", "format": "int32"}, "mobileParams": {"description": "如何生成android指纹", "$ref": "#/components/schemas/GenFingerParams"}, "namePrefix": {"type": "string", "description": "店铺名称前缀，创建数量为1时，代表分身名称"}, "operatingCategory": {"type": "string", "description": "经营品类", "enum": ["医药保健", "图书文具", "宠物用品", "家具建材", "家电电器", "工业用品", "户外运动", "手机数码", "手表眼镜", "护肤美妆", "母婴玩具", "汽车配件", "生活家居", "电商其他", "电脑平板", "艺术珠宝", "花园聚会", "计生情趣", "软件程序", "鞋服箱包", "音乐影视", "食品生鲜", "鲜花绿植"]}, "password": {"type": "string", "description": "店铺账号密码"}, "pcParams": {"description": "如何生成pc指纹", "$ref": "#/components/schemas/GenFingerParams"}, "platformId": {"type": "integer", "description": "平台ID", "format": "int64"}, "recordPolicy": {"type": "string", "description": "录像策略", "enum": ["<PERSON><PERSON>", "Disabled", "Forced"]}, "shopLanProxy": {"description": "本地代理", "$ref": "#/components/schemas/ShopLanProxyVo"}, "stateless": {"type": "boolean", "description": "是否创建无状态分身", "example": false}, "type": {"type": "string", "description": "店铺类型", "enum": ["Global", "Local", "None"]}, "username": {"type": "string", "description": "店铺账号"}}}, "DeviceVo": {"title": "DeviceVo", "type": "object", "properties": {"clientIp": {"type": "string", "description": "最后登录IP"}, "cpus": {"type": "integer", "description": "CPU核数", "format": "int32"}, "createTime": {"type": "string", "description": "首次运行时间", "format": "date-time"}, "deviceId": {"type": "string", "description": "设备唯一标识"}, "hostName": {"type": "string", "description": "主机名称"}, "id": {"type": "integer", "description": "数据库ID", "format": "int64"}, "lastActiveTime": {"type": "string", "description": "最后活跃时间", "format": "date-time"}, "lastCity": {"type": "string", "description": "最后登录城市"}, "lastUserId": {"type": "integer", "description": "最后登录用户", "format": "int64"}, "mem": {"type": "integer", "description": "内存(bytes)", "format": "int64"}, "online": {"type": "boolean", "description": "是否在线", "example": false}, "osName": {"type": "string", "description": "操作系统"}, "sessionCount": {"type": "integer", "description": "打开的会话数", "format": "int32"}, "userAgent": {"type": "string"}, "version": {"type": "string", "description": "客户端版本号"}}}, "ExtensionsVo": {"title": "ExtensionsVo", "type": "object", "properties": {"category": {"type": "string", "description": "插件类别", "enum": ["ChooseGoods", "General", "Marketing", "Productivity", "ShopOperator"]}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time"}, "crx": {"type": "string", "description": "crx文件路径"}, "description": {"type": "string", "description": "详细描述"}, "icon": {"type": "string", "description": "图标;图标地址"}, "id": {"type": "integer", "format": "int64"}, "installed": {"type": "boolean"}, "provider": {"type": "string", "description": "提供商"}, "providerSite": {"type": "string", "description": "提供商网站"}, "publishTime": {"type": "string", "description": "上架时间", "format": "date-time"}, "score": {"type": "integer", "description": "评分，0-100分", "format": "int32"}, "shortDesc": {"type": "string", "description": "简短描述"}, "status": {"type": "string", "description": "当前状态;正上架，已下架等", "enum": ["Normal", "OffLine"]}, "storeUpdateDate": {"type": "string", "description": "插件市场最后一次更新日期(可能为空)", "format": "date-time"}, "teamId": {"type": "integer", "description": "如果不为空且不为0，表明该插件是团队自有插件", "format": "int64"}, "title": {"type": "string", "description": "标题"}, "updateTime": {"type": "string", "description": "更新时间", "format": "date-time"}, "userCount": {"type": "integer", "description": "用户数", "format": "int32"}, "version": {"type": "string", "description": "版本号"}}}, "GenFingerParams": {"title": "GenFingerParams", "type": "object", "properties": {"browser": {"type": "string", "enum": ["Chrome", "Edge", "Safari"]}, "platform": {"type": "string", "enum": ["Android", "IOS", "Linux", "<PERSON>", "Windows"]}, "version": {"type": "integer", "description": "生成的ua版本，如果为空表示随机版本", "format": "int32"}}}, "GrantToUsersRequest": {"title": "GrantToUsersRequest", "type": "object", "properties": {"accountIds": {"type": "array", "description": "分身ID", "items": {"type": "integer", "format": "int64"}}, "cleanFirst": {"type": "boolean", "description": "先清理分身原有的用户授权关系", "example": false}, "userIds": {"type": "array", "description": "用户ID", "items": {"type": "integer", "format": "int64"}}}}, "ImportIpRequest": {"title": "ImportIpRequest", "type": "object", "properties": {"description": {"type": "string", "description": "描述（可选）"}, "dynamic": {"type": "boolean", "description": "是否动态IP（必填）", "example": false}, "host": {"type": "string", "description": "代理地址（必填）"}, "password": {"type": "string", "description": "密码（可选）"}, "port": {"type": "integer", "description": "端口（必填）", "format": "int32"}, "refreshUrl": {"type": "string", "description": "隧道IP的刷新地址（可选）"}, "sshKey": {"type": "string", "description": "ssh登录私钥（可选，当type=ssh时）"}, "type": {"type": "string", "description": "代理协议（必填）", "enum": ["http", "socks5", "ssh"]}, "username": {"type": "string", "description": "用户名（可选）"}}}, "IpVo": {"title": "IpVo", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "domestic": {"type": "boolean"}, "dynamic": {"type": "boolean"}, "forbiddenLongLatitude": {"type": "boolean"}, "host": {"type": "string"}, "hostDomestic": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "importType": {"type": "string", "enum": ["Platform", "User"]}, "ip": {"type": "string"}, "latitude": {"type": "number", "format": "double"}, "locale": {"type": "string"}, "locationId": {"type": "integer", "format": "int64"}, "longitude": {"type": "number", "format": "double"}, "name": {"type": "string"}, "port": {"type": "integer", "format": "int32"}, "refreshUrl": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}, "timezone": {"type": "string"}, "type": {"type": "string"}, "username": {"type": "string"}, "valid": {"type": "boolean"}}}, "ItemShopInfo": {"title": "ItemShopInfo", "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}}}, "JDEIpAllocationVo": {"title": "JDEIpAllocationVo", "type": "object", "properties": {"cityId": {"type": "integer", "format": "int64"}, "cityName": {"type": "string"}, "gatewayId": {"type": "integer", "format": "int64"}, "port": {"type": "integer", "format": "int32"}}}, "NewRpaTaskRequest": {"title": "NewRpaTaskRequest", "type": "object", "properties": {"caseBrowserOpened": {"type": "string", "description": "当账号的浏览器已打开时的策略：stop | reopen | reuse"}, "closeBrowserOnEnd": {"type": "string", "description": "流程结束后是否关闭账号浏览器：close | keep"}, "cloudInstanceId": {"type": "integer", "format": "int64"}, "concurrent": {"type": "integer", "format": "int32"}, "concurrentDelay": {"type": "integer", "description": "并发等待时间间隔", "format": "int32"}, "description": {"type": "string"}, "deviceId": {"type": "string"}, "environments": {"type": "object", "description": "任务里可以通过 rpa.getEnv(key) 来获取传递的值"}, "forceRecord": {"type": "boolean"}, "formId": {"type": "string"}, "headless": {"type": "boolean", "description": "是否以无头模式运行", "example": false}, "layoutConfig": {"type": "string", "description": "窗口布局配置,json格式"}, "manualRun": {"type": "boolean", "description": "是否手动执行", "example": false}, "name": {"type": "string"}, "params": {"type": "object", "additionalProperties": {"type": "object"}, "description": "创建一个task时用来指定变量值。如果指定的key不在流程定义里会被忽略"}, "popupTaskLog": {"type": "boolean", "description": "是否自动弹出流程日志窗口，为空表示false", "example": false}, "provider": {"type": "string", "description": "云端执行的云厂商", "enum": ["<PERSON><PERSON><PERSON>", "aws", "aws_cn", "aws_ls", "azure", "azure_cn", "baidu", "bao<PERSON><PERSON>", "bluevps", "dmit", "ecloud10086", "googlecloud", "hua<PERSON>", "huayang", "huo<PERSON>", "jdbox", "jdcloud", "jdeip", "lan", "oracle", "other", "qcloud", "raincloud", "ucloud", "vlcloud", "vps", "yge<PERSON>"]}, "region": {"type": "string", "description": "云端执行的区域"}, "rpaFlowId": {"type": "integer", "format": "int64"}, "rpaFlowVersion": {"type": "string", "description": "手动直接执行一个流程的时候（不包含计划）支持直接执行指定版本"}, "runOnCloud": {"type": "boolean", "description": "是否云端执行", "example": false}, "scheduleId": {"type": "integer", "format": "int64"}, "scheduleJobId": {"type": "string", "description": "被哪个quartz调度任务触发"}, "shopIds": {"type": "array", "description": "有哪些账户参与该流程Task执行，如果是手机流程，指有哪些 mobile 参与该流程的执行", "items": {"type": "integer", "format": "int64"}}, "showMouseTrack": {"type": "boolean", "description": "是否显示鼠标轨迹", "example": false}, "sidePanel": {"type": "string", "description": "执行时侧边栏显示策略。hide | show，为空表示继承流程配置"}, "snapshot": {"type": "string", "enum": ["Node", "Not", "OnFail"]}, "sscToken": {"type": "string", "description": "快捷方式的Token"}}}, "OpenSessionRequest": {"title": "OpenSessionRequest", "type": "object", "properties": {"accountId": {"type": "integer", "description": "分身ID。必填", "format": "int64"}, "browserSwitches": {"type": "string", "description": "浏览器启动参数。通过换行符\\n分割每个参数对。可选"}, "deviceId": {"type": "string", "description": "打开分身的设备标识。可选"}, "policy": {"description": "指定打开会话的策略，覆盖分身的原有策略", "$ref": "#/components/schemas/SessionPolicyVo"}, "remoteDebugPort": {"type": "integer", "description": "远程调试端口，可选。不填写或取值范围[1024,65535]。可选", "format": "int32"}, "sessionIp": {"description": "代理IP相关信息（可选）", "$ref": "#/components/schemas/SessionIpVo"}}}, "OpenSessionRequestV3": {"title": "OpenSessionRequestV3", "type": "object", "properties": {"accountId": {"type": "integer", "description": "分身ID。必填", "format": "int64"}, "browserSwitches": {"type": "string", "description": "浏览器启动参数。通过换行符\\n分割每个参数对。可选"}, "deviceId": {"type": "string", "description": "打开分身的设备标识。可选"}, "fingerprint": {"description": "指纹相关信息", "$ref": "#/components/schemas/SessionFingerprintVo"}, "policy": {"description": "指定打开会话的策略，覆盖分身的原有策略", "$ref": "#/components/schemas/SessionPolicyVo"}, "remoteDebugPort": {"type": "integer", "description": "远程调试端口，可选。不填写或取值范围[1024,65535]。可选", "format": "int32"}, "serialDelay": {"type": "integer", "description": "串行执行的延迟", "format": "int32"}, "serialRun": {"type": "boolean", "description": "在同一设备上串行执行", "example": false}, "sessionIp": {"description": "代理IP相关信息（可选）", "$ref": "#/components/schemas/SessionIpVo"}, "url": {"type": "string", "description": "会话打开以后访问的网站"}}}, "OpenapiTaskDto": {"title": "OpenapiTaskDto", "type": "object", "properties": {"accessKeyId": {"type": "string", "description": "调用者AKID"}, "action": {"type": "string", "description": "任务操作"}, "bizData": {"type": "string", "description": "业务参数"}, "createTime": {"type": "string", "description": "任务创建时间", "format": "date-time"}, "creator": {"type": "integer", "description": "任务创建者ID", "format": "int64"}, "done": {"type": "boolean", "description": "是否已经完成", "example": false}, "finishTime": {"type": "string", "description": "任务完成时间", "format": "date-time"}, "id": {"type": "integer", "description": "任务ID", "format": "int64"}, "name": {"type": "string", "description": "任务名称"}, "progress": {"type": "integer", "description": "进度（如果支持）", "format": "int32"}, "remarks": {"type": "string", "description": "任务关联资源ID（如果支持）"}, "requestId": {"type": "string", "description": "请求ID"}, "resourceId": {"type": "integer", "description": "任务关联资源ID（如果支持）", "format": "int64"}, "resourceType": {"type": "string", "description": "任务关联资源类型（如果支持）", "enum": ["AK", "Activity", "Audit", "BlockElements", "Cloud", "CrsOrder", "CrsProduct", "DiskFile", "FingerPrint", "FingerPrintTemplate", "Gateway", "GhCreator", "GhGifter", "GhJobPlan", "GhUser", "GhVideoCreator", "GiftCardPack", "InsTeamUser", "InsUser", "Invoice", "Ip", "IpPool", "IppIp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "KakaoFriend", "KolCreator", "MobileAccount", "None", "Orders", "PluginTeamPack", "Record", "RpaFlow", "RpaTask", "RpaTaskItem", "RpaVoucher", "Shop", "ShopSession", "Tag", "TeamDiskRoot", "TeamMobile", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TkCreator", "TkTeamPack", "Tkshop", "<PERSON>ks<PERSON><PERSON>uyer", "TkshopCreator", "TrafficPack", "TunnelVps", "Users", "View", "Voucher", "XhsAccount"]}, "serialDelay": {"type": "integer", "format": "int32"}, "serialRunNo": {"type": "string"}, "startTime": {"type": "string", "format": "date-time"}, "startTimeout": {"type": "integer", "format": "int32"}, "status": {"type": "string", "description": "任务状态"}, "teamId": {"type": "integer", "description": "所属团队ID", "format": "int64"}}}, "OpenapiTaskModel": {"title": "OpenapiTaskModel", "type": "object", "properties": {"accessKeyId": {"type": "string", "description": "调用者AKID"}, "action": {"type": "string", "description": "任务操作"}, "bizData": {"type": "string", "description": "业务参数"}, "createTime": {"type": "string", "description": "任务创建时间", "format": "date-time"}, "creator": {"type": "integer", "description": "任务创建者ID", "format": "int64"}, "done": {"type": "boolean", "description": "是否已经完成", "example": false}, "finishTime": {"type": "string", "description": "任务完成时间", "format": "date-time"}, "id": {"type": "integer", "description": "任务ID", "format": "int64"}, "name": {"type": "string", "description": "任务名称"}, "progress": {"type": "integer", "description": "进度（如果支持）", "format": "int32"}, "remarks": {"type": "string", "description": "任务关联资源ID（如果支持）"}, "requestId": {"type": "string", "description": "请求ID"}, "resource": {"type": "object", "description": "关联资源"}, "resourceId": {"type": "integer", "description": "任务关联资源ID（如果支持）", "format": "int64"}, "resourceType": {"type": "string", "description": "任务关联资源类型（如果支持）", "enum": ["AK", "Activity", "Audit", "BlockElements", "Cloud", "CrsOrder", "CrsProduct", "DiskFile", "FingerPrint", "FingerPrintTemplate", "Gateway", "GhCreator", "GhGifter", "GhJobPlan", "GhUser", "GhVideoCreator", "GiftCardPack", "InsTeamUser", "InsUser", "Invoice", "Ip", "IpPool", "IppIp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "KakaoFriend", "KolCreator", "MobileAccount", "None", "Orders", "PluginTeamPack", "Record", "RpaFlow", "RpaTask", "RpaTaskItem", "RpaVoucher", "Shop", "ShopSession", "Tag", "TeamDiskRoot", "TeamMobile", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TkCreator", "TkTeamPack", "Tkshop", "<PERSON>ks<PERSON><PERSON>uyer", "TkshopCreator", "TrafficPack", "TunnelVps", "Users", "View", "Voucher", "XhsAccount"]}, "serialDelay": {"type": "integer", "format": "int32"}, "serialRunNo": {"type": "string"}, "startTime": {"type": "string", "format": "date-time"}, "startTimeout": {"type": "integer", "format": "int32"}, "status": {"type": "string", "description": "任务状态"}, "teamId": {"type": "integer", "description": "所属团队ID", "format": "int64"}}}, "PageResult«AccountVo»": {"title": "PageResult«AccountVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/AccountVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«ExtensionsVo»": {"title": "PageResult«ExtensionsVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/ExtensionsVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«IpVo»": {"title": "PageResult«IpVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/IpVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«RpaFlowVo»": {"title": "PageResult«RpaFlowVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/RpaFlowVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "ReportCookieRequest": {"title": "ReportCookieRequest", "type": "object", "properties": {"cookies": {"type": "array", "items": {"$ref": "#/components/schemas/ShopCookieVo"}}}}, "RpaFlowGroupVo": {"title": "RpaFlowGroupVo", "type": "object", "properties": {"description": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "sortNumber": {"type": "integer", "format": "int32"}, "teamId": {"type": "integer", "format": "int64"}}}, "RpaFlowShareCodeResult": {"title": "RpaFlowShareCodeResult", "type": "object", "properties": {"allowPushUpdate": {"type": "boolean"}, "allowRead": {"type": "boolean"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "expiredTime": {"type": "string", "format": "date-time"}, "flowIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "id": {"type": "integer", "format": "int64"}, "includeGroups": {"type": "boolean"}, "shareCode": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}, "validMinutes": {"type": "integer", "format": "int32"}}}, "RpaFlowVo": {"title": "RpaFlowVo", "type": "object", "properties": {"allowPushUpdate": {"type": "boolean"}, "allowRead": {"type": "boolean", "description": "是否可读。针对分享流程和市场流程", "example": false}, "bizCode": {"type": "string"}, "configId": {"type": "string"}, "console": {"type": "boolean"}, "createTime": {"type": "string", "format": "date-time"}, "createType": {"type": "string", "enum": ["FileCopy", "Manual", "Market", "MarketCopy", "Shared", "TkPack", "Tkshop"]}, "creatorId": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "dirty": {"type": "boolean"}, "expireTime": {"type": "string", "description": "过期时间，仅针对引用市场流程", "format": "date-time"}, "expired": {"type": "boolean", "description": "是否已过期，仅针对引用市场流程，根据 expireTime 计算得出来的", "example": false}, "extra": {"type": "object"}, "flowShareCode": {"type": "string"}, "groups": {"type": "array", "items": {"$ref": "#/components/schemas/RpaFlowGroupVo"}}, "id": {"type": "integer", "format": "int64"}, "marketId": {"type": "integer", "description": "对应的市场模板ID", "format": "int64"}, "marketLatestVersion": {"type": "string", "description": "如果是市场流程，显示市场流程的最新版本"}, "name": {"type": "string"}, "nameBrief": {"type": "string"}, "numberVersion": {"type": "integer", "description": "数字版本号，会从1开始累加", "format": "int32"}, "platforms": {"type": "array", "items": {"$ref": "#/components/schemas/RpaPlatformVo"}}, "publishTime": {"type": "string", "format": "date-time"}, "rpaType": {"type": "string", "description": "流程类型，分手机和browser", "enum": ["Browser", "IOS", "Mobile"]}, "sessionInner": {"type": "boolean"}, "shareFromTeamId": {"type": "integer", "format": "int64"}, "shareFromTeamName": {"type": "string"}, "shareLatestVersion": {"type": "string", "description": "如果是分享过来的流程，显示被分享的流程最新的版本"}, "sharedFlowId": {"type": "integer", "description": "不为空表示是他人分享的流程", "format": "int64"}, "shopId": {"type": "integer", "format": "int64"}, "sortNo": {"type": "integer", "format": "int32"}, "status": {"type": "string", "enum": ["Draft", "Published"]}, "supportConcurrent": {"type": "boolean"}, "teamId": {"type": "integer", "description": "团队ID;", "format": "int64"}, "teamName": {"type": "string"}, "tkFlowId": {"type": "integer", "format": "int64"}, "updateTime": {"type": "string", "format": "date-time"}, "version": {"type": "string"}}}, "RpaPlatformVo": {"title": "RpaPlatformVo", "type": "object", "properties": {"flowId": {"type": "integer", "format": "int64"}, "platformName": {"type": "string"}}}, "RpaTaskVo": {"title": "RpaTaskVo", "type": "object", "properties": {"caseBrowserOpened": {"type": "string", "description": "当账号的浏览器已打开时的策略：stop | reopen | reuse"}, "city": {"type": "string"}, "clientId": {"type": "string"}, "clientIp": {"type": "string"}, "closeBrowserOnEnd": {"type": "string", "description": "流程结束后是否关闭账号浏览器：close | keep"}, "cloudInstanceId": {"type": "integer", "format": "int64"}, "concurrent": {"type": "integer", "format": "int32"}, "concurrentDelay": {"type": "integer", "description": "并发等待时间间隔", "format": "int32"}, "configId": {"type": "string"}, "console": {"type": "boolean"}, "country": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "createType": {"type": "string", "enum": ["FileCopy", "Manual", "Market", "MarketCopy", "Shared", "TkPack", "Tkshop"]}, "creatorId": {"type": "integer", "format": "int64"}, "credit": {"type": "number", "description": "扣掉了多少个花瓣", "format": "bigdecimal"}, "creditDetailId": {"type": "integer", "format": "int64"}, "creditDetailSerialNumber": {"type": "string"}, "description": {"type": "string"}, "deviceName": {"type": "string"}, "done": {"type": "boolean", "description": "是否结束", "example": false}, "errorCode": {"type": "integer", "description": "#see RpaFailReason.xxx", "format": "int32"}, "errorMsg": {"type": "string"}, "executeEndTime": {"type": "string", "format": "date-time"}, "executeTime": {"type": "string", "format": "date-time"}, "executorId": {"type": "integer", "description": "执行者身份。历史数据访字段为空，展示的时候使用creatorId", "format": "int64"}, "failedItems": {"type": "integer", "format": "int32"}, "fileLocked": {"type": "boolean"}, "fileSize": {"type": "integer", "description": "总文件大小，不包括.log文件", "format": "int64"}, "fileStatus": {"type": "string", "enum": ["Deleted", "Undefined", "<PERSON><PERSON>"]}, "flowId": {"type": "integer", "format": "int64"}, "flowName": {"type": "string"}, "flowVersion": {"type": "string"}, "forceRecord": {"type": "boolean"}, "headless": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "layoutConfig": {"type": "string", "description": "窗口布局配置,json格式"}, "manualRun": {"type": "boolean"}, "name": {"type": "string"}, "planId": {"type": "integer", "format": "int64"}, "planName": {"type": "string"}, "planType": {"type": "string", "description": "计划类型（如果是计划触发）", "enum": ["Auto", "Loop", "Manual", "<PERSON><PERSON>"]}, "popupTaskLog": {"type": "boolean", "description": "是否自动弹出流程日志窗口，为空表示false", "example": false}, "preview": {"type": "boolean"}, "price": {"type": "number", "description": "消耗单价:花瓣/分钟", "format": "bigdecimal"}, "provider": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "aws", "aws_cn", "aws_ls", "azure", "azure_cn", "baidu", "bao<PERSON><PERSON>", "bluevps", "dmit", "ecloud10086", "googlecloud", "hua<PERSON>", "huayang", "huo<PERSON>", "jdbox", "jdcloud", "jdeip", "lan", "oracle", "other", "qcloud", "raincloud", "ucloud", "vlcloud", "vps", "yge<PERSON>"]}, "region": {"type": "string"}, "rpaType": {"type": "string", "description": "流程类型，分手机和browser", "enum": ["Browser", "IOS", "Mobile"]}, "rpaVoucherId": {"type": "integer", "format": "int64"}, "runOnCloud": {"type": "boolean"}, "shops": {"type": "array", "items": {"$ref": "#/components/schemas/ItemShopInfo"}}, "showMouseTrack": {"type": "boolean", "description": "是否显示鼠标轨迹", "example": false}, "sidePanel": {"type": "string", "description": "执行时侧边栏显示策略。hide | show，为空表示继承流程配置"}, "snapshot": {"type": "string", "enum": ["Node", "Not", "OnFail"]}, "status": {"type": "string", "enum": ["Cancelled", "CreateFailed", "Ended", "Ended_All_Failed", "Ended_Partial_Failed", "Ignored", "NotStart", "Running", "ScheduleCancelled", "Scheduled", "Scheduling", "UnusualEnded"]}, "successItems": {"type": "integer", "format": "int32"}, "teamId": {"type": "integer", "format": "int64"}, "totalItems": {"type": "integer", "format": "int32"}, "triggerType": {"type": "string", "description": "触发类型", "enum": ["Email", "File", "Http", "Loop", "Message", "Schedule"]}}}, "SessionFingerprintVo": {"title": "SessionFingerprintVo", "type": "object", "properties": {"devicePixelRatio": {"type": "number", "description": "指定 window.devicePixelRatio", "format": "double"}, "fingerprintTemplateId": {"type": "integer", "description": "指纹模版ID", "format": "int64"}, "screenResolution": {"type": "string", "description": "指纹中的分辨率"}, "userAgent": {"type": "string", "description": "指纹中的userAgent"}}}, "SessionIpVo": {"title": "SessionIpVo", "type": "object", "properties": {"host": {"type": "string", "description": "代理地址（必填）"}, "password": {"type": "string", "description": "密码（可选）"}, "port": {"type": "integer", "description": "端口（必填）", "format": "int32"}, "sshKey": {"type": "string", "description": "ssh登录私钥（可选，当type=ssh时）"}, "type": {"type": "string", "description": "代理协议（必填）", "enum": ["http", "socks5", "ssh"]}, "username": {"type": "string", "description": "用户名（可选）"}}}, "SessionPolicyVo": {"title": "SessionPolicyVo", "type": "object", "properties": {"imageForbiddenSize": {"type": "integer", "description": "超过指定大小的图片不加载（单位：KB）", "format": "int64"}, "loadImage": {"type": "boolean", "description": "是否加载图片", "example": false}, "loadVideo": {"type": "boolean", "description": "是否加载视频", "example": false}}}, "SessionVo": {"title": "SessionVo", "type": "object", "properties": {"bucketId": {"type": "integer", "format": "int64"}, "clientIp": {"type": "string"}, "closeTime": {"type": "string", "format": "date-time"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "deviceId": {"type": "string"}, "downTraffic": {"type": "integer", "format": "int64"}, "ghost": {"type": "boolean"}, "hbTimeout": {"type": "integer", "format": "int32"}, "heartbeatTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "ipId": {"type": "integer", "format": "int64"}, "openapiTaskId": {"type": "integer", "format": "int64"}, "proxyId": {"type": "integer", "format": "int64"}, "recordLocked": {"type": "boolean"}, "recordStatus": {"type": "string", "enum": ["Deleted", "Undefined", "<PERSON><PERSON>"]}, "remarks": {"type": "string"}, "remoteDebugPort": {"type": "integer", "format": "int32"}, "remoteProxyPort": {"type": "integer", "format": "int32"}, "remoteProxyType": {"type": "string"}, "rpaFlowId": {"type": "integer", "format": "int64"}, "rpaTaskId": {"type": "integer", "format": "int64"}, "rpaTaskItemId": {"type": "integer", "format": "int64"}, "shopId": {"type": "integer", "format": "int64"}, "shortcutId": {"type": "integer", "format": "int64"}, "spyScr": {"type": "boolean"}, "status": {"type": "string", "enum": ["CLOSE", "CREATING", "READY"]}, "teamId": {"type": "integer", "format": "int64"}, "trafficDeducted": {"type": "boolean"}, "transitId": {"type": "integer", "format": "int64"}, "upTraffic": {"type": "integer", "format": "int64"}, "version": {"type": "integer", "format": "int32"}}}, "ShopCookieVo": {"title": "ShopCookieVo", "type": "object", "properties": {"domain": {"type": "string"}, "expires": {"type": "number", "format": "double"}, "httpOnly": {"type": "boolean"}, "name": {"type": "string"}, "path": {"type": "string"}, "priority": {"type": "string"}, "sameParty": {"type": "boolean"}, "sameSite": {"type": "string"}, "secure": {"type": "boolean"}, "updateTime": {"type": "string", "format": "date-time"}, "value": {"type": "string"}}}, "ShopLanProxyVo": {"title": "ShopLanProxyVo", "type": "object", "properties": {"enabled": {"type": "boolean"}, "host": {"type": "string"}, "latitude": {"type": "number", "format": "double"}, "locale": {"type": "string"}, "locationId": {"type": "integer", "format": "int64"}, "longitude": {"type": "number", "format": "double"}, "networkType": {"type": "string", "enum": ["UseDirect", "UseProxy", "UseSystem"]}, "password": {"type": "string"}, "port": {"type": "integer", "format": "int32"}, "probeOnSession": {"type": "boolean"}, "proxyType": {"type": "string"}, "remoteIp": {"type": "string"}, "sshKey": {"type": "string"}, "timezone": {"type": "string"}, "updateTime": {"type": "string", "format": "date-time"}, "username": {"type": "string"}}}, "ShopPasswordsDto": {"title": "ShopPasswordsDto", "type": "object", "properties": {"actionUrl": {"type": "string"}, "blacklistedByUser": {"type": "integer", "format": "int32"}, "dateCreated": {"type": "integer", "format": "int32"}, "id": {"type": "integer", "format": "int64"}, "originUrl": {"type": "string"}, "passwordElement": {"type": "string"}, "passwordType": {"type": "integer", "format": "int32"}, "passwordValue": {"type": "string"}, "platformId": {"type": "integer", "format": "int64"}, "scheme": {"type": "integer", "format": "int32"}, "shopId": {"type": "integer", "format": "int64"}, "signonRealm": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}, "updateTime": {"type": "string", "format": "date-time"}, "usernameElement": {"type": "string"}, "usernameValue": {"type": "string"}}}, "ShopPlatformDto": {"title": "ShopPlatformDto", "type": "object", "properties": {"area": {"type": "string", "enum": ["Argentina", "Australia", "Austria", "Belarus", "Belgium", "Bolivia", "Brazil", "Canada", "Chile", "China", "Colombia", "Costa_Rica", "Dominican", "Ecuador", "Egypt", "France", "Germany", "Global", "Guatemala", "Honduras", "HongKong", "India", "Indonesia", "Ireland", "Israel", "Italy", "Japan", "Kazakhstan", "Korea", "Malaysia", "Mexico", "Netherlands", "Nicaragua", "Panama", "Paraguay", "Peru", "Philippines", "Poland", "Portuguese", "Puerto_Rico", "Russia", "Salvador", "Saudi_Arabia", "Singapore", "Spain", "Sweden", "Switzerland", "Taiwan", "Thailand", "Turkey", "United_Arab_Emirates", "United_Kingdom", "United_States", "Uruguay", "Venezuela", "Vietnam"]}, "frontUrl": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "loginUrl": {"type": "string"}, "name": {"type": "string"}}}, "ShopSessionDto": {"title": "ShopSessionDto", "type": "object", "properties": {"bucketId": {"type": "integer", "format": "int64"}, "clientIp": {"type": "string"}, "closeTime": {"type": "string", "format": "date-time"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "deviceId": {"type": "string"}, "downTraffic": {"type": "integer", "format": "int64"}, "ghost": {"type": "boolean"}, "hbTimeout": {"type": "integer", "format": "int32"}, "heartbeatTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "ipId": {"type": "integer", "format": "int64"}, "openapiTaskId": {"type": "integer", "format": "int64"}, "proxyId": {"type": "integer", "format": "int64"}, "recordLocked": {"type": "boolean"}, "recordStatus": {"type": "string", "enum": ["Deleted", "Undefined", "<PERSON><PERSON>"]}, "remarks": {"type": "string"}, "remoteDebugPort": {"type": "integer", "format": "int32"}, "remoteProxyPort": {"type": "integer", "format": "int32"}, "remoteProxyType": {"type": "string"}, "rpaFlowId": {"type": "integer", "format": "int64"}, "rpaTaskId": {"type": "integer", "format": "int64"}, "rpaTaskItemId": {"type": "integer", "format": "int64"}, "shopId": {"type": "integer", "format": "int64"}, "shortcutId": {"type": "integer", "format": "int64"}, "spyScr": {"type": "boolean"}, "status": {"type": "string", "enum": ["CLOSE", "CREATING", "READY"]}, "teamId": {"type": "integer", "format": "int64"}, "trafficDeducted": {"type": "boolean"}, "transitId": {"type": "integer", "format": "int64"}, "upTraffic": {"type": "integer", "format": "int64"}, "version": {"type": "integer", "format": "int32"}}}, "ShopShortcutDto": {"title": "ShopShortcutDto", "type": "object", "properties": {"createSource": {"type": "string", "description": "创建来源", "enum": ["Desktop", "Share"]}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time"}, "creatorId": {"type": "integer", "description": "创建者ID", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string", "description": "名称"}, "remark": {"type": "string", "description": "备注"}, "rpaEnabled": {"type": "boolean", "description": "是否允许执行RPA", "example": false}, "shopId": {"type": "integer", "description": "账号ID（分身ID）", "format": "int64"}, "teamId": {"type": "integer", "description": "团队ID", "format": "int64"}, "token": {"type": "string", "description": "唯一标识token"}, "userId": {"type": "integer", "description": "关联影子用户ID", "format": "int64"}, "valid": {"type": "boolean", "description": "当前释放有效", "example": false}, "validDays": {"type": "integer", "description": "有效天数，0表示永久有效", "format": "int32"}}}, "TagDto": {"title": "TagDto", "type": "object", "properties": {"bizCode": {"type": "string"}, "color": {"type": "integer", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}, "expireTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "resourceType": {"type": "string", "enum": ["AK", "Activity", "Audit", "BlockElements", "Cloud", "CrsOrder", "CrsProduct", "DiskFile", "FingerPrint", "FingerPrintTemplate", "Gateway", "GhCreator", "GhGifter", "GhJobPlan", "GhUser", "GhVideoCreator", "GiftCardPack", "InsTeamUser", "InsUser", "Invoice", "Ip", "IpPool", "IppIp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "KakaoFriend", "KolCreator", "MobileAccount", "None", "Orders", "PluginTeamPack", "Record", "RpaFlow", "RpaTask", "RpaTaskItem", "RpaVoucher", "Shop", "ShopSession", "Tag", "TeamDiskRoot", "TeamMobile", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TkCreator", "TkTeamPack", "Tkshop", "<PERSON>ks<PERSON><PERSON>uyer", "TkshopCreator", "TrafficPack", "TunnelVps", "Users", "View", "Voucher", "XhsAccount"]}, "system": {"type": "boolean"}, "tag": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}}}, "UpdateAccountPasswordRequest": {"title": "UpdateAccountPasswordRequest", "type": "object", "properties": {"passwordItems": {"type": "array", "items": {"$ref": "#/components/schemas/AccountPasswordItemVo"}}}}, "UserVo": {"title": "UserVo", "type": "object", "properties": {"account": {"type": "string", "description": "账号"}, "avatar": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "email": {"type": "string", "description": "邮箱"}, "gender": {"type": "string", "enum": ["FEMALE", "MALE", "UNSPECIFIC"]}, "id": {"type": "integer", "format": "int64"}, "nickname": {"type": "string"}, "partnerId": {"type": "integer", "format": "int64"}, "phone": {"type": "string", "description": "手机"}, "residentCity": {"type": "string"}, "signature": {"type": "string"}, "status": {"type": "string", "enum": ["ACTIVE", "BLOCK", "DELETED", "INACTIVE"]}, "tenant": {"type": "integer", "format": "int64"}, "userType": {"type": "string", "enum": ["NORMAL", "PARTNER", "SHADOW"]}, "weixin": {"type": "string"}}}, "WebResult": {"title": "WebResult", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«AccountVo»": {"title": "WebResult«AccountVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/AccountVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«AkAccessToken»": {"title": "WebResult«AkAccessToken»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/AkAccessToken"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«DeviceVo»": {"title": "WebResult«DeviceVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/DeviceVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«IpVo»": {"title": "WebResult«IpVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/IpVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«AccountVo»»": {"title": "WebResult«List«AccountVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/AccountVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«DeviceVo»»": {"title": "WebResult«List«DeviceVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/DeviceVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«JDEIpAllocationVo»»": {"title": "WebResult«List«JDEIpAllocationVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/JDEIpAllocationVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«OpenapiTaskModel»»": {"title": "WebResult«List«OpenapiTaskModel»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/OpenapiTaskModel"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«RpaFlowVo»»": {"title": "WebResult«List«RpaFlowVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/RpaFlowVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«SessionVo»»": {"title": "WebResult«List«SessionVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/SessionVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«ShopCookieVo»»": {"title": "WebResult«List«ShopCookieVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ShopCookieVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«ShopPasswordsDto»»": {"title": "WebResult«List«ShopPasswordsDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ShopPasswordsDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«ShopPlatformDto»»": {"title": "WebResult«List«ShopPlatformDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ShopPlatformDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«ShopShortcutDto»»": {"title": "WebResult«List«ShopShortcutDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ShopShortcutDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«string»»": {"title": "WebResult«List«string»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"type": "string"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«OpenapiTaskDto»": {"title": "WebResult«OpenapiTaskDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/OpenapiTaskDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«AccountVo»»": {"title": "WebResult«PageResult«AccountVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«AccountVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«ExtensionsVo»»": {"title": "WebResult«PageResult«ExtensionsVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«ExtensionsVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«IpVo»»": {"title": "WebResult«PageResult«IpVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«IpVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«RpaFlowVo»»": {"title": "WebResult«PageResult«RpaFlowVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«RpaFlowVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«RpaFlowShareCodeResult»": {"title": "WebResult«RpaFlowShareCodeResult»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/RpaFlowShareCodeResult"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«RpaFlowVo»": {"title": "WebResult«RpaFlowVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/RpaFlowVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«RpaTaskVo»": {"title": "WebResult«RpaTaskVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/RpaTaskVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«ShopSessionDto»": {"title": "WebResult«ShopSessionDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/ShopSessionDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«ShopShortcutDto»": {"title": "WebResult«ShopShortcutDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/ShopShortcutDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TagDto»": {"title": "WebResult«TagDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TagDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«UserVo»": {"title": "WebResult«UserVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/UserVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«string»": {"title": "WebResult«string»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "string"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}}, "securitySchemes": {"Authorization": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}, "x-dm-user-id": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-dm-user-id", "in": "header"}, "x-dm-team-id": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-dm-team-id", "in": "header"}}}}
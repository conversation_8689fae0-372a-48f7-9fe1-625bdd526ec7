{"openapi": "3.0.3", "info": {"title": "Payment API", "version": "App version 1.0,Spring Boot Version:2.7.14"}, "servers": [{"url": "http://dev.thinkoncloud.cn", "description": "Inferred Url"}], "tags": [{"name": "Alipay Notify API", "description": "Alipay Controller"}, {"name": "Payment Webhook API", "description": "Webhook Payment Controller"}, {"name": "WechatPay Notify API", "description": "Wechat Pay Controller"}, {"name": "礼品卡相关API", "description": "Gift Card Controller"}, {"name": "费用中心:发票相关API", "description": "Invoice Controller"}, {"name": "费用中心:支付|购买相关API", "description": "Payment Controller"}, {"name": "费用中心远程调用", "description": "Payment Remote Service Impl"}], "paths": {"/api/payment/alipay/notify": {"post": {"tags": ["AlipayController"], "summary": "alipayNotify", "operationId": "paymentAlipayNotifyPost", "parameters": [{"name": "out_trade_no", "in": "query", "description": "out_trade_no", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "trade_no", "in": "query", "description": "trade_no", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "trade_status", "in": "query", "description": "trade_status", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "total_amount", "in": "query", "description": "total_amount", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "refund_fee", "in": "query", "description": "refund_fee", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "string"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/alipay/notify/{teamId}": {"post": {"tags": ["AlipayController"], "summary": "alipayNotify", "operationId": "paymentAlipayNotifyByTeamIdPost", "parameters": [{"name": "teamId", "in": "path", "description": "teamId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "out_trade_no", "in": "query", "description": "out_trade_no", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "trade_no", "in": "query", "description": "trade_no", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "trade_status", "in": "query", "description": "trade_status", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "total_amount", "in": "query", "description": "total_amount", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "refund_fee", "in": "query", "description": "refund_fee", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "string"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/alipay/return": {"post": {"tags": ["AlipayController"], "summary": "alipayReturn", "operationId": "paymentAlipayReturnPost", "parameters": [{"name": "out_trade_no", "in": "query", "description": "out_trade_no", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/alipay/return/{teamId}": {"post": {"tags": ["AlipayController"], "summary": "alipayReturn", "operationId": "paymentAlipayReturnByTeamIdPost", "parameters": [{"name": "teamId", "in": "path", "description": "teamId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "out_trade_no", "in": "query", "description": "out_trade_no", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/gift-card/activeAGiftCard": {"post": {"tags": ["GiftCardController"], "summary": "激活一张礼品卡", "operationId": "paymentGiftCardActiveAGiftCardPost", "parameters": [{"name": "receivingTeamId", "in": "query", "description": "receivingTeamId", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "cardNumber", "in": "query", "description": "cardNumber", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "cardPassword", "in": "query", "description": "cardPassword", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "activeNow", "in": "query", "description": "是否立即激活，如果 false 说明只想检查一下密码对不对，并取回相应信息", "required": false, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«GiftCardPackItemVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/cancelInvoice/{invoiceId}": {"put": {"tags": ["InvoiceController"], "summary": "取消一个发票申请", "operationId": "paymentCancelInvoiceByInvoiceIdPut", "parameters": [{"name": "invoiceId", "in": "path", "description": "invoiceId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/configVoiceAddress": {"post": {"tags": ["InvoiceController"], "summary": "修改发票邮寄地址", "operationId": "paymentConfigVoiceAddressPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConfigVoiceInfoRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/configVoiceInfo": {"post": {"tags": ["InvoiceController"], "summary": "修改开票信息", "operationId": "paymentConfigVoiceInfoPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConfigVoiceInfoRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/invoice": {"put": {"tags": ["InvoiceController"], "summary": "开发票", "operationId": "paymentInvoicePut", "parameters": [{"name": "orderIds", "in": "query", "description": "orderIds", "required": true, "style": "form", "explode": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "electronic", "in": "query", "description": "electronic", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "address", "in": "query", "description": "address", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "userDesc", "in": "query", "description": "userDesc", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/invoiceHistory": {"get": {"tags": ["InvoiceController"], "summary": "获取团队发票历史记录的接口", "operationId": "paymentInvoiceHistoryGet", "parameters": [{"name": "pageNum", "in": "query", "description": "pageNum", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«InvoiceHistoryVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/invoiceSummary": {"get": {"tags": ["InvoiceController"], "summary": "获取团队发票概览信息", "operationId": "paymentInvoiceSummaryGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«InvoiceSummaryVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/notInvoicedOrders": {"get": {"tags": ["InvoiceController"], "summary": "获取可以开发票的订单列表，该接口不分页", "operationId": "paymentNotInvoicedOrdersGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«NotInvoicedOrderVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/activeAVoucher": {"post": {"tags": ["PaymentController"], "summary": "激活一张代金券", "operationId": "paymentActiveAVoucherPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActiveVoucherRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«VoucherVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/balance": {"get": {"tags": ["PaymentController"], "summary": "获取团队余额", "operationId": "paymentBalanceGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«bigdecimal»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/balanceInOutList": {"get": {"tags": ["PaymentController"], "summary": "查询团队收支明细列表", "operationId": "paymentBalanceInOutListGet", "parameters": [{"name": "pageNum", "in": "query", "description": "pageNum", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "orderType", "in": "query", "description": "orderType", "required": false, "style": "form", "schema": {"type": "string", "enum": ["BuyGiftCard", "BuyIp", "BuyPluginPack", "BuyRpaVoucher", "BuyTkPack", "BuyTkshop", "BuyTraffic", "CashOut", "MakeupPriceDifference", "<PERSON><PERSON>", "OrderCancel", "PartnerBuyVoucher", "PartnerDraw", "PartnerRecharge", "PartnerRechargeCredit", "PartnerRenewRpaVoucher", "Present", "Recharge", "RechargeCredit", "Refund", "RenewIp", "RenewPluginPack", "RenewRpaVoucher", "RenewTkPack", "RenewTkshop", "UpgradeTkshop"]}}, {"name": "payType", "in": "query", "description": "payType", "required": false, "style": "form", "schema": {"type": "string", "enum": ["AliPay", "BalancePay", "BankPay", "WechatPay"]}}, {"name": "from", "in": "query", "description": "from", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "to", "in": "query", "description": "to", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«BalanceInOutDetailVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/calcBuyCreditPrice": {"get": {"tags": ["PaymentController"], "summary": "计算购买花瓣价格", "operationId": "paymentCalcBuyCreditPriceGet", "parameters": [{"name": "count", "in": "query", "description": "购买多少个", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "distributionCode", "in": "query", "description": "distributionCode", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«CalcPriceResponse»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/calcBuyIpPrice": {"get": {"tags": ["PaymentController"], "summary": "计算购买ip价格", "operationId": "paymentCalcBuyIpPriceGet", "parameters": [{"name": "goodsIds", "in": "query", "description": "ip方案对应的商品id", "required": true, "style": "form", "explode": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "counts", "in": "query", "description": "每个goods购买多少个，必须与goodsIds顺序一一对应", "required": true, "style": "form", "explode": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "duration", "in": "query", "description": "购买多少个周期", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "periodUnit", "in": "query", "description": "续费周期单位", "required": true, "style": "form", "schema": {"type": "string", "enum": ["Buyout", "Byte", "GB", "GB天", "个", "个天", "分钟", "周", "天", "年", "张", "无", "月", "次"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«CalcPriceResponse»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/cancel/order/{orderId}": {"put": {"tags": ["PaymentController"], "summary": "取消一个未支付的订单", "operationId": "paymentCancelOrderByOrderIdPut", "parameters": [{"name": "orderId", "in": "path", "description": "orderId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "reason", "in": "query", "description": "reason", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/cancelOrders": {"put": {"tags": ["PaymentController"], "summary": "批量取消未支付的订单", "operationId": "paymentCancelOrdersPut", "parameters": [{"name": "orderIds", "in": "path", "description": "orderIds", "required": true, "style": "simple", "schema": {"type": "string"}}, {"name": "reason", "in": "query", "description": "reason", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/cloudIpDiscounts": {"get": {"tags": ["PaymentController"], "summary": "获取公有云IP通用折扣信息", "operationId": "paymentCloudIpDiscountsGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«DiscountsDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/costSummary": {"get": {"tags": ["PaymentController"], "summary": "获取团队账户某段时间支出情况", "operationId": "paymentCostSummaryGet", "parameters": [{"name": "from", "in": "query", "description": "开始日期", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "to", "in": "query", "description": "结束日期", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«CostSummaryVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/countVouchers": {"get": {"tags": ["PaymentController"], "summary": "获取团队的各种代金券数量", "operationId": "paymentCountVouchersGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«Map«string,int»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/createBuyCreditOrder": {"post": {"tags": ["PaymentController"], "summary": "创建购买花瓣订单", "operationId": "paymentCreateBuyCreditOrderPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateBuyCreditOrderRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«CreateOrderResponse»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/createRechargeOrder": {"post": {"tags": ["PaymentController"], "summary": "创建充值订单", "operationId": "paymentCreateRechargeOrderPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateRechargeOrderRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«CreateOrderResponse»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/credit": {"get": {"tags": ["PaymentController"], "summary": "获取团队花瓣", "operationId": "paymentCreditGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«bigdecimal»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/creditExcel": {"get": {"tags": ["PaymentController"], "summary": "下载花瓣收支明细", "operationId": "paymentCreditExcelGet", "parameters": [{"name": "creditTypes", "in": "query", "description": "creditTypes", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "from", "in": "query", "description": "from", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "to", "in": "query", "description": "to", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Resource"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/creditExcel/{token}": {"get": {"tags": ["PaymentController"], "summary": "下载花瓣收支明细", "operationId": "paymentCreditExcelByTokenGet", "parameters": [{"name": "token", "in": "path", "description": "token", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Resource"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/creditExcelToken": {"get": {"tags": ["PaymentController"], "summary": "下载花瓣收支明细", "operationId": "paymentCreditExcelTokenGet", "parameters": [{"name": "creditTypes", "in": "query", "description": "creditTypes", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "from", "in": "query", "description": "from", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "to", "in": "query", "description": "to", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/creditInOutList": {"get": {"tags": ["PaymentController"], "summary": "查询团队花瓣明细列表", "operationId": "paymentCreditInOutListGet", "parameters": [{"name": "pageNum", "in": "query", "description": "pageNum", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "creditType", "in": "query", "description": "creditType", "required": false, "style": "form", "schema": {"type": "string", "enum": ["BuyCredit", "ConsumeCredit", "FingerprintQuota", "GiftCard", "InitPresent", "IosDeveloperApprove", "IpOver<PERSON><PERSON><PERSON><PERSON>raffic", "OrderRefund", "Present", "RpaCaptcha", "RpaExecuteQuota", "RpaMarketFlow", "RpaOpenAi", "RpaSendEmail", "RpaSendSms", "RpaSendWeChat", "ShopQuota", "ShopSecurityPolicy", "StorageQuota", "TeamMemberQuota", "Team<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TransferIn", "TransferOut", "TransitTraffic"]}}, {"name": "minCredit", "in": "query", "description": "minCredit", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "serialNumber", "in": "query", "description": "serialNumber", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "from", "in": "query", "description": "from", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "to", "in": "query", "description": "to", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«CreditInOutDetailVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/discounts": {"get": {"tags": ["PaymentController"], "summary": "获取一些商品可用折扣情况", "operationId": "paymentDiscountsGet", "parameters": [{"name": "goodsIds", "in": "query", "description": "goodsIds", "required": true, "style": "form", "explode": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«Map«long,List«DiscountsDto»»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/findOrderResources": {"get": {"tags": ["PaymentController"], "summary": "查询订单商品明细", "operationId": "paymentFindOrderResourcesGet", "parameters": [{"name": "orderIds", "in": "query", "description": "orderIds", "required": true, "style": "form", "explode": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«Map«long,List«OrderResourceVo»»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/getCreditGoods": {"get": {"tags": ["PaymentController"], "summary": "获取花瓣这一商品信息，主要是因为后边售卖都需要goodsId，通过该方法来获取花瓣goodsId", "operationId": "paymentGetCreditGoodsGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«GoodsDto»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/getRpaVoucherGoodsInfo": {"get": {"tags": ["PaymentController"], "summary": "获取rpa包时券商品信息，主要是因为后边售卖都需要goodsId，通过该方法来获取goodsId", "operationId": "paymentGetRpaVoucherGoodsInfoGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«GoodsDto»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/lockAndPayOrder": {"post": {"tags": ["PaymentController"], "summary": "锁定并支付一个订单（查看未支付订单详情界面点立即支付）", "operationId": "paymentLockAndPayOrderPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LockAndPayOrderRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«CreateOrderResponse»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/mergePayOrders": {"post": {"tags": ["PaymentController"], "summary": "合并支付订单。已经锁定的订单不允许参与合并支付。合并支付点支付后会将所有的子订单锁定", "operationId": "paymentMergePayOrdersPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MergePayOrdersRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«CreateOrderResponse»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/order/{orderId}/markBankTransferDone": {"post": {"tags": ["PaymentController"], "summary": "客户银行转账后确认提交", "operationId": "paymentOrderByOrderIdMarkBankTransferDonePost", "parameters": [{"name": "orderId", "in": "path", "description": "orderId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«CreateOrderResponse»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/order/{orderId}/paidInfoForPay": {"get": {"tags": ["PaymentController"], "summary": "用在订单列表界面点击未支付订单的【支付】按钮，重新获取支付方式", "operationId": "paymentOrderByOrderIdPaidInfoForPayGet", "parameters": [{"name": "orderId", "in": "path", "description": "orderId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«CreateOrderResponse»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/orders": {"get": {"tags": ["PaymentController"], "summary": "查询团队订单列表", "operationId": "paymentOrdersGet", "parameters": [{"name": "pageNum", "in": "query", "description": "pageNum", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "id", "in": "query", "description": "id", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "parentOrderId", "in": "query", "description": "parentOrderId", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "serialNumber", "in": "query", "description": "serialNumber", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "orderType", "in": "query", "description": "orderType", "required": false, "style": "form", "schema": {"type": "string", "enum": ["BuyGiftCard", "BuyIp", "BuyPluginPack", "BuyRpaVoucher", "BuyTkPack", "BuyTkshop", "BuyTraffic", "CashOut", "MakeupPriceDifference", "<PERSON><PERSON>", "OrderCancel", "PartnerBuyVoucher", "PartnerDraw", "PartnerRecharge", "PartnerRechargeCredit", "PartnerRenewRpaVoucher", "Present", "Recharge", "RechargeCredit", "Refund", "RenewIp", "RenewPluginPack", "RenewRpaVoucher", "RenewTkPack", "RenewTkshop", "UpgradeTkshop"]}}, {"name": "orderTypes", "in": "query", "description": "orderTypes", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "payStatus", "in": "query", "description": "payStatus", "required": false, "style": "form", "schema": {"type": "string", "enum": ["CANCELED", "Created", "Locked", "PAID", "PartialRefund", "REFUNDED", "WAIT_CONFIRM"]}}, {"name": "from", "in": "query", "description": "from", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "to", "in": "query", "description": "to", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«OrderDetailVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/orders/{orderId}/detail": {"get": {"tags": ["PaymentController"], "summary": "查询团队某个订单详情", "operationId": "paymentOrdersByOrderIdDetailGet", "parameters": [{"name": "orderId", "in": "path", "description": "orderId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«OrderDetailVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/orders/{orderId}/recalculateDraw": {"get": {"tags": ["PaymentController"], "summary": "重新计算订单的提成并返回订单数据（不存盘）", "operationId": "paymentOrdersByOrderIdRecalculateDrawGet", "parameters": [{"name": "orderId", "in": "path", "description": "orderId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«OrdersDto»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/summary": {"get": {"tags": ["PaymentController"], "summary": "获取团队账户总览", "operationId": "paymentSummaryGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PaymentSummaryVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/updateOrderPayment": {"post": {"tags": ["PaymentController"], "summary": "更新一个订单支付信息（查看未支付订单详情界面继续点稍候支付）", "operationId": "paymentUpdateOrderPaymentPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateOrderPaymentRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«CreateOrderResponse»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/voucher/card_number/{cardNumber}": {"get": {"tags": ["PaymentController"], "summary": "根据cardNumber获取代金券详情", "operationId": "paymentVoucherCard_numberByCardNumberGet", "parameters": [{"name": "cardNumber", "in": "path", "description": "cardNumber", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«VoucherVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/vouchers": {"get": {"tags": ["PaymentController"], "summary": "获取团队的代金券", "operationId": "paymentVouchersGet", "parameters": [{"name": "pageNum", "in": "query", "description": "pageNum", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "voucherStatus", "in": "query", "description": "voucherStatus", "required": true, "style": "form", "schema": {"type": "string", "enum": ["Available", "ExpireSoon", "Invalid", "Used"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«VoucherVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "delete": {"tags": ["PaymentController"], "summary": "删除代金券", "operationId": "paymentVouchersDelete", "parameters": [{"name": "voucherIds", "in": "query", "description": "voucherIds", "required": true, "style": "form", "explode": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«int»"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/payment/checkProduced": {"post": {"tags": ["PaymentRemoteServiceImpl"], "summary": "重新生产某个未生产成功订单", "operationId": "remotePaymentCheckProducedPost", "parameters": [{"name": "orderId", "in": "query", "description": "orderId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/payment/checkRefundAble": {"get": {"tags": ["PaymentRemoteServiceImpl"], "summary": "检查某个订单是否可安全退款，如果不可安全退款则会返回非空值", "operationId": "remotePaymentCheckRefundAbleGet", "parameters": [{"name": "orderId", "in": "query", "description": "orderId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "string"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/payment/notifyBankPayFailed": {"post": {"tags": ["PaymentRemoteServiceImpl"], "summary": "通知银行支付订单打款失败", "operationId": "remotePaymentNotifyBankPayFailedPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotifyBankPayFailedRequest"}}}}, "responses": {"200": {"description": "OK"}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/payment/notifyBankPaySuccess": {"post": {"tags": ["PaymentRemoteServiceImpl"], "summary": "通知银行支付订单打款成功", "operationId": "remotePaymentNotifyBankPaySuccessPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotifyBankPaySuccessRequest"}}}}, "responses": {"200": {"description": "OK"}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/payment/notifyOfflineOrderCreated": {"post": {"tags": ["PaymentRemoteServiceImpl"], "summary": "通知一个offline订单创建了。主要用来给团队用户发订单待支付消息", "operationId": "remotePaymentNotifyOfflineOrderCreatedPost", "parameters": [{"name": "orderId", "in": "query", "description": "orderId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/payment/reProduceOrder": {"post": {"tags": ["PaymentRemoteServiceImpl"], "summary": "重新生产某个未生产成功订单", "operationId": "remotePaymentReProduceOrderPost", "parameters": [{"name": "orderId", "in": "query", "description": "orderId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/payment/refundOrder": {"post": {"tags": ["PaymentRemoteServiceImpl"], "summary": "订单退款", "operationId": "remotePaymentRefundOrderPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefundRequest"}}}}, "responses": {"200": {"description": "OK"}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/webhook/payment/calcBuyIpPrice": {"get": {"tags": ["WebhookPaymentController"], "summary": "计算购买ip价格", "operationId": "webhookPaymentCalcBuyIpPriceGet", "parameters": [{"name": "goodsIds", "in": "query", "description": "ip方案对应的商品id", "required": true, "style": "form", "explode": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "counts", "in": "query", "description": "每个goods购买多少个，必须与goodsIds顺序一一对应", "required": true, "style": "form", "explode": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "duration", "in": "query", "description": "购买多少个周期", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "periodUnit", "in": "query", "description": "续费周期单位", "required": true, "style": "form", "schema": {"type": "string", "enum": ["Buyout", "Byte", "GB", "GB天", "个", "个天", "分钟", "周", "天", "年", "张", "无", "月", "次"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«CalcPriceResponse»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/webhook/payment/cloudIpDiscounts": {"get": {"tags": ["WebhookPaymentController"], "summary": "获取公有云IP通用折扣信息", "operationId": "webhookPaymentCloudIpDiscountsGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«DiscountsDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/wechatpay/notify": {"post": {"tags": ["WechatPayController"], "summary": "wechatpay_notify", "operationId": "paymentWechatpayNotifyPost", "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "string"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/wechatpay/notify/{teamId}": {"post": {"tags": ["WechatPayController"], "summary": "wechatpay_notify", "operationId": "paymentWechatpayNotifyByTeamIdPost", "parameters": [{"name": "teamId", "in": "path", "description": "teamId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "string"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/payment/wechatpay/qrcode": {"get": {"tags": ["WechatPayController"], "summary": "qrcode", "operationId": "paymentWechatpayQrcodeGet", "parameters": [{"name": "code_url", "in": "query", "description": "code_url", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}}, "components": {"schemas": {"ActiveVoucherRequest": {"title": "ActiveVoucherRequest", "type": "object", "properties": {"activeNow": {"type": "boolean"}, "cardNumber": {"type": "string"}, "cardPassword": {"type": "string"}}}, "BalanceInOutDetailVo": {"title": "BalanceInOutDetailVo", "type": "object", "properties": {"balance": {"type": "number", "description": "交易后的账户余额", "format": "bigdecimal"}, "balanceAmount": {"type": "number", "description": "余额抵扣金额", "format": "bigdecimal"}, "createTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "orderId": {"type": "integer", "description": "关联的订单id", "format": "int64"}, "orderSerialNumber": {"type": "string"}, "orderSource": {"type": "string", "description": "订单来源/创建方式", "enum": ["Automatic", "Offline", "Users"]}, "orderType": {"type": "string", "enum": ["BuyGiftCard", "BuyIp", "BuyPluginPack", "BuyRpaVoucher", "BuyTkPack", "BuyTkshop", "BuyTraffic", "CashOut", "MakeupPriceDifference", "<PERSON><PERSON>", "OrderCancel", "PartnerBuyVoucher", "PartnerDraw", "PartnerRecharge", "PartnerRechargeCredit", "PartnerRenewRpaVoucher", "Present", "Recharge", "RechargeCredit", "Refund", "RenewIp", "RenewPluginPack", "RenewRpaVoucher", "RenewTkPack", "RenewTkshop", "UpgradeTkshop"]}, "payType": {"type": "string", "enum": ["AliPay", "BalancePay", "BankPay", "WechatPay"]}, "payablePrice": {"type": "number", "description": "订单应付总额，比如说买一年打8折或者销售改价之后的价格", "format": "bigdecimal"}, "presentAmount": {"type": "number", "description": "充值赠送金额;只在充值订单有效", "format": "bigdecimal"}, "realPrice": {"type": "number", "description": "实付金额，即现金支付金额;可开票金额以此为依据", "format": "bigdecimal"}, "serialNumber": {"type": "string"}, "totalPrice": {"type": "number", "description": "订单总额", "format": "bigdecimal"}, "voucherAmount": {"type": "number", "description": "代金券抵扣金额", "format": "bigdecimal"}, "voucherCardNumber": {"type": "string", "description": "代金券卡号"}}}, "BankPayConfig": {"title": "BankPayConfig", "type": "object", "properties": {"accountName": {"type": "string"}, "accountNumber": {"type": "string"}, "bankName": {"type": "string"}}}, "CalcPriceResponse": {"title": "CalcPriceResponse", "type": "object", "properties": {"discount": {"type": "number", "description": "折扣,[0-1]", "format": "double"}, "discountAmount": {"type": "number", "description": "打折减掉的金额(如果是打折的话)", "format": "bigdecimal"}, "items": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/ItemPriceInfo"}, "description": "记录每个item的价格及折扣或赠送信息，<key=goodsId, value=PriceInfo>"}, "payablePrice": {"type": "number", "description": "订单应付价(减掉了打折等信息)", "format": "bigdecimal"}, "presentAmount": {"type": "number", "description": "赠送金额，目前只出现在购买花瓣", "format": "bigdecimal"}, "totalCost": {"type": "number", "description": "订单总成本", "format": "bigdecimal"}, "totalPrice": {"type": "number", "description": "订单总价(原价)", "format": "bigdecimal"}}}, "ConfigVoiceInfoRequest": {"title": "ConfigVoiceInfoRequest", "type": "object", "properties": {"account": {"type": "string"}, "address": {"type": "string"}, "bank": {"type": "string"}, "email": {"type": "string"}, "id": {"type": "integer", "description": "按原型图一个团队只允许有一条发票信息，所以ID字段暂时用不到", "format": "int64"}, "invoiceType": {"type": "string", "enum": ["EntNormal", "EntSpecial", "None", "Personal"]}, "number": {"type": "string", "description": "电话号码"}, "receiverAddress": {"type": "string"}, "receiverName": {"type": "string"}, "receiverPhone": {"type": "string"}, "taxNum": {"type": "string"}, "title": {"type": "string"}}, "description": "设置开票信息和邮寄地址共用，用不到的属性空着就行"}, "CostSummaryVo": {"title": "CostSummaryVo", "type": "object", "properties": {"days": {"type": "array", "description": "每日支出（只有当日有支出才会有数据）", "items": {"$ref": "#/components/schemas/DayCostVo"}}, "total": {"type": "number", "description": "当月总支出", "format": "bigdecimal"}, "typeCosts": {"type": "object", "additionalProperties": {"type": "number", "format": "bigdecimal"}, "description": "不同种类的支出"}}, "description": "一个月已支出费用概览"}, "CreateBuyCreditOrderRequest": {"title": "CreateBuyCreditOrderRequest", "type": "object", "properties": {"agreement": {"type": "boolean", "description": "已经勾选阅读和同意使用协议，没什么用", "example": false}, "balanceAmount": {"type": "number", "description": "余额抵扣金额", "format": "bigdecimal"}, "count": {"type": "integer", "description": "购买数量", "format": "int32"}, "distributionCode": {"type": "string", "description": "优惠码"}, "immediatePay": {"type": "boolean", "description": "是否立即支付（点稍候支付该属性传false）", "example": false}, "payType": {"type": "string", "enum": ["AliPay", "BalancePay", "BankPay", "WechatPay"]}, "voucherAmount": {"type": "number", "description": "代金券抵扣金额，不得大于代金券余额", "format": "bigdecimal"}, "voucherId": {"type": "integer", "description": "要使用的代金券id", "format": "int64"}}}, "CreateOrderResponse": {"title": "CreateOrderResponse", "type": "object", "properties": {"bankAccount": {"type": "string"}, "bankAccountName": {"type": "string"}, "bankName": {"type": "string"}, "bankRemark": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "deductedPrice": {"type": "number", "description": "扣减金额", "format": "bigdecimal"}, "orderId": {"type": "integer", "format": "int64"}, "payOutContent": {"type": "string", "description": "支付输出的内容"}, "payOutType": {"type": "string", "description": "支付输出的内容类型"}, "payStatus": {"type": "string", "description": "如果不需要现金支付，该订单状态会直接变成已支付", "enum": ["CANCELED", "Created", "Locked", "PAID", "PartialRefund", "REFUNDED", "WAIT_CONFIRM"]}, "payType": {"type": "string", "description": "支付方式", "enum": ["AliPay", "BalancePay", "BankPay", "WechatPay"]}, "realPrice": {"type": "number", "description": "需要现金支付的money", "format": "bigdecimal"}, "salesReduction": {"type": "number", "format": "bigdecimal"}, "serialNumber": {"type": "string"}}}, "CreateRechargeOrderRequest": {"title": "CreateRechargeOrderRequest", "type": "object", "properties": {"amount": {"type": "number", "description": "充值金额", "format": "bigdecimal"}, "payType": {"type": "string", "description": "支付方式", "enum": ["AliPay", "BalancePay", "BankPay", "WechatPay"]}}}, "CreditCostItemVo": {"title": "CreditCostItemVo", "type": "object", "properties": {"cost": {"type": "number", "format": "bigdecimal"}, "creditType": {"type": "string", "enum": ["BuyCredit", "ConsumeCredit", "FingerprintQuota", "GiftCard", "InitPresent", "IosDeveloperApprove", "IpOver<PERSON><PERSON><PERSON><PERSON>raffic", "OrderRefund", "Present", "RpaCaptcha", "RpaExecuteQuota", "RpaMarketFlow", "RpaOpenAi", "RpaSendEmail", "RpaSendSms", "RpaSendWeChat", "ShopQuota", "ShopSecurityPolicy", "StorageQuota", "TeamMemberQuota", "Team<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TransferIn", "TransferOut", "TransitTraffic"]}, "desc": {"type": "string"}}}, "CreditCostPackVo": {"title": "CreditCostPackVo", "type": "object", "properties": {"desc": {"type": "string"}, "itemMap": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/CreditCostItemVo"}}, "pack": {"type": "string", "enum": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Member", "Rpa", "Shop", "Storage"]}, "totalCost": {"type": "number", "format": "bigdecimal"}}}, "CreditInOutDetailVo": {"title": "CreditInOutDetailVo", "type": "object", "properties": {"amount": {"type": "integer", "format": "int64"}, "bizDesc": {"type": "string"}, "bizId": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "creatorName": {"type": "string"}, "creatorTeam": {"type": "integer", "format": "int64"}, "creatorTeamName": {"type": "string"}, "credit": {"type": "number", "format": "bigdecimal"}, "creditType": {"type": "string", "enum": ["BuyCredit", "ConsumeCredit", "FingerprintQuota", "GiftCard", "InitPresent", "IosDeveloperApprove", "IpOver<PERSON><PERSON><PERSON><PERSON>raffic", "OrderRefund", "Present", "RpaCaptcha", "RpaExecuteQuota", "RpaMarketFlow", "RpaOpenAi", "RpaSendEmail", "RpaSendSms", "RpaSendWeChat", "ShopQuota", "ShopSecurityPolicy", "StorageQuota", "TeamMemberQuota", "Team<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TransferIn", "TransferOut", "TransitTraffic"]}, "goodsType": {"type": "string", "enum": ["Credit", "CreditPack", "ExclusiveIp", "FingerprintQuota", "IosDeveloperApprove", "Ip", "IpGo", "IpProxy", "MarketFlow", "None", "PluginPack", "PriceDifference", "ProxyTraffic", "RpaCaptcha", "RpaExecuteQuota", "RpaMobile", "RpaOpenAi", "RpaSendEmail", "RpaSendSms", "RpaSendWeChat", "Rpa_Voucher_Base", "Rpa_Voucher_Performance", "SharingIp", "ShopQuota", "ShopSecurityPolicy", "StorageQuota", "TeamMemberQuota", "Team<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TkPack", "TkPackTrail", "Tkshop", "TkshopEnterprise", "TkshopStandard", "Traffic", "TransitTraffic", "TransitTrafficV2", "UserExclusiveIp", "Voucher"]}, "id": {"type": "integer", "format": "int64"}, "periodUnit": {"type": "string", "enum": ["Buyout", "Byte", "GB", "GB天", "个", "个天", "分钟", "周", "天", "年", "张", "无", "月", "次"]}, "price": {"type": "number", "format": "bigdecimal"}, "remainCredit": {"type": "number", "format": "bigdecimal"}, "serialNumber": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}}}, "DailyCreditCost": {"title": "DailyCreditCost", "type": "object", "properties": {"day": {"type": "string", "format": "date-time"}, "packs": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/CreditCostPackVo"}}, "totalCost": {"type": "number", "format": "bigdecimal"}}}, "DayCostOrderVo": {"title": "DayCostOrderVo", "type": "object", "properties": {"orderId": {"type": "integer", "format": "int64"}, "price": {"type": "number", "format": "bigdecimal"}, "serialNumber": {"type": "string"}}}, "DayCostVo": {"title": "DayCostVo", "type": "object", "properties": {"day": {"type": "string", "description": "天"}, "orders": {"type": "array", "description": "当天的订单记录", "items": {"$ref": "#/components/schemas/DayCostOrderVo"}}, "total": {"type": "number", "description": "当天总支出", "format": "bigdecimal"}}, "description": "某天已支出费用概览"}, "DiscountsDto": {"title": "DiscountsDto", "type": "object", "properties": {"amount": {"type": "integer", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}, "discountCode": {"type": "string"}, "discountRange": {"type": "string", "enum": ["ConsumeCredit", "Ip", "Recharge", "RechargeCredit", "RpaVoucher", "TkPack"]}, "discountType": {"type": "string", "enum": ["Discount", "LadderPrice", "Present"]}, "enabled": {"type": "boolean"}, "goodsId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "periodUnit": {"type": "string", "enum": ["Buyout", "Byte", "GB", "GB天", "个", "个天", "分钟", "周", "天", "年", "张", "无", "月", "次"]}, "remarks": {"type": "string"}, "threshold": {"type": "integer", "format": "int32"}}}, "DiscountsVo": {"title": "DiscountsVo", "type": "object", "properties": {"amount": {"type": "integer", "description": "赠送数量或折扣百分比或阶梯折扣百分比", "format": "int32"}, "discountCode": {"type": "string", "description": "打折code"}, "discountType": {"type": "string", "description": "打折还是赠送", "enum": ["Discount", "LadderPrice", "Present"]}, "periodUnit": {"type": "string", "description": "周期或数量单位", "enum": ["Buyout", "Byte", "GB", "GB天", "个", "个天", "分钟", "周", "天", "年", "张", "无", "月", "次"]}, "remarks": {"type": "string", "description": "备注"}, "threshold": {"type": "integer", "description": "期数或数量", "format": "int32"}}}, "GiftCardPackDetailVo": {"title": "GiftCardPackDetailVo", "type": "object", "properties": {"cardCount": {"type": "integer", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}, "giftCardType": {"type": "string", "enum": ["Credit", "RpaVoucher"]}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "packItems": {"type": "array", "items": {"$ref": "#/components/schemas/GiftCardPackItemDetailVo"}}, "partnerId": {"type": "integer", "format": "int64"}, "remainCount": {"type": "integer", "description": "剩余可用礼品卡数量", "format": "int32"}, "remarks": {"type": "string"}, "serialNumber": {"type": "string"}, "valid": {"type": "boolean", "description": "是否在有效期内（根据createTime & validDays计算出来的）", "example": false}, "validDays": {"type": "integer", "format": "int32"}}}, "GiftCardPackItemDetailVo": {"title": "GiftCardPackItemDetailVo", "type": "object", "properties": {"activatedTeamId": {"type": "integer", "format": "int64"}, "activatedTeamName": {"type": "string"}, "activeTime": {"type": "string", "format": "date-time"}, "amount": {"type": "number", "format": "bigdecimal"}, "cardNumber": {"type": "string"}, "cardPackId": {"type": "integer", "format": "int64"}, "cardPassword": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "disabled": {"type": "boolean"}, "duration": {"type": "integer", "description": "当为rpa礼品卡时，表示该卡有几个 periodUnit 月/周", "format": "int32"}, "expireDate": {"type": "string", "format": "date-time"}, "giftCardType": {"type": "string", "enum": ["Credit", "RpaVoucher"]}, "goodsId": {"type": "integer", "format": "int64"}, "goodsType": {"type": "string", "enum": ["Credit", "CreditPack", "ExclusiveIp", "FingerprintQuota", "IosDeveloperApprove", "Ip", "IpGo", "IpProxy", "MarketFlow", "None", "PluginPack", "PriceDifference", "ProxyTraffic", "RpaCaptcha", "RpaExecuteQuota", "RpaMobile", "RpaOpenAi", "RpaSendEmail", "RpaSendSms", "RpaSendWeChat", "Rpa_Voucher_Base", "Rpa_Voucher_Performance", "SharingIp", "ShopQuota", "ShopSecurityPolicy", "StorageQuota", "TeamMemberQuota", "Team<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TkPack", "TkPackTrail", "Tkshop", "TkshopEnterprise", "TkshopStandard", "Traffic", "TransitTraffic", "TransitTrafficV2", "UserExclusiveIp", "Voucher"]}, "id": {"type": "integer", "format": "int64"}, "orderItemId": {"type": "integer", "format": "int64"}, "periodUnit": {"type": "string", "enum": ["Buyout", "Byte", "GB", "GB天", "个", "个天", "分钟", "周", "天", "年", "张", "无", "月", "次"]}, "remarks": {"type": "string"}, "valid": {"type": "boolean", "description": "是否在有效期内（根据createTime & validDays计算出来的）", "example": false}}}, "GiftCardPackItemVo": {"title": "GiftCardPackItemVo", "type": "object", "properties": {"activatedTeamId": {"type": "integer", "format": "int64"}, "activeTime": {"type": "string", "format": "date-time"}, "amount": {"type": "number", "format": "bigdecimal"}, "cardNumber": {"type": "string"}, "cardPackId": {"type": "integer", "format": "int64"}, "cardPassword": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "disabled": {"type": "boolean"}, "duration": {"type": "integer", "description": "当为rpa礼品卡时，表示该卡有几个 periodUnit 月/周", "format": "int32"}, "expireDate": {"type": "string", "format": "date-time"}, "giftCardType": {"type": "string", "enum": ["Credit", "RpaVoucher"]}, "goodsId": {"type": "integer", "format": "int64"}, "goodsType": {"type": "string", "enum": ["Credit", "CreditPack", "ExclusiveIp", "FingerprintQuota", "IosDeveloperApprove", "Ip", "IpGo", "IpProxy", "MarketFlow", "None", "PluginPack", "PriceDifference", "ProxyTraffic", "RpaCaptcha", "RpaExecuteQuota", "RpaMobile", "RpaOpenAi", "RpaSendEmail", "RpaSendSms", "RpaSendWeChat", "Rpa_Voucher_Base", "Rpa_Voucher_Performance", "SharingIp", "ShopQuota", "ShopSecurityPolicy", "StorageQuota", "TeamMemberQuota", "Team<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TkPack", "TkPackTrail", "Tkshop", "TkshopEnterprise", "TkshopStandard", "Traffic", "TransitTraffic", "TransitTrafficV2", "UserExclusiveIp", "Voucher"]}, "id": {"type": "integer", "format": "int64"}, "orderItemId": {"type": "integer", "format": "int64"}, "periodUnit": {"type": "string", "enum": ["Buyout", "Byte", "GB", "GB天", "个", "个天", "分钟", "周", "天", "年", "张", "无", "月", "次"]}, "remarks": {"type": "string"}, "valid": {"type": "boolean", "description": "是否在有效期内（根据createTime & validDays计算出来的）", "example": false}}}, "GoodsDto": {"title": "GoodsDto", "type": "object", "properties": {"arch": {"type": "string"}, "bandwidth": {"type": "integer", "format": "int32"}, "buyoutPrice": {"type": "number", "format": "bigdecimal"}, "city": {"type": "string"}, "cost": {"type": "number", "format": "bigdecimal"}, "countryCode": {"type": "string"}, "cpu": {"type": "integer", "format": "int32"}, "currency": {"type": "string", "enum": ["CREDIT", "RMB", "USD"]}, "dayCost": {"type": "number", "format": "bigdecimal"}, "dayPrice": {"type": "number", "format": "bigdecimal"}, "dayTraffic": {"type": "number", "format": "double"}, "description": {"type": "string"}, "disk": {"type": "integer", "format": "int32"}, "diskCategory": {"type": "string"}, "dynamic": {"type": "boolean"}, "goodsType": {"type": "string", "enum": ["Credit", "CreditPack", "ExclusiveIp", "FingerprintQuota", "IosDeveloperApprove", "Ip", "IpGo", "IpProxy", "MarketFlow", "None", "PluginPack", "PriceDifference", "ProxyTraffic", "RpaCaptcha", "RpaExecuteQuota", "RpaMobile", "RpaOpenAi", "RpaSendEmail", "RpaSendSms", "RpaSendWeChat", "Rpa_Voucher_Base", "Rpa_Voucher_Performance", "SharingIp", "ShopQuota", "ShopSecurityPolicy", "StorageQuota", "TeamMemberQuota", "Team<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TkPack", "TkPackTrail", "Tkshop", "TkshopEnterprise", "TkshopStandard", "Traffic", "TransitTraffic", "TransitTrafficV2", "UserExclusiveIp", "Voucher"]}, "id": {"type": "integer", "format": "int64"}, "initialQuantity": {"type": "integer", "format": "int32"}, "instanceType": {"type": "string"}, "ipv6": {"type": "boolean"}, "listPrice": {"type": "number", "format": "bigdecimal"}, "mem": {"type": "number", "format": "double"}, "name": {"type": "string"}, "networkType": {"type": "string", "enum": ["cloudIdc", "mobile", "proxyIdc", "residential", "unknown", "unknownIdc"]}, "onSale": {"type": "string", "enum": ["Disabled", "Offline", "Online"]}, "perfLevel": {"type": "string", "enum": ["CostEffective", "HighlyConcurrent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "None", "RemoteLogin", "Unlim<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "periodUnit": {"type": "string", "enum": ["Buyout", "Byte", "GB", "GB天", "个", "个天", "分钟", "周", "天", "年", "张", "无", "月", "次"]}, "pipeType": {"type": "string", "enum": ["None", "Proxy", "Tunnel", "TunnelFailToProxy"]}, "platform": {"type": "string", "enum": ["android", "linux", "macos", "unknown", "web", "windows", "windows7"]}, "price": {"type": "number", "format": "bigdecimal"}, "provider": {"type": "string"}, "region": {"type": "string"}, "remoteLogin": {"type": "boolean"}, "resourceId": {"type": "integer", "format": "int64"}, "sortNo": {"type": "integer", "format": "int32"}, "tcpfp": {"type": "boolean"}, "traffic": {"type": "number", "format": "double"}, "trafficCurrency": {"type": "string", "enum": ["CREDIT", "RMB", "USD"]}, "trafficPrice": {"type": "number", "format": "bigdecimal"}, "updateTime": {"type": "string", "format": "date-time"}, "weekCost": {"type": "number", "format": "bigdecimal"}, "weekPrice": {"type": "number", "format": "bigdecimal"}, "weekTraffic": {"type": "number", "format": "double"}}}, "GoodsVo": {"title": "GoodsVo", "type": "object", "properties": {"arch": {"type": "string"}, "bandwidth": {"type": "integer", "format": "int32"}, "buyoutPrice": {"type": "number", "format": "bigdecimal"}, "city": {"type": "string"}, "cost": {"type": "number", "format": "bigdecimal"}, "country": {"type": "string"}, "countryCode": {"type": "string"}, "countryEn": {"type": "string", "description": "国家英文"}, "cpu": {"type": "integer", "format": "int32"}, "currency": {"type": "string", "enum": ["CREDIT", "RMB", "USD"]}, "dayCost": {"type": "number", "format": "bigdecimal"}, "dayPrice": {"type": "number", "format": "bigdecimal"}, "dayTraffic": {"type": "number", "format": "double"}, "description": {"type": "string"}, "disk": {"type": "integer", "format": "int32"}, "diskCategory": {"type": "string"}, "dynamic": {"type": "boolean"}, "goodsType": {"type": "string", "enum": ["Credit", "CreditPack", "ExclusiveIp", "FingerprintQuota", "IosDeveloperApprove", "Ip", "IpGo", "IpProxy", "MarketFlow", "None", "PluginPack", "PriceDifference", "ProxyTraffic", "RpaCaptcha", "RpaExecuteQuota", "RpaMobile", "RpaOpenAi", "RpaSendEmail", "RpaSendSms", "RpaSendWeChat", "Rpa_Voucher_Base", "Rpa_Voucher_Performance", "SharingIp", "ShopQuota", "ShopSecurityPolicy", "StorageQuota", "TeamMemberQuota", "Team<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TkPack", "TkPackTrail", "Tkshop", "TkshopEnterprise", "TkshopStandard", "Traffic", "TransitTraffic", "TransitTrafficV2", "UserExclusiveIp", "Voucher"]}, "id": {"type": "integer", "format": "int64"}, "initialQuantity": {"type": "integer", "format": "int32"}, "instanceType": {"type": "string"}, "ipv6": {"type": "boolean"}, "listPrice": {"type": "number", "format": "bigdecimal"}, "mem": {"type": "number", "format": "double"}, "name": {"type": "string"}, "networkType": {"type": "string", "enum": ["cloudIdc", "mobile", "proxyIdc", "residential", "unknown", "unknownIdc"]}, "onSale": {"type": "string", "enum": ["Disabled", "Offline", "Online"]}, "perfLevel": {"type": "string", "enum": ["CostEffective", "HighlyConcurrent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "None", "RemoteLogin", "Unlim<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "periodUnit": {"type": "string", "enum": ["Buyout", "Byte", "GB", "GB天", "个", "个天", "分钟", "周", "天", "年", "张", "无", "月", "次"]}, "pipeType": {"type": "string", "enum": ["None", "Proxy", "Tunnel", "TunnelFailToProxy"]}, "platform": {"type": "string", "enum": ["android", "linux", "macos", "unknown", "web", "windows", "windows7"]}, "price": {"type": "number", "format": "bigdecimal"}, "provider": {"type": "string"}, "providerName": {"type": "string"}, "region": {"type": "string"}, "remoteLogin": {"type": "boolean"}, "resourceId": {"type": "integer", "format": "int64"}, "sortNo": {"type": "integer", "format": "int32"}, "tcpfp": {"type": "boolean"}, "traffic": {"type": "number", "format": "double"}, "trafficCurrency": {"type": "string", "enum": ["CREDIT", "RMB", "USD"]}, "trafficPrice": {"type": "number", "format": "bigdecimal"}, "updateTime": {"type": "string", "format": "date-time"}, "weekCost": {"type": "number", "format": "bigdecimal"}, "weekPrice": {"type": "number", "format": "bigdecimal"}, "weekTraffic": {"type": "number", "format": "double"}}}, "InvoiceHistoryVo": {"title": "InvoiceHistoryVo", "type": "object", "properties": {"account": {"type": "string"}, "address": {"type": "string"}, "amount": {"type": "number", "format": "bigdecimal"}, "bank": {"type": "string"}, "consoleDesc": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "electronic": {"type": "boolean", "description": "是否电子发票", "example": false}, "email": {"type": "string"}, "expressCompany": {"type": "string"}, "expressNumber": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "invoiceCode": {"type": "string"}, "invoiceNumber": {"type": "string"}, "invoiceStatus": {"type": "string", "enum": ["Applied", "Invoiced", "NotInvoiced"]}, "invoiceType": {"type": "string", "enum": ["EntNormal", "EntSpecial", "None", "Personal"]}, "number": {"type": "string"}, "receiverAddress": {"type": "string"}, "receiverName": {"type": "string"}, "receiverPhone": {"type": "string"}, "tax": {"type": "number", "format": "bigdecimal"}, "taxNum": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}, "title": {"type": "string"}, "userDesc": {"type": "string"}}}, "InvoiceInfoVo": {"title": "InvoiceInfoVo", "type": "object", "properties": {"account": {"type": "string"}, "address": {"type": "string"}, "bank": {"type": "string"}, "electronic": {"type": "boolean"}, "email": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "invoiceType": {"type": "string", "enum": ["EntNormal", "EntSpecial", "None", "Personal"]}, "number": {"type": "string", "description": "电话号码"}, "receiverAddress": {"type": "string"}, "receiverName": {"type": "string"}, "receiverPhone": {"type": "string"}, "taxNum": {"type": "string"}, "title": {"type": "string"}}}, "InvoiceSummaryVo": {"title": "InvoiceSummaryVo", "type": "object", "properties": {"invoiceInfo": {"$ref": "#/components/schemas/InvoiceInfoVo"}, "notInvoicedAmount": {"type": "number", "description": "可开票金额", "format": "bigdecimal"}}}, "IpLocationDto": {"title": "IpLocationDto", "type": "object", "properties": {"city": {"type": "string"}, "cityEn": {"type": "string"}, "continent": {"type": "string"}, "continentCode": {"type": "string"}, "continentEn": {"type": "string"}, "country": {"type": "string"}, "countryCode": {"type": "string"}, "countryEn": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "geonameId": {"type": "integer", "format": "int32"}, "id": {"type": "integer", "format": "int64"}, "inEu": {"type": "boolean"}, "latitude": {"type": "number", "format": "double"}, "level": {"type": "string", "enum": ["City", "Continent", "Country", "District", "None", "Province", "Unknown"]}, "locale": {"type": "string"}, "longitude": {"type": "number", "format": "double"}, "postalCode": {"type": "string"}, "province": {"type": "string"}, "provinceCode": {"type": "string"}, "provinceEn": {"type": "string"}, "show": {"type": "boolean"}, "teamId": {"type": "integer", "format": "int64"}, "timezone": {"type": "string"}}}, "IpWithLocationVo": {"title": "IpWithLocationVo", "type": "object", "properties": {"autoRenew": {"type": "boolean"}, "cloudProvider": {"type": "string"}, "cloudRegion": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "directDownTraffic": {"type": "integer", "format": "int64"}, "directUpTraffic": {"type": "integer", "format": "int64"}, "domestic": {"type": "boolean"}, "downTraffic": {"type": "integer", "format": "int64"}, "dynamic": {"type": "boolean"}, "eipId": {"type": "integer", "format": "int64"}, "enableWhitelist": {"type": "boolean"}, "expireStatus": {"type": "string", "description": "过期状态", "enum": ["Expired", "Expiring", "Normal"]}, "forbiddenLongLatitude": {"type": "boolean"}, "gatewayId": {"type": "integer", "format": "int64"}, "goodsId": {"type": "integer", "format": "int64"}, "goodsType": {"type": "string", "enum": ["Credit", "CreditPack", "ExclusiveIp", "FingerprintQuota", "IosDeveloperApprove", "Ip", "IpGo", "IpProxy", "MarketFlow", "None", "PluginPack", "PriceDifference", "ProxyTraffic", "RpaCaptcha", "RpaExecuteQuota", "RpaMobile", "RpaOpenAi", "RpaSendEmail", "RpaSendSms", "RpaSendWeChat", "Rpa_Voucher_Base", "Rpa_Voucher_Performance", "SharingIp", "ShopQuota", "ShopSecurityPolicy", "StorageQuota", "TeamMemberQuota", "Team<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TkPack", "TkPackTrail", "Tkshop", "TkshopEnterprise", "TkshopStandard", "Traffic", "TransitTraffic", "TransitTrafficV2", "UserExclusiveIp", "Voucher"]}, "id": {"type": "integer", "format": "int64"}, "importType": {"type": "string", "enum": ["Platform", "User"]}, "invalidTime": {"type": "string", "format": "date-time"}, "ip": {"type": "string"}, "ipv6": {"type": "boolean"}, "lastProbeTime": {"type": "string", "format": "date-time"}, "latitude": {"type": "number", "format": "double"}, "locale": {"type": "string"}, "location": {"$ref": "#/components/schemas/IpLocationDto"}, "locationId": {"type": "integer", "format": "int64"}, "longitude": {"type": "number", "format": "double"}, "name": {"type": "string"}, "networkType": {"type": "string", "enum": ["cloudIdc", "mobile", "proxyIdc", "residential", "unknown", "unknownIdc"]}, "operateStatus": {"type": "string", "enum": ["shared", "sharing", "sole", "transferring"]}, "originalTeam": {"type": "integer", "format": "int64"}, "periodUnit": {"type": "string", "enum": ["Buyout", "Byte", "GB", "GB天", "个", "个天", "分钟", "周", "天", "年", "张", "无", "月", "次"]}, "pipeType": {"type": "string", "enum": ["None", "Proxy", "Tunnel", "TunnelFailToProxy"]}, "preferTransit": {"type": "integer", "format": "int64"}, "probeError": {"type": "string"}, "protoType": {"type": "string", "enum": ["http", "httpTunnel", "ipgo", "luminati", "socks5", "ssh", "vps"]}, "providerName": {"type": "string", "description": "供应商名称"}, "realIp": {"type": "string"}, "refreshUrl": {"type": "string"}, "remoteLogin": {"type": "boolean"}, "renewPrice": {"type": "number", "format": "bigdecimal"}, "source": {"type": "string"}, "speedLimit": {"type": "integer", "format": "int32"}, "status": {"type": "string", "enum": ["Available", "Pending", "Unavailable"]}, "sticky": {"type": "boolean"}, "teamId": {"type": "integer", "format": "int64"}, "testingTime": {"type": "integer", "format": "int32"}, "timezone": {"type": "string"}, "traffic": {"type": "number", "format": "double"}, "trafficCurrency": {"type": "string", "enum": ["CREDIT", "RMB", "USD"]}, "trafficPrice": {"type": "number", "format": "bigdecimal"}, "trafficUnlimited": {"type": "boolean"}, "transitType": {"type": "string", "enum": ["Auto", "Direct", "Transit"]}, "tunnelTypes": {"type": "string"}, "upTraffic": {"type": "integer", "format": "int64"}, "valid": {"type": "boolean"}, "validEndDate": {"type": "string", "format": "date-time"}, "vpsId": {"type": "integer", "format": "int64"}}}, "ItemPriceInfo": {"title": "ItemPriceInfo", "type": "object", "properties": {"costPrice": {"type": "number", "format": "bigdecimal"}, "currentValidEndTime": {"type": "string", "description": "当前过期时间", "format": "date-time"}, "discount": {"$ref": "#/components/schemas/DiscountsVo"}, "discountAmount": {"type": "number", "description": "打折减掉的金额，如果是打折的话", "format": "bigdecimal"}, "goodsId": {"type": "integer", "format": "int64"}, "payablePrice": {"type": "number", "description": "应付价格", "format": "bigdecimal"}, "presentAmount": {"type": "number", "description": "赠送数量，如果是赠送的话。目前只出现在购买花瓣", "format": "bigdecimal"}, "price": {"type": "number", "description": "item总价", "format": "bigdecimal"}, "validEndTime": {"type": "string", "description": "续费后到期时间", "format": "date-time"}}}, "LockAndPayOrderRequest": {"title": "LockAndPayOrderRequest", "type": "object", "properties": {"agreement": {"type": "boolean", "description": "已经勾选阅读和同意使用协议，没什么用", "example": false}, "balanceAmount": {"type": "number", "description": "余额抵扣金额", "format": "bigdecimal"}, "immediatePay": {"type": "boolean", "description": "是否立即支付（点稍候支付该属性传false）", "example": false}, "orderId": {"type": "integer", "format": "int64"}, "payType": {"type": "string", "enum": ["AliPay", "BalancePay", "BankPay", "WechatPay"]}, "voucherAmount": {"type": "number", "description": "代金券抵扣金额，不得大于代金券余额", "format": "bigdecimal"}, "voucherId": {"type": "integer", "description": "要使用的代金券id", "format": "int64"}}}, "MergePayOrdersRequest": {"title": "MergePayOrdersRequest", "type": "object", "properties": {"agreement": {"type": "boolean", "description": "已经勾选阅读和同意使用协议，没什么用", "example": false}, "balanceAmount": {"type": "number", "description": "余额抵扣金额", "format": "bigdecimal"}, "immediatePay": {"type": "boolean", "description": "是否立即支付（点稍候支付该属性传false）", "example": false}, "orderIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "payType": {"type": "string", "enum": ["AliPay", "BalancePay", "BankPay", "WechatPay"]}, "voucherAmount": {"type": "number", "description": "代金券抵扣金额，不得大于代金券余额", "format": "bigdecimal"}, "voucherId": {"type": "integer", "description": "要使用的代金券id", "format": "int64"}}}, "NotInvoicedOrderVo": {"title": "NotInvoicedOrderVo", "type": "object", "properties": {"automatic": {"type": "boolean", "description": "是否为自动订单，例如自动续费订单", "example": false}, "createTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "orderType": {"type": "string", "enum": ["BuyGiftCard", "BuyIp", "BuyPluginPack", "BuyRpaVoucher", "BuyTkPack", "BuyTkshop", "BuyTraffic", "CashOut", "MakeupPriceDifference", "<PERSON><PERSON>", "OrderCancel", "PartnerBuyVoucher", "PartnerDraw", "PartnerRecharge", "PartnerRechargeCredit", "PartnerRenewRpaVoucher", "Present", "Recharge", "RechargeCredit", "Refund", "RenewIp", "RenewPluginPack", "RenewRpaVoucher", "RenewTkPack", "RenewTkshop", "UpgradeTkshop"]}, "realPrice": {"type": "number", "description": "实付金额，即现金支付金额;可开票金额以此为依据", "format": "bigdecimal"}, "realRefundAmount": {"type": "number", "format": "bigdecimal"}, "serialNumber": {"type": "string"}}}, "NotifyBankPayFailedRequest": {"title": "NotifyBankPayFailedRequest", "type": "object", "properties": {"orderId": {"type": "integer", "format": "int64"}, "payStatus": {"type": "string", "enum": ["CANCELED", "Created", "Locked", "PAID", "PartialRefund", "REFUNDED", "WAIT_CONFIRM"]}, "remarks": {"type": "string"}}}, "NotifyBankPaySuccessRequest": {"title": "NotifyBankPaySuccessRequest", "type": "object", "properties": {"orderId": {"type": "integer", "format": "int64"}, "remarks": {"type": "string"}, "thirdPartOrderNumber": {"type": "string"}}}, "OrderDetailVo": {"title": "OrderDetailVo", "type": "object", "properties": {"automatic": {"type": "boolean", "description": "是否为自动订单，例如自动续费订单", "example": false}, "balanceAmount": {"type": "number", "description": "余额抵扣金额", "format": "bigdecimal"}, "bankPayConfig": {"description": "如果是银行卡支付，则包含银行卡信息", "$ref": "#/components/schemas/BankPayConfig"}, "cashPayType": {"type": "string", "enum": ["AliPay", "BalancePay", "BankPay", "WechatPay"]}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "discountReason": {"type": "string"}, "earnedPartner": {"type": "integer", "format": "int64"}, "earnedPartnerDto": {"description": "钱款入账的代理商信息", "$ref": "#/components/schemas/PartnerDto"}, "id": {"type": "integer", "format": "int64"}, "lockTime": {"type": "string", "description": "订单锁定时间;订单锁定之后60分钟不支付会被取消掉", "format": "date-time"}, "nickname": {"type": "string"}, "orderType": {"type": "string", "enum": ["BuyGiftCard", "BuyIp", "BuyPluginPack", "BuyRpaVoucher", "BuyTkPack", "BuyTkshop", "BuyTraffic", "CashOut", "MakeupPriceDifference", "<PERSON><PERSON>", "OrderCancel", "PartnerBuyVoucher", "PartnerDraw", "PartnerRecharge", "PartnerRechargeCredit", "PartnerRenewRpaVoucher", "Present", "Recharge", "RechargeCredit", "Refund", "RenewIp", "RenewPluginPack", "RenewRpaVoucher", "RenewTkPack", "RenewTkshop", "UpgradeTkshop"]}, "parentOrderId": {"type": "integer", "description": "如果父订单不为空说明该订单被选中合并支付了", "format": "int64"}, "payStatus": {"type": "string", "enum": ["CANCELED", "Created", "Locked", "PAID", "PartialRefund", "REFUNDED", "WAIT_CONFIRM"]}, "payablePrice": {"type": "number", "description": "订单应付总额，比如说买一年打8折或者销售改价之后的价格", "format": "bigdecimal"}, "presentAmount": {"type": "number", "description": "充值赠送金额;只在充值订单有效", "format": "bigdecimal"}, "productionRemarks": {"type": "string", "description": "生产备注"}, "productionStatus": {"type": "string", "description": "生产状态", "enum": ["Finished", "NotStart", "ProduceError", "Producing", "ReFunded", "RefundError", "Refunding", "WaitReFund"]}, "realPrice": {"type": "number", "description": "实付金额，即现金支付金额;可开票金额以此为依据", "format": "bigdecimal"}, "salesReduction": {"type": "number", "description": "销售改价折现", "format": "bigdecimal"}, "serialNumber": {"type": "string"}, "totalPrice": {"type": "number", "description": "订单总额", "format": "bigdecimal"}, "voucherAmount": {"type": "number", "description": "代金券抵扣金额", "format": "bigdecimal"}, "voucherCardNumber": {"type": "string", "description": "代金券卡号"}}}, "OrderResourceVo": {"title": "OrderResourceVo", "type": "object", "properties": {"cloudName": {"type": "string"}, "count": {"type": "integer", "format": "int32"}, "discountReason": {"type": "string"}, "extraInfo": {"type": "string"}, "giftCardPack": {"$ref": "#/components/schemas/GiftCardPackDetailVo"}, "goods": {"description": "订单Item的商品信息", "$ref": "#/components/schemas/GoodsVo"}, "goodsId": {"type": "integer", "format": "int64"}, "goodsPeriodUnit": {"type": "string", "enum": ["Buyout", "Byte", "GB", "GB天", "个", "个天", "分钟", "周", "天", "年", "张", "无", "月", "次"]}, "goodsType": {"type": "string", "enum": ["Credit", "CreditPack", "ExclusiveIp", "FingerprintQuota", "IosDeveloperApprove", "Ip", "IpGo", "IpProxy", "MarketFlow", "None", "PluginPack", "PriceDifference", "ProxyTraffic", "RpaCaptcha", "RpaExecuteQuota", "RpaMobile", "RpaOpenAi", "RpaSendEmail", "RpaSendSms", "RpaSendWeChat", "Rpa_Voucher_Base", "Rpa_Voucher_Performance", "SharingIp", "ShopQuota", "ShopSecurityPolicy", "StorageQuota", "TeamMemberQuota", "Team<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TkPack", "TkPackTrail", "Tkshop", "TkshopEnterprise", "TkshopStandard", "Traffic", "TransitTraffic", "TransitTrafficV2", "UserExclusiveIp", "Voucher"]}, "id": {"type": "integer", "format": "int64"}, "ip": {"$ref": "#/components/schemas/IpWithLocationVo"}, "ips": {"type": "array", "items": {"$ref": "#/components/schemas/TeamIpDto"}}, "itemPrice": {"type": "number", "format": "bigdecimal"}, "orderId": {"type": "integer", "format": "int64"}, "orderType": {"type": "string", "enum": ["BuyGiftCard", "BuyIp", "BuyPluginPack", "BuyRpaVoucher", "BuyTkPack", "BuyTkshop", "BuyTraffic", "CashOut", "MakeupPriceDifference", "<PERSON><PERSON>", "OrderCancel", "PartnerBuyVoucher", "PartnerDraw", "PartnerRecharge", "PartnerRechargeCredit", "PartnerRenewRpaVoucher", "Present", "Recharge", "RechargeCredit", "Refund", "RenewIp", "RenewPluginPack", "RenewRpaVoucher", "RenewTkPack", "RenewTkshop", "UpgradeTkshop"]}, "productionRemarks": {"type": "string"}, "productionStatus": {"type": "string", "enum": ["Finished", "NotStart", "ProduceError", "Producing", "ReFunded", "RefundError", "Refunding", "WaitReFund"]}, "proxyIpId": {"type": "integer", "format": "int64"}, "resourceId": {"type": "integer", "format": "int64"}, "resourceName": {"type": "string"}, "resourceType": {"type": "string", "enum": ["AK", "Activity", "Audit", "BlockElements", "Cloud", "CrsOrder", "CrsProduct", "DiskFile", "FingerPrint", "FingerPrintTemplate", "Gateway", "GhCreator", "GhGifter", "GhJobPlan", "GhUser", "GhVideoCreator", "GiftCardPack", "InsTeamUser", "InsUser", "Invoice", "Ip", "IpPool", "IppIp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "KakaoFriend", "KolCreator", "MobileAccount", "None", "Orders", "PluginTeamPack", "Record", "RpaFlow", "RpaTask", "RpaTaskItem", "RpaVoucher", "Shop", "ShopSession", "Tag", "TeamDiskRoot", "TeamMobile", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TkCreator", "TkTeamPack", "Tkshop", "<PERSON>ks<PERSON><PERSON>uyer", "TkshopCreator", "TrafficPack", "TunnelVps", "Users", "View", "Voucher", "XhsAccount"]}, "teamId": {"type": "integer", "format": "int64"}, "vpsName": {"type": "string"}}}, "OrdersDto": {"title": "OrdersDto", "type": "object", "properties": {"autoRenew": {"type": "boolean"}, "balanceAmount": {"type": "number", "description": "余额抵扣金额", "format": "bigdecimal"}, "buyerId": {"type": "integer", "format": "int64"}, "cashPayType": {"type": "string", "enum": ["AliPay", "BalancePay", "BankPay", "WechatPay"]}, "costPrice": {"type": "number", "format": "bigdecimal"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "discountReason": {"type": "string"}, "distributionCode": {"type": "integer", "format": "int64"}, "distributor": {"type": "integer", "format": "int64"}, "drawPrice": {"type": "number", "format": "bigdecimal"}, "drawStatus": {"type": "string", "enum": ["Applying", "Canceled", "Drawn", "NotDrawn", "NotSupported", "Paying"]}, "earnedPartner": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "invalidReason": {"type": "string"}, "invoiceStatus": {"type": "string", "enum": ["Applied", "Invoiced", "NotInvoiced"]}, "lockTime": {"type": "string", "format": "date-time"}, "orderSource": {"type": "string", "enum": ["Automatic", "Offline", "Users"]}, "orderType": {"type": "string", "enum": ["BuyGiftCard", "BuyIp", "BuyPluginPack", "BuyRpaVoucher", "BuyTkPack", "BuyTkshop", "BuyTraffic", "CashOut", "MakeupPriceDifference", "<PERSON><PERSON>", "OrderCancel", "PartnerBuyVoucher", "PartnerDraw", "PartnerRecharge", "PartnerRechargeCredit", "PartnerRenewRpaVoucher", "Present", "Recharge", "RechargeCredit", "Refund", "RenewIp", "RenewPluginPack", "RenewRpaVoucher", "RenewTkPack", "RenewTkshop", "UpgradeTkshop"]}, "params": {"type": "string"}, "parentOrderId": {"type": "integer", "format": "int64"}, "payStatus": {"type": "string", "enum": ["CANCELED", "Created", "Locked", "PAID", "PartialRefund", "REFUNDED", "WAIT_CONFIRM"]}, "payTime": {"type": "string", "format": "date-time"}, "payablePrice": {"type": "number", "description": "订单应付总额，比如说买一年打8折或者销售改价之后的价格", "format": "bigdecimal"}, "presentAmount": {"type": "number", "description": "充值赠送金额;只在充值订单有效", "format": "bigdecimal"}, "productionRemarks": {"type": "string", "description": "生产备注"}, "productionStatus": {"type": "string", "description": "生产状态", "enum": ["Finished", "NotStart", "ProduceError", "Producing", "ReFunded", "RefundError", "Refunding", "WaitReFund"]}, "productionTime": {"type": "string", "format": "date-time"}, "realPrice": {"type": "number", "description": "实付金额，即现金支付金额;可开票金额以此为依据", "format": "bigdecimal"}, "realRefundAmount": {"type": "number", "format": "bigdecimal"}, "reductionSalesId": {"type": "integer", "format": "int64"}, "remarks": {"type": "string"}, "salesReduction": {"type": "number", "description": "销售改价折现", "format": "bigdecimal"}, "serialNumber": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}, "testing": {"type": "boolean"}, "thirdPartOrderNumber": {"type": "string"}, "totalPrice": {"type": "number", "description": "订单总额", "format": "bigdecimal"}, "voucherAmount": {"type": "number", "description": "代金券卡号", "format": "bigdecimal"}, "voucherId": {"type": "integer", "format": "int64"}}}, "PageResult«BalanceInOutDetailVo»": {"title": "PageResult«BalanceInOutDetailVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/BalanceInOutDetailVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«CreditInOutDetailVo»": {"title": "PageResult«CreditInOutDetailVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/CreditInOutDetailVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«InvoiceHistoryVo»": {"title": "PageResult«InvoiceHistoryVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/InvoiceHistoryVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«OrderDetailVo»": {"title": "PageResult«OrderDetailVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/OrderDetailVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«VoucherVo»": {"title": "PageResult«VoucherVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/VoucherVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PartnerDto": {"title": "PartnerDto", "type": "object", "properties": {"bankAccount": {"type": "string"}, "bankName": {"type": "string"}, "bankNo": {"type": "string"}, "contactName": {"type": "string"}, "contactPhone": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "fullName": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "managerId": {"type": "integer", "format": "int64"}, "oemSupport": {"type": "boolean"}, "openapiSupport": {"type": "boolean"}, "organizedTeamAccountQuota": {"type": "integer", "format": "int32"}, "organizedTeamUserQuota": {"type": "integer", "format": "int32"}, "password": {"type": "string"}, "role": {"type": "string", "enum": ["Broker", "Organizer"]}, "shortName": {"type": "string"}, "status": {"type": "string", "enum": ["ACTIVE", "BLOCK", "DELETED", "INACTIVE"]}, "teamId": {"type": "integer", "format": "int64"}, "userId": {"type": "integer", "format": "int64"}}}, "PaymentSummaryVo": {"title": "PaymentSummaryVo", "type": "object", "properties": {"balance": {"type": "number", "description": "可用余额（不包含锁定余额）", "format": "bigdecimal"}, "credit": {"type": "number", "description": "可用花瓣", "format": "bigdecimal"}, "creditCost": {"description": "昨日花瓣消耗", "$ref": "#/components/schemas/DailyCreditCost"}, "lockBalance": {"type": "number", "description": "锁定余额", "format": "bigdecimal"}, "monthCost": {"type": "number", "description": "每月预估费用", "format": "bigdecimal"}, "nextDeductionDate": {"type": "string", "description": "下次扣费日期", "format": "date-time"}, "notInvoicedAmount": {"type": "number", "description": "可开票金额", "format": "bigdecimal"}, "platformIpAutoRenewCount": {"type": "integer", "description": "已开启自动续费的平台IP数", "format": "int32"}, "platformIpCount": {"type": "integer", "description": "已购买的平台IP数", "format": "int32"}, "teamId": {"type": "integer", "format": "int64"}, "thirtyDaysAverageCredit": {"type": "number", "format": "bigdecimal"}, "voucherCount": {"type": "integer", "description": "可用代金券数量", "format": "int32"}}}, "RefundRequest": {"title": "RefundRequest", "type": "object", "properties": {"backtrack": {"type": "boolean", "description": "实付金额是否原路返回，为空和false都表示将实付金额退到余额", "example": false}, "balanceAmount": {"type": "number", "description": "余额退多少，不可超过订单实际余额支付额。为空表示全额退", "format": "bigdecimal"}, "orderId": {"type": "integer", "format": "int64"}, "realPayAmount": {"type": "number", "description": "实付金额退多少，不可超过订单实际实付额。为空表示全额退", "format": "bigdecimal"}, "remarks": {"type": "string"}, "voucher": {"type": "boolean", "description": "是否退代金券，为空和true都表示退", "example": false}}}, "Resource": {"title": "Resource", "type": "object"}, "TeamIpDto": {"title": "TeamIpDto", "type": "object", "properties": {"autoRenew": {"type": "boolean"}, "cloudProvider": {"type": "string"}, "cloudRegion": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "directDownTraffic": {"type": "integer", "format": "int64"}, "directUpTraffic": {"type": "integer", "format": "int64"}, "domestic": {"type": "boolean"}, "downTraffic": {"type": "integer", "format": "int64"}, "dynamic": {"type": "boolean"}, "eipId": {"type": "integer", "format": "int64"}, "enableWhitelist": {"type": "boolean"}, "forbiddenLongLatitude": {"type": "boolean"}, "gatewayId": {"type": "integer", "format": "int64"}, "goodsId": {"type": "integer", "format": "int64"}, "goodsType": {"type": "string", "enum": ["Credit", "CreditPack", "ExclusiveIp", "FingerprintQuota", "IosDeveloperApprove", "Ip", "IpGo", "IpProxy", "MarketFlow", "None", "PluginPack", "PriceDifference", "ProxyTraffic", "RpaCaptcha", "RpaExecuteQuota", "RpaMobile", "RpaOpenAi", "RpaSendEmail", "RpaSendSms", "RpaSendWeChat", "Rpa_Voucher_Base", "Rpa_Voucher_Performance", "SharingIp", "ShopQuota", "ShopSecurityPolicy", "StorageQuota", "TeamMemberQuota", "Team<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TkPack", "TkPackTrail", "Tkshop", "TkshopEnterprise", "TkshopStandard", "Traffic", "TransitTraffic", "TransitTrafficV2", "UserExclusiveIp", "Voucher"]}, "id": {"type": "integer", "format": "int64"}, "importType": {"type": "string", "enum": ["Platform", "User"]}, "invalidTime": {"type": "string", "format": "date-time"}, "ip": {"type": "string"}, "lastProbeTime": {"type": "string", "format": "date-time"}, "latitude": {"type": "number", "format": "double"}, "locale": {"type": "string"}, "locationId": {"type": "integer", "format": "int64"}, "longitude": {"type": "number", "format": "double"}, "name": {"type": "string"}, "networkType": {"type": "string", "enum": ["cloudIdc", "mobile", "proxyIdc", "residential", "unknown", "unknownIdc"]}, "operateStatus": {"type": "string", "enum": ["shared", "sharing", "sole", "transferring"]}, "originalTeam": {"type": "integer", "format": "int64"}, "periodUnit": {"type": "string", "enum": ["Buyout", "Byte", "GB", "GB天", "个", "个天", "分钟", "周", "天", "年", "张", "无", "月", "次"]}, "pipeType": {"type": "string", "enum": ["None", "Proxy", "Tunnel", "TunnelFailToProxy"]}, "preferTransit": {"type": "integer", "format": "int64"}, "probeError": {"type": "string"}, "realIp": {"type": "string"}, "refreshUrl": {"type": "string"}, "remoteLogin": {"type": "boolean"}, "renewPrice": {"type": "number", "format": "bigdecimal"}, "source": {"type": "string"}, "speedLimit": {"type": "integer", "format": "int32"}, "status": {"type": "string", "enum": ["Available", "Pending", "Unavailable"]}, "sticky": {"type": "boolean"}, "teamId": {"type": "integer", "format": "int64"}, "testingTime": {"type": "integer", "format": "int32"}, "timezone": {"type": "string"}, "traffic": {"type": "number", "format": "double"}, "trafficCurrency": {"type": "string", "enum": ["CREDIT", "RMB", "USD"]}, "trafficPrice": {"type": "number", "format": "bigdecimal"}, "trafficUnlimited": {"type": "boolean"}, "transitType": {"type": "string", "enum": ["Auto", "Direct", "Transit"]}, "tunnelTypes": {"type": "string"}, "upTraffic": {"type": "integer", "format": "int64"}, "valid": {"type": "boolean"}, "validEndDate": {"type": "string", "format": "date-time"}, "vpsId": {"type": "integer", "format": "int64"}}}, "UpdateOrderPaymentRequest": {"title": "UpdateOrderPaymentRequest", "type": "object", "properties": {"agreement": {"type": "boolean", "description": "已经勾选阅读和同意使用协议，没什么用", "example": false}, "balanceAmount": {"type": "number", "description": "余额抵扣金额", "format": "bigdecimal"}, "immediatePay": {"type": "boolean", "description": "是否立即支付（点稍候支付该属性传false）", "example": false}, "orderId": {"type": "integer", "format": "int64"}, "payType": {"type": "string", "enum": ["AliPay", "BalancePay", "BankPay", "WechatPay"]}, "voucherAmount": {"type": "number", "description": "代金券抵扣金额，不得大于代金券余额", "format": "bigdecimal"}, "voucherId": {"type": "integer", "description": "要使用的代金券id", "format": "int64"}}}, "VoucherVo": {"title": "VoucherVo", "type": "object", "properties": {"amount": {"type": "number", "format": "bigdecimal"}, "balance": {"type": "number", "description": "可用余额（不包含锁定余额）", "format": "bigdecimal"}, "cardNumber": {"type": "string"}, "expireDate": {"type": "string", "format": "date-time"}, "expired": {"type": "boolean", "description": "是否已经失效", "example": false}, "id": {"type": "integer", "format": "int64"}, "life": {"type": "integer", "description": "剩余可扣次数", "format": "int32"}, "lockBalance": {"type": "number", "description": "锁定余额", "format": "bigdecimal"}, "minOrderPrice": {"type": "number", "description": "最小可用金额", "format": "bigdecimal"}, "voucherType": {"type": "integer", "description": "see VoucherType.java", "format": "int32"}}}, "WebResult": {"title": "WebResult", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«CalcPriceResponse»": {"title": "WebResult«CalcPriceResponse»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/CalcPriceResponse"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«CostSummaryVo»": {"title": "WebResult«CostSummaryVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/CostSummaryVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«CreateOrderResponse»": {"title": "WebResult«CreateOrderResponse»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/CreateOrderResponse"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«GiftCardPackItemVo»": {"title": "WebResult«GiftCardPackItemVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/GiftCardPackItemVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«GoodsDto»": {"title": "WebResult«GoodsDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/GoodsDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«InvoiceSummaryVo»": {"title": "WebResult«InvoiceSummaryVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/InvoiceSummaryVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«DiscountsDto»»": {"title": "WebResult«List«DiscountsDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/DiscountsDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«NotInvoicedOrderVo»»": {"title": "WebResult«List«NotInvoicedOrderVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/NotInvoicedOrderVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«Map«long,List«DiscountsDto»»»": {"title": "WebResult«Map«long,List«DiscountsDto»»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/components/schemas/DiscountsDto"}}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«Map«long,List«OrderResourceVo»»»": {"title": "WebResult«Map«long,List«OrderResourceVo»»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/components/schemas/OrderResourceVo"}}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«Map«string,int»»": {"title": "WebResult«Map«string,int»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«OrderDetailVo»": {"title": "WebResult«OrderDetailVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/OrderDetailVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«OrdersDto»": {"title": "WebResult«OrdersDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/OrdersDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«BalanceInOutDetailVo»»": {"title": "WebResult«PageResult«BalanceInOutDetailVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«BalanceInOutDetailVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«CreditInOutDetailVo»»": {"title": "WebResult«PageResult«CreditInOutDetailVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«CreditInOutDetailVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«InvoiceHistoryVo»»": {"title": "WebResult«PageResult«InvoiceHistoryVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«InvoiceHistoryVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«OrderDetailVo»»": {"title": "WebResult«PageResult«OrderDetailVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«OrderDetailVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«VoucherVo»»": {"title": "WebResult«PageResult«VoucherVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«VoucherVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PaymentSummaryVo»": {"title": "WebResult«PaymentSummaryVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PaymentSummaryVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«VoucherVo»": {"title": "WebResult«VoucherVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/VoucherVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«bigdecimal»": {"title": "WebResult«bigdecimal»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "number", "format": "bigdecimal"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«int»": {"title": "WebResult«int»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«string»": {"title": "WebResult«string»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "string"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}}, "securitySchemes": {"Authorization": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}, "x-dm-team-id": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-dm-team-id", "in": "header"}}}}
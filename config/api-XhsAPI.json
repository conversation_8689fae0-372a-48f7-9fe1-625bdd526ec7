{"openapi": "3.0.3", "info": {"title": "Kakao API", "version": "App version 1.0,Spring Boot Version:2.7.14"}, "servers": [{"url": "http://dev.thinkoncloud.cn", "description": "Inferred Url"}], "tags": [{"name": "小红书账号API", "description": "Xhs Account Controller"}], "paths": {"/api/xhs/account/create": {"post": {"tags": ["XhsAccountController"], "summary": "创建账号", "operationId": "xhsAccountCreatePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddXhsAccountRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«XhsAccountDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/xhs/account/page": {"get": {"tags": ["XhsAccountController"], "summary": "分页查询", "operationId": "xhsAccountPageGet", "parameters": [{"name": "accountType", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["Assistant", "SocialMedia"]}}, {"name": "containsTag", "in": "query", "description": "包含指定标签。取false时，tagLc只能是OR", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "createTimeFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "createTimeTo", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "filterByFavorite", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "followCntFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "followCntTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "followerCntFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "followerCntTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "lastSyncTimeFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "lastSyncTimeTo", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "likeCntFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "likeCntTo", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "limit", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "onlineStatus", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["Offline", "Online"]}}, {"name": "pageNum", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "shopId", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "sortField", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "sortOrder", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}, {"name": "tagIds", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "tagLc", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["AND", "NOT", "OR"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«XhsAccountDetailVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/xhs/account/sync": {"put": {"tags": ["XhsAccountController"], "summary": "syncAccount", "operationId": "xhsAccountSyncPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SyncAccountRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/xhs/account/{id}": {"put": {"tags": ["XhsAccountController"], "summary": "修改账号", "operationId": "xhsAccountByIdPut", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddXhsAccountRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«XhsAccountDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "delete": {"tags": ["XhsAccountController"], "summary": "删除账号", "operationId": "xhsAccountByIdDelete", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}}, "components": {"schemas": {"AddXhsAccountRequest": {"title": "AddXhsAccountRequest", "type": "object", "properties": {"account": {"type": "string"}, "accountType": {"type": "string", "enum": ["Assistant", "SocialMedia"]}, "clientPlatform": {"type": "string"}, "description": {"type": "string"}, "name": {"type": "string"}, "password": {"type": "string"}}}, "PageResult«XhsAccountDetailVo»": {"title": "PageResult«XhsAccountDetailVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/XhsAccountDetailVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "SyncAccountRequest": {"title": "SyncAccountRequest", "type": "object", "properties": {"accountId": {"type": "integer", "format": "int64"}, "followCnt": {"type": "integer", "format": "int32"}, "followerCnt": {"type": "integer", "format": "int32"}, "likeCnt": {"type": "integer", "format": "int32"}, "onlineStatus": {"type": "string", "enum": ["Offline", "Online"]}, "tags": {"type": "array", "items": {"type": "string"}}}}, "TagDto": {"title": "TagDto", "type": "object", "properties": {"bizCode": {"type": "string"}, "color": {"type": "integer", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}, "expireTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "resourceType": {"type": "string", "enum": ["AK", "Activity", "Audit", "BlockElements", "Cloud", "CrsOrder", "CrsProduct", "DiskFile", "FingerPrint", "FingerPrintTemplate", "Gateway", "GhCreator", "GhGifter", "GhJobPlan", "GhUser", "GhVideoCreator", "GiftCardPack", "InsTeamUser", "InsUser", "Invoice", "Ip", "IpPool", "IppIp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "KakaoFriend", "KolCreator", "MobileAccount", "None", "Orders", "PluginTeamPack", "Record", "RpaFlow", "RpaTask", "RpaTaskItem", "RpaVoucher", "Shop", "ShopSession", "Tag", "TeamDiskRoot", "TeamMobile", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TkCreator", "TkTeamPack", "Tkshop", "<PERSON>ks<PERSON><PERSON>uyer", "TkshopCreator", "TrafficPack", "TunnelVps", "Users", "View", "Voucher", "XhsAccount"]}, "system": {"type": "boolean"}, "tag": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}}}, "WebResult": {"title": "WebResult", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«XhsAccountDetailVo»»": {"title": "WebResult«PageResult«XhsAccountDetailVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«XhsAccountDetailVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«XhsAccountDto»": {"title": "WebResult«XhsAccountDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/XhsAccountDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "XhsAccountDetailVo": {"title": "XhsAccountDetailVo", "type": "object", "properties": {"account": {"type": "string"}, "accountType": {"type": "string", "enum": ["Assistant", "SocialMedia"]}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "followCnt": {"type": "integer", "format": "int32"}, "followerCnt": {"type": "integer", "format": "int32"}, "id": {"type": "integer", "format": "int64"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "likeCnt": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "onlineStatus": {"type": "string", "enum": ["Offline", "Online"]}, "password": {"type": "string"}, "shopId": {"type": "integer", "format": "int64"}, "tags": {"type": "array", "description": "标签", "items": {"$ref": "#/components/schemas/TagDto"}}, "teamId": {"type": "integer", "format": "int64"}, "userId": {"type": "string"}}}, "XhsAccountDto": {"title": "XhsAccountDto", "type": "object", "properties": {"account": {"type": "string"}, "accountType": {"type": "string", "enum": ["Assistant", "SocialMedia"]}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "followCnt": {"type": "integer", "format": "int32"}, "followerCnt": {"type": "integer", "format": "int32"}, "id": {"type": "integer", "format": "int64"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "likeCnt": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "onlineStatus": {"type": "string", "enum": ["Offline", "Online"]}, "password": {"type": "string"}, "shopId": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}, "userId": {"type": "string"}}}}, "securitySchemes": {"Authorization": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}, "x-dm-team-id": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-dm-team-id", "in": "header"}}}}
{"openapi": "3.0.3", "info": {"title": "Remote API", "version": "App version 1.0,Spring Boot Version:2.7.14"}, "servers": [{"url": "http://dev.thinkoncloud.cn", "description": "Inferred Url"}], "tags": [{"name": "Remote ApiWorker Service", "description": "Remote Api Worker Service Impl"}, {"name": "Remote Discovery Service", "description": "Offline Service Impl"}, {"name": "Remote Proxy Service", "description": "Remote Tunnel Service Impl"}, {"name": "default-remote-messenger-service", "description": "Default Remote Messenger Service"}, {"name": "remote-ip-service-controller", "description": "Remote Ip Service Controller"}, {"name": "remote-meta-service-impl", "description": "Remote Meta Service Impl"}, {"name": "remote-ops-service-controller", "description": "Remote Ops Service Controller"}, {"name": "remote-qq-wry-service-controller", "description": "Remote QQ Wry Service Controller"}, {"name": "remote-scheduler-service-impl", "description": "Remote Scheduler Service Impl"}, {"name": "remote-shop-service-controller", "description": "Remote Shop Service Controller"}, {"name": "rpa-remote-service-controller", "description": "Rpa Remote Service Controller"}, {"name": "websocket-emitter-remote-service-controller", "description": "Websocket Emitter Remote Service Controller"}, {"name": "后台往前台推送事件相关的接口", "description": "Ajax Event Remote Service Controller"}, {"name": "微服务内省远程接口", "description": "Introspection Controller"}, {"name": "权限远程接口", "description": "Remote Privilege Service Impl"}, {"name": "账户信息远程调用", "description": "Remote Account Service Controller"}, {"name": "费用中心远程调用", "description": "Payment Remote Service Impl"}], "paths": {"/api/remote/ajax-event/publish": {"post": {"tags": ["AjaxEventRemoteServiceController"], "summary": "向前台推送一个事件", "operationId": "remoteAjaxEventPublishPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PublishAjaxEventRequest"}}}}, "responses": {"200": {"description": "OK"}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/messenger/reloadSmsProvider": {"get": {"tags": ["DefaultRemoteMessengerService"], "summary": "reloadSmsProvider", "operationId": "remoteMessengerReloadSmsProviderGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ReloadSmsProviderResultVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/introspection/git": {"get": {"tags": ["IntrospectionController"], "summary": "getGitInfo", "operationId": "remoteIntrospectionGitGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«GitInfoVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/introspection/info": {"get": {"tags": ["IntrospectionController"], "summary": "getInfo", "operationId": "remoteIntrospectionInfoGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«MicroServiceInfoVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/msg-center/notifyDwlPass": {"post": {"tags": ["PaymentRemoteServiceImpl"], "summary": "广播一个事件，通常是控制台调用来给门户的用户发送一条消息", "operationId": "remoteMsgCenterNotifyDwlPassPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DwlNotifyRequest"}}}}, "responses": {"200": {"description": "OK"}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/msg-center/notifyDwlReject": {"post": {"tags": ["PaymentRemoteServiceImpl"], "summary": "广播一个事件，通常是控制台调用来给门户的用户发送一条消息", "operationId": "remoteMsgCenterNotifyDwlRejectPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DwlNotifyRequest"}}}}, "responses": {"200": {"description": "OK"}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/msg-center/notifySysBroadcastChange": {"post": {"tags": ["PaymentRemoteServiceImpl"], "summary": "通知门户系统通知有改变", "operationId": "remoteMsgCenterNotifySysBroadcastChangePost", "responses": {"200": {"description": "OK"}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/discovery/dumpThread": {"get": {"tags": ["OfflineServiceImpl"], "summary": "打印堆栈", "operationId": "remoteDiscoveryDumpThreadGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/discovery/offline": {"get": {"tags": ["OfflineServiceImpl"], "summary": "下线当前微服务", "operationId": "remoteDiscoveryOfflineGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/discovery/online": {"get": {"tags": ["OfflineServiceImpl"], "summary": "上线当前微服务", "operationId": "remoteDiscoveryOnlineGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/discovery/spy": {"get": {"tags": ["OfflineServiceImpl"], "summary": "微服务Spy", "operationId": "remoteDiscoverySpyGet", "parameters": [{"name": "path", "in": "query", "description": "path", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "filter", "in": "query", "description": "filter", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "page", "in": "query", "description": "page", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "size", "in": "query", "description": "size", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«SpyVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/discovery/triggerEvent": {"get": {"tags": ["OfflineServiceImpl"], "summary": "触发事件", "operationId": "remoteDiscoveryTriggerEventGet", "parameters": [{"name": "event", "in": "query", "description": "event", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/discovery/updateWeight": {"get": {"tags": ["OfflineServiceImpl"], "summary": "更新权重", "operationId": "remoteDiscoveryUpdateWeightGet", "parameters": [{"name": "weight", "in": "query", "description": "weight", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«int»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/payment/checkProduced": {"post": {"tags": ["PaymentRemoteServiceImpl"], "summary": "重新生产某个未生产成功订单", "operationId": "remotePaymentCheckProducedPost", "parameters": [{"name": "orderId", "in": "query", "description": "orderId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/payment/checkRefundAble": {"get": {"tags": ["PaymentRemoteServiceImpl"], "summary": "检查某个订单是否可安全退款，如果不可安全退款则会返回非空值", "operationId": "remotePaymentCheckRefundAbleGet", "parameters": [{"name": "orderId", "in": "query", "description": "orderId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "string"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/payment/notifyBankPayFailed": {"post": {"tags": ["PaymentRemoteServiceImpl"], "summary": "通知银行支付订单打款失败", "operationId": "remotePaymentNotifyBankPayFailedPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotifyBankPayFailedRequest"}}}}, "responses": {"200": {"description": "OK"}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/payment/notifyBankPaySuccess": {"post": {"tags": ["PaymentRemoteServiceImpl"], "summary": "通知银行支付订单打款成功", "operationId": "remotePaymentNotifyBankPaySuccessPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotifyBankPaySuccessRequest"}}}}, "responses": {"200": {"description": "OK"}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/payment/notifyOfflineOrderCreated": {"post": {"tags": ["PaymentRemoteServiceImpl"], "summary": "通知一个offline订单创建了。主要用来给团队用户发订单待支付消息", "operationId": "remotePaymentNotifyOfflineOrderCreatedPost", "parameters": [{"name": "orderId", "in": "query", "description": "orderId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/payment/reProduceOrder": {"post": {"tags": ["PaymentRemoteServiceImpl"], "summary": "重新生产某个未生产成功订单", "operationId": "remotePaymentReProduceOrderPost", "parameters": [{"name": "orderId", "in": "query", "description": "orderId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/payment/refundOrder": {"post": {"tags": ["PaymentRemoteServiceImpl"], "summary": "订单退款", "operationId": "remotePaymentRefundOrderPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefundRequest"}}}}, "responses": {"200": {"description": "OK"}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/account/getPrincipalSigningKey": {"get": {"tags": ["RemoteAccountServiceController"], "summary": "获取主体的签名密钥", "operationId": "remoteAccountGetPrincipalSigningKeyGet", "parameters": [{"name": "principalType", "in": "query", "description": "principalType", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "principalId", "in": "query", "description": "principalId", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PrincipalSigningKeyResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/account/getPrivateKeyPem": {"get": {"tags": ["RemoteAccountServiceController"], "summary": "获取JWT的签名私钥（用producktKey加密）", "operationId": "remoteAccountGetPrivateKeyPemGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "string"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/account/getPublicKeyPem": {"get": {"tags": ["RemoteAccountServiceController"], "summary": "获取校验JWT的公钥", "operationId": "remoteAccountGetPublicKeyPemGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "string"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/account/getUserResource": {"get": {"tags": ["RemoteAccountServiceController"], "summary": "getUserResource", "operationId": "remoteAccountGetUserResourceGet", "parameters": [{"name": "teamId", "in": "query", "description": "teamId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "userId", "in": "query", "description": "userId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "resourceType", "in": "query", "description": "resourceType", "required": true, "style": "form", "schema": {"type": "string", "enum": ["AK", "Activity", "Audit", "BlockElements", "Cloud", "CrsOrder", "CrsProduct", "DiskFile", "FingerPrint", "FingerPrintTemplate", "Gateway", "GhCreator", "GhGifter", "GhJobPlan", "GhUser", "GhVideoCreator", "GiftCardPack", "InsTeamUser", "InsUser", "Invoice", "Ip", "IpPool", "IppIp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "KakaoFriend", "KolCreator", "MobileAccount", "None", "Orders", "PluginTeamPack", "Record", "RpaFlow", "RpaTask", "RpaTaskItem", "RpaVoucher", "Shop", "ShopSession", "Tag", "TeamDiskRoot", "TeamMobile", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TkCreator", "TkTeamPack", "Tkshop", "<PERSON>ks<PERSON><PERSON>uyer", "TkshopCreator", "TrafficPack", "TunnelVps", "Users", "View", "Voucher", "XhsAccount"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserResourceVo"}}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/account/userList": {"get": {"tags": ["RemoteAccountServiceController"], "summary": "userList", "operationId": "remoteAccountUserListGet", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserVo"}}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/account/{userId}/status": {"get": {"tags": ["RemoteAccountServiceController"], "summary": "通知禁用用户", "operationId": "remoteAccountByUserIdStatusGet", "parameters": [{"name": "userId", "in": "path", "description": "userId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "block", "in": "query", "description": "block", "required": true, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/apiWorker/{identifier}/ipp/checkParams": {"post": {"tags": ["RemoteApiWorkerServiceImpl"], "summary": "检查IP池的参数", "operationId": "remoteApiWorkerByIdentifierIppCheckParamsPost", "parameters": [{"name": "identifier", "in": "path", "description": "identifier", "required": true, "style": "simple", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProduceIpRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/apiWorker/{identifier}/ipp/prepareProduceIpSpec": {"post": {"tags": ["RemoteApiWorkerServiceImpl"], "summary": "让ip池生产调用规范", "operationId": "remoteApiWorkerByIdentifierIppPrepareProduceIpSpecPost", "parameters": [{"name": "identifier", "in": "path", "description": "identifier", "required": true, "style": "simple", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProduceIpRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ProduceIpSpecVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/apiWorker/{identifier}/ipp/produce": {"post": {"tags": ["RemoteApiWorkerServiceImpl"], "summary": "让ip池生成一批IP", "operationId": "remoteApiWorkerByIdentifierIppProducePost", "parameters": [{"name": "identifier", "in": "path", "description": "identifier", "required": true, "style": "simple", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProduceIpRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ProduceIpResultVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/ip/jdeipTriggerAllocation": {"get": {"tags": ["RemoteIpServiceController"], "summary": "jdeipTriggerAllocation", "operationId": "remoteIpJdeipTriggerAllocationGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/ip/probe": {"get": {"tags": ["RemoteIpServiceController"], "summary": "探测ip", "operationId": "remoteIpProbeGet", "parameters": [{"name": "ipId", "in": "query", "description": "ipId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "transitId", "in": "query", "description": "transitId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ProxyProbeWithRemoteIp»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/ip/probeLaunchInstanceBatch": {"post": {"tags": ["RemoteIpServiceController"], "summary": "批量探测", "operationId": "remoteIpProbeLaunchInstanceBatchPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonIdsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TaskDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/ip/switchBatchToProxy": {"post": {"tags": ["RemoteIpServiceController"], "summary": "把IPGO的IP切换成代理IP", "operationId": "remoteIpSwitchBatchToProxyPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonIdsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«SwitchBatchProxyResult»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/ip/switchToProxy": {"get": {"tags": ["RemoteIpServiceController"], "summary": "把IPGO的IP切换成代理IP", "operationId": "remoteIpSwitchToProxyGet", "parameters": [{"name": "ipId", "in": "query", "description": "ipId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«SwitchProxyResult»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/meta/ip/location": {"get": {"tags": ["RemoteMetaServiceImpl"], "summary": "getLocation", "operationId": "remoteMetaIpLocationGet", "parameters": [{"name": "ip", "in": "query", "description": "ip", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«IpLocationResultVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/meta/ip/locationProviderVersion": {"get": {"tags": ["RemoteMetaServiceImpl"], "summary": "getIpLocationProviderVersion", "operationId": "remoteMetaIpLocationProviderVersionGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«IpLocationResultVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/meta/ip/reloadLocationProvider": {"get": {"tags": ["RemoteMetaServiceImpl"], "summary": "reloadIpLocationProvider", "operationId": "remoteMetaIpReloadLocationProviderGet", "parameters": [{"name": "geoFile", "in": "query", "description": "geoFile", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "qqwryFile", "in": "query", "description": "qqwryFile", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«IpLocationResultVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/ops/fireOpsMessage": {"post": {"tags": ["RemoteOpsServiceController"], "summary": "发送运维消息", "description": "返回任务ID", "operationId": "remoteOpsFireOpsMessagePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FireOpsMessageRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«long»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/ops/pauseOpsMessage": {"get": {"tags": ["RemoteOpsServiceController"], "summary": "pauseOpsMessage", "operationId": "remoteOpsPauseOpsMessageGet", "parameters": [{"name": "pause", "in": "query", "description": "pause", "required": true, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/ops/triggerNetProbe": {"get": {"tags": ["RemoteOpsServiceController"], "summary": "triggerNetProbe", "operationId": "remoteOpsTriggerNetProbeGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/privilege/checkPrincipalPrivileges": {"get": {"tags": ["RemotePrivilegeServiceImpl"], "summary": "校验主体是否具有特定权限", "operationId": "remotePrivilegeCheckPrincipalPrivilegesGet", "parameters": [{"name": "principalType", "in": "query", "description": "主体类型", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "principalId", "in": "query", "description": "主体ID", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "scopeId", "in": "query", "description": "操作范围ID", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "condition", "in": "query", "description": "权限连接条件,AND|OR", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "codes", "in": "query", "description": "权限代码，用逗号连接", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "roles", "in": "query", "description": "角色代码，用逗号连接。权限和角色代码必须设置一项", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RemoteApiResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/privilege/metas": {"get": {"tags": ["RemotePrivilegeServiceImpl"], "summary": "获取权限元数据", "operationId": "remotePrivilegeMetasGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccessControlMeta"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/qqwry/findIp": {"get": {"tags": ["RemoteQQWryServiceController"], "summary": "findIp", "operationId": "remoteQqwryFindIpGet", "parameters": [{"name": "ip", "in": "query", "description": "ip", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«QQWryInfo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/qqwry/getVersionInfo": {"get": {"tags": ["RemoteQQWryServiceController"], "summary": "getVersionInfo", "operationId": "remoteQqwryGetVersionInfoGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«FileDbInfo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/qqwry/reload": {"get": {"tags": ["RemoteQQWryServiceController"], "summary": "reload", "operationId": "remoteQqwryReloadGet", "parameters": [{"name": "fullPath", "in": "query", "description": "fullPath", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«FileDbInfo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/job/cancelTask": {"post": {"tags": ["RemoteSchedulerServiceImpl"], "summary": "cancelTask", "operationId": "remoteJobCancelTaskPost", "parameters": [{"name": "taskId", "in": "query", "description": "taskId", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«boolean»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/job/existsTask": {"post": {"tags": ["RemoteSchedulerServiceImpl"], "summary": "existsTask", "operationId": "remoteJobExistsTaskPost", "parameters": [{"name": "taskId", "in": "query", "description": "taskId", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«boolean»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/job/getNextFireTime": {"get": {"tags": ["RemoteSchedulerServiceImpl"], "summary": "getNextFireTime", "operationId": "remoteJobGetNextFireTimeGet", "parameters": [{"name": "taskId", "in": "query", "description": "taskId", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«long»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/job/reScheduleOneShot": {"post": {"tags": ["RemoteSchedulerServiceImpl"], "summary": "scheduleOrReplace", "operationId": "remoteJobReScheduleOneShotPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReScheduleOneShotRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/job/scheduleCron": {"post": {"tags": ["RemoteSchedulerServiceImpl"], "summary": "schedule", "operationId": "remoteJobScheduleCronPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScheduleCronRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/job/scheduleOneShot": {"post": {"tags": ["RemoteSchedulerServiceImpl"], "summary": "schedule", "operationId": "remoteJobScheduleOneShotPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScheduleOneShotRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/job/scheduleWithFixedDelay": {"post": {"tags": ["RemoteSchedulerServiceImpl"], "summary": "scheduleWithFixedDelay", "operationId": "remoteJobScheduleWithFixedDelayPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScheduleWithFixedDelayVo"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/shop/findShopWithPlatform": {"get": {"tags": ["RemoteShopServiceController"], "summary": "findShopWithPlatform", "operationId": "remoteShopFindShopWithPlatformGet", "parameters": [{"name": "teamId", "in": "query", "description": "teamId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "shopId", "in": "query", "description": "shopId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShopWithPlatformVo"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/shop/listByTeamId": {"get": {"tags": ["RemoteShopServiceController"], "summary": "listByTeamId", "operationId": "remoteShopListByTeamIdGet", "parameters": [{"name": "teamId", "in": "query", "description": "teamId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ShopDto"}}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/shop/listByUserId": {"get": {"tags": ["RemoteShopServiceController"], "summary": "getGrantedByUserId", "operationId": "remoteShopListByUserIdGet", "parameters": [{"name": "teamId", "in": "query", "description": "teamId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "userId", "in": "query", "description": "userId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ShopDto"}}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/shop/listIdByTeamId": {"get": {"tags": ["RemoteShopServiceController"], "summary": "listIdByTeamId", "operationId": "remoteShopListIdByTeamIdGet", "parameters": [{"name": "teamId", "in": "query", "description": "teamId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/shop/reloadI18ns": {"get": {"tags": ["RemoteShopServiceController"], "summary": "reloadI18ns", "operationId": "remoteShopReloadI18nsGet", "parameters": [{"name": "code", "in": "query", "description": "code", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "languages", "in": "query", "description": "languages", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/shop/reloadSystemProxy": {"get": {"tags": ["RemoteShopServiceController"], "summary": "reloadSystemProxy", "operationId": "remoteShopReloadSystemProxyGet", "parameters": [{"name": "scene", "in": "query", "description": "scene", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/shop/resetIpLocation": {"get": {"tags": ["RemoteShopServiceController"], "summary": "resetIpLocation", "operationId": "remoteShopResetIpLocationGet", "parameters": [{"name": "ipId", "in": "query", "description": "ipId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ResetIpLocationResultVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/shop/shopSessionToken/{sessionId}": {"get": {"tags": ["RemoteShopServiceController"], "summary": "getShopSessionToken", "operationId": "remoteShopShopSessionTokenBySessionIdGet", "parameters": [{"name": "sessionId", "in": "path", "description": "sessionId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "tunnelDirect", "in": "query", "description": "是否启用IPGO直连", "required": false, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ShopTokenVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/shop/triggerTransitOff": {"get": {"tags": ["RemoteShopServiceController"], "summary": "triggerTransitOff", "operationId": "remoteShopTriggerTransitOffGet", "parameters": [{"name": "transitIds", "in": "query", "description": "transitIds", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/shop/triggerTunnelOff": {"get": {"tags": ["RemoteShopServiceController"], "summary": "triggerTunnel<PERSON>ff", "operationId": "remoteShopTriggerTunnelOffGet", "parameters": [{"name": "nodeIds", "in": "query", "description": "nodeIds", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/shop/triggerUploadLogs": {"get": {"tags": ["RemoteShopServiceController"], "summary": "triggerUploadLogs", "operationId": "remoteShopTriggerUploadLogsGet", "parameters": [{"name": "deviceId", "in": "query", "description": "deviceId", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/ops/table/cleanColdTable": {"post": {"tags": ["RemoteAccountServiceController"], "summary": "清理冷库", "operationId": "remoteOpsTableCleanColdTablePost", "parameters": [{"name": "table", "in": "query", "description": "table", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "day", "in": "query", "description": "day", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TaskDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/ops/table/kolSync": {"put": {"tags": ["RemoteAccountServiceController"], "summary": "kolSync", "operationId": "remoteOpsTableKolSyncPut", "parameters": [{"name": "teamId", "in": "query", "description": "teamId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "syncLive", "in": "query", "description": "syncLive", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "id", "in": "query", "description": "id", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/ops/table/kolSyncAll": {"put": {"tags": ["RemoteAccountServiceController"], "summary": "kolSyncAll", "operationId": "remoteOpsTableKolSyncAllPut", "parameters": [{"name": "syncLive", "in": "query", "description": "syncLive", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "id", "in": "query", "description": "id", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/ops/table/kolSyncStop": {"put": {"tags": ["RemoteAccountServiceController"], "summary": "kolSyncStop", "operationId": "remoteOpsTableKolSyncStopPut", "parameters": [{"name": "teamId", "in": "query", "description": "teamId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/ops/table/shard": {"post": {"tags": ["RemoteAccountServiceController"], "summary": "对特定团队的特定数据进行分表", "operationId": "remoteOpsTableShardPost", "parameters": [{"name": "table", "in": "query", "description": "table", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "fromSuffix", "in": "query", "description": "fromSuffix", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "toSuffix", "in": "query", "description": "toSuffix", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "teamId", "in": "query", "description": "teamId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TaskDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/ops/table/sharding": {"put": {"tags": ["RemoteAccountServiceController"], "summary": "updateSharding", "operationId": "remoteOpsTableShardingPut", "parameters": [{"name": "run", "in": "query", "description": "run", "required": true, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/ops/table/teamTypeChangedV2": {"put": {"tags": ["RemoteAccountServiceController"], "summary": "团队类型改变V2", "operationId": "remoteOpsTableTeamTypeChangedV2Put", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TeamTypeChangedRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/ops/table/transfer": {"post": {"tags": ["RemoteAccountServiceController"], "summary": "迁移表到冷库", "operationId": "remoteOpsTableTransferPost", "parameters": [{"name": "table", "in": "query", "description": "table", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "day", "in": "query", "description": "day", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "query", "in": "query", "description": "query", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TaskDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/ops/table/triggerKolAllocation": {"put": {"tags": ["RemoteAccountServiceController"], "summary": "triggerKolAllocation", "operationId": "remoteOpsTableTriggerKolAllocationPut", "parameters": [{"name": "teamId", "in": "query", "description": "teamId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/ops/table/updateTeamStatus": {"get": {"tags": ["RemoteAccountServiceController"], "summary": "团队类型改变", "operationId": "remoteOpsTableUpdateTeamStatusGet", "parameters": [{"name": "teamId", "in": "query", "description": "teamId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "status", "in": "query", "description": "status", "required": true, "style": "form", "schema": {"type": "string", "enum": ["Blocked", "Deleted", "Pending", "Ready"]}}, {"name": "validEndTimeStamp", "in": "query", "description": "validEndTimeStamp", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/tunnel/address": {"get": {"tags": ["RemoteTunnelServiceImpl"], "summary": "当前服务tunnel地址", "operationId": "remoteTunnelAddressGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TunnelAddress»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/tunnel/blockProxy": {"get": {"tags": ["RemoteTunnelServiceImpl"], "summary": "阻塞Proxy事件线程（用于测试）", "operationId": "remoteTunnelBlockProxyGet", "parameters": [{"name": "identifier", "in": "query", "description": "identifier", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "seconds", "in": "query", "description": "seconds", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/tunnel/connectedPeers": {"get": {"tags": ["RemoteTunnelServiceImpl"], "summary": "获取目标端的已经连接的端", "operationId": "remoteTunnelConnectedPeersGet", "parameters": [{"name": "target", "in": "query", "description": "target", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RpcConnectedPeers»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/tunnel/createProxyPendingToken": {"get": {"tags": ["RemoteTunnelServiceImpl"], "summary": "获取一个等待启动的IPGO的Token", "operationId": "remoteTunnelCreateProxyPendingTokenGet", "parameters": [{"name": "teamId", "in": "query", "description": "teamId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "creatorId", "in": "query", "description": "creatorId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "provider", "in": "query", "description": "provider", "required": true, "style": "form", "schema": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "aws", "aws_cn", "aws_ls", "azure", "azure_cn", "baidu", "bao<PERSON><PERSON>", "bluevps", "dmit", "ecloud10086", "googlecloud", "hua<PERSON>", "huayang", "huo<PERSON>", "jdbox", "jdcloud", "jdeip", "lan", "oracle", "other", "qcloud", "raincloud", "ucloud", "vlcloud", "vps", "yge<PERSON>"]}}, {"name": "region", "in": "query", "description": "region", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "expireSeconds", "in": "query", "description": "expireSeconds", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "orderItemId", "in": "query", "description": "orderItemId", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ProxyTokenVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/tunnel/dumpThread": {"get": {"tags": ["RemoteTunnelServiceImpl"], "summary": "获取目标端的线程信息", "operationId": "remoteTunnelDumpThreadGet", "parameters": [{"name": "target", "in": "query", "description": "target", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/tunnel/exitProxy": {"get": {"tags": ["RemoteTunnelServiceImpl"], "summary": "通知Proxy停止（用于测试）", "operationId": "remoteTunnelExitProxyGet", "parameters": [{"name": "identifier", "in": "query", "description": "identifier", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "exitCode", "in": "query", "description": "exitCode", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/tunnel/gatewayEndpoint": {"get": {"tags": ["RemoteTunnelServiceImpl"], "summary": "获取网关升级网址前缀", "operationId": "remoteTunnelGatewayEndpointGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/tunnel/gitInfo": {"get": {"tags": ["RemoteTunnelServiceImpl"], "summary": "获取目标端的GIT信息", "operationId": "remoteTunnelGitInfoGet", "parameters": [{"name": "target", "in": "query", "description": "target", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«GitInfoVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/tunnel/gitSpy": {"get": {"tags": ["RemoteTunnelServiceImpl"], "summary": "获取目标端的Spy信息", "operationId": "remoteTunnelGitSpyGet", "parameters": [{"name": "target", "in": "query", "description": "target", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "path", "in": "query", "description": "path", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "filter", "in": "query", "description": "filter", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "page", "in": "query", "description": "page", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "size", "in": "query", "description": "size", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«SpyVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/tunnel/probeRemoteIp": {"post": {"tags": ["RemoteTunnelServiceImpl"], "summary": "探测remoteIp", "operationId": "remoteTunnelProbeRemoteIpPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RemoteProbeRemoteIpRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ProxyProbeWithRemoteIp»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/tunnel/proxy/changePassword": {"put": {"tags": ["RemoteTunnelServiceImpl"], "summary": "IPGO修改特定用户的登录秘密", "operationId": "remoteTunnelProxyChangePasswordPut", "parameters": [{"name": "identifier", "in": "query", "description": "identifier", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "user", "in": "query", "description": "user", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "password", "in": "query", "description": "password", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/tunnel/proxy/changePwdLogin": {"put": {"tags": ["RemoteTunnelServiceImpl"], "summary": "IPGO修改Linux系统是否允许秘密登录", "operationId": "remoteTunnelProxyChangePwdLoginPut", "parameters": [{"name": "identifier", "in": "query", "description": "identifier", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "enabled", "in": "query", "description": "enabled", "required": true, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/tunnel/proxy/connectTransit": {"post": {"tags": ["RemoteTunnelServiceImpl"], "summary": "触发Proxy连接最快中转2", "description": "从特定分组中连接，而不是所有", "operationId": "remoteTunnelProxyConnectTransitPost", "parameters": [{"name": "identifier", "in": "query", "description": "identifier", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "forceReconnect", "in": "query", "description": "forceReconnect", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "transitId", "in": "query", "description": "transitId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "jumpIfNeed", "in": "query", "description": "jumpIfNeed", "required": false, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/tunnel/proxy/forceReportIp": {"post": {"tags": ["RemoteTunnelServiceImpl"], "summary": "触发Proxy一次IP汇报", "operationId": "remoteTunnelProxyForceReportIpPost", "parameters": [{"name": "identifier", "in": "query", "description": "identifier", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/tunnel/proxy/forceUpdate": {"post": {"tags": ["RemoteTunnelServiceImpl"], "summary": "强制更新", "operationId": "remoteTunnelProxyForceUpdatePost", "parameters": [{"name": "identifier", "in": "query", "description": "identifier", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/tunnel/proxy/getTimestamp": {"post": {"tags": ["RemoteTunnelServiceImpl"], "summary": "获取Proxy时间戳", "operationId": "remoteTunnelProxyGetTimestampPost", "parameters": [{"name": "identifier", "in": "query", "description": "identifier", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«long»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/tunnel/proxy/pingFasttestAndConnectTransit": {"post": {"tags": ["RemoteTunnelServiceImpl"], "summary": "触发Proxy连接最快中转", "operationId": "remoteTunnelProxyPingFasttestAndConnectTransitPost", "parameters": [{"name": "identifier", "in": "query", "description": "identifier", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "forceReconnect", "in": "query", "description": "forceReconnect", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "domestic", "in": "query", "description": "domestic", "required": false, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TransitConnectionInfo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/tunnel/proxy/pingFasttestAndConnectTransit2": {"post": {"tags": ["RemoteTunnelServiceImpl"], "summary": "触发Proxy连接最快中转2", "description": "从特定分组中连接，而不是所有", "operationId": "remoteTunnelProxyPingFasttestAndConnectTransit2Post", "parameters": [{"name": "identifier", "in": "query", "description": "identifier", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "forceReconnect", "in": "query", "description": "forceReconnect", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "transitIds", "in": "query", "description": "transitIds", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TransitConnectionInfo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/tunnel/proxy/pingFasttestAndConnectTransitById": {"post": {"tags": ["RemoteTunnelServiceImpl"], "summary": "触发Proxy连接最快中转", "operationId": "remoteTunnelProxyPingFasttestAndConnectTransitByIdPost", "parameters": [{"name": "proxyId", "in": "query", "description": "proxyId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "forceReconnect", "in": "query", "description": "forceReconnect", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "domestic", "in": "query", "description": "domestic", "required": false, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TransitConnectionInfo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/tunnel/proxy/probeTcp": {"post": {"tags": ["RemoteTunnelServiceImpl"], "summary": "通过中转（或门户）探测代理", "operationId": "remoteTunnelProxyProbeTcpPost", "parameters": [{"name": "identifier", "in": "query", "description": "identifier", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "host", "in": "query", "description": "host", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "port", "in": "query", "description": "port", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ProbeResult»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/tunnel/proxy/probeToTransit": {"post": {"tags": ["RemoteTunnelServiceImpl"], "summary": "通过Proxy探测中转连接性", "operationId": "remoteTunnelProxyProbeToTransitPost", "parameters": [{"name": "identifier", "in": "query", "description": "identifier", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "localIp", "in": "query", "description": "localIp", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "transitId", "in": "query", "description": "transitId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ProxyProbeWithRemoteIp»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/tunnel/proxy/runCmd": {"post": {"tags": ["RemoteTunnelServiceImpl"], "summary": "获取Proxy时间戳", "operationId": "remoteTunnelProxyRunCmdPost", "parameters": [{"name": "identifier", "in": "query", "description": "identifier", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "cmds", "in": "query", "description": "cmds", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "timeout", "in": "query", "description": "timeout", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "charset", "in": "query", "description": "charset", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ExecResult»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/tunnel/proxy/startDf2Server": {"post": {"tags": ["RemoteTunnelServiceImpl"], "summary": "启动一个DF2协议的Server", "operationId": "remoteTunnelProxyStartDf2ServerPost", "parameters": [{"name": "identifier", "in": "query", "description": "identifier", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "description": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "bindPort", "in": "query", "description": "bindPort", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "contextPath", "in": "query", "description": "contextPath", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "ignoreLocalBindAddress", "in": "query", "description": "ignoreLocalBindAddress", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "sign<PERSON><PERSON>", "in": "query", "description": "sign<PERSON><PERSON>", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "https", "in": "query", "description": "https", "required": false, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/tunnel/proxy/startProxyForwardServer": {"post": {"tags": ["RemoteTunnelServiceImpl"], "summary": "启动一个代理服务", "operationId": "remoteTunnelProxyStartProxyForwardServerPost", "parameters": [{"name": "identifier", "in": "query", "description": "identifier", "required": true, "style": "form", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StartProxyForwardVo"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«StartProxyForwardResult»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/tunnel/proxy/startSocks5Server": {"post": {"tags": ["RemoteTunnelServiceImpl"], "summary": "启动一个Raw Socks5 server", "operationId": "remoteTunnelProxyStartSocks5ServerPost", "parameters": [{"name": "identifier", "in": "query", "description": "identifier", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "description": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "bindPort", "in": "query", "description": "bindPort", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "auth", "in": "query", "description": "auth", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "username", "in": "query", "description": "username", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "password", "in": "query", "description": "password", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/tunnel/proxy/stopBindServer": {"post": {"tags": ["RemoteTunnelServiceImpl"], "summary": "停止一个Tcp Server监听", "operationId": "remoteTunnelProxyStopBindServerPost", "parameters": [{"name": "identifier", "in": "query", "description": "identifier", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "description": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "bindPort", "in": "query", "description": "bindPort", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/tunnel/proxy/stopProxyForwardServers": {"post": {"tags": ["RemoteTunnelServiceImpl"], "summary": "停止代理服务", "operationId": "remoteTunnelProxyStopProxyForwardServersPost", "parameters": [{"name": "identifier", "in": "query", "description": "identifier", "required": true, "style": "form", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StopProxyForwardVo"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/tunnel/proxy/storeSshKey": {"put": {"tags": ["RemoteTunnelServiceImpl"], "summary": "IPGO替换用户的登录ssh公钥", "operationId": "remoteTunnelProxyStoreSshKeyPut", "parameters": [{"name": "identifier", "in": "query", "description": "identifier", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "user", "in": "query", "description": "user", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "ssh<PERSON>ey", "in": "query", "description": "ssh<PERSON>ey", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/tunnel/proxy/updateProxyForwardServerAuth": {"post": {"tags": ["RemoteTunnelServiceImpl"], "summary": "修改代理服务", "operationId": "remoteTunnelProxyUpdateProxyForwardServerAuthPost", "parameters": [{"name": "identifier", "in": "query", "description": "identifier", "required": true, "style": "form", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProxyForwardAuth"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/tunnel/proxy/userData": {"get": {"tags": ["RemoteTunnelServiceImpl"], "summary": "获取Proxy主机启动脚本userdata", "operationId": "remoteTunnelProxyUserDataGet", "parameters": [{"name": "ipgoScriptType", "in": "query", "description": "ipgoScriptType", "required": true, "style": "form", "schema": {"type": "string", "enum": ["initialize", "install"]}}, {"name": "provider", "in": "query", "description": "provider", "required": true, "style": "form", "schema": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "aws", "aws_cn", "aws_ls", "azure", "azure_cn", "baidu", "bao<PERSON><PERSON>", "bluevps", "dmit", "ecloud10086", "googlecloud", "hua<PERSON>", "huayang", "huo<PERSON>", "jdbox", "jdcloud", "jdeip", "lan", "oracle", "other", "qcloud", "raincloud", "ucloud", "vlcloud", "vps", "yge<PERSON>"]}}, {"name": "regionId", "in": "query", "description": "regionId", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "tcpFp", "in": "query", "description": "tcpFp", "required": true, "style": "form", "schema": {"type": "boolean"}}, {"name": "platform", "in": "query", "description": "platform", "required": true, "style": "form", "schema": {"type": "string", "enum": ["android", "linux", "macos", "unknown", "web", "windows", "windows7"]}}, {"name": "teamId", "in": "query", "description": "teamId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "creatorId", "in": "query", "description": "creatorId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "base64", "in": "query", "description": "base64", "required": true, "style": "form", "schema": {"type": "boolean"}}, {"name": "orderItemId", "in": "query", "description": "orderItemId", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/tunnel/restartProxy": {"get": {"tags": ["RemoteTunnelServiceImpl"], "summary": "重启proxy（用于测试）", "operationId": "remoteTunnelRestartProxyGet", "parameters": [{"name": "identifier", "in": "query", "description": "identifier", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/tunnel/startTunnelTrace": {"get": {"tags": ["RemoteTunnelServiceImpl"], "summary": "开启TunnelTrace", "operationId": "remoteTunnelStartTunnelTraceGet", "parameters": [{"name": "target", "in": "query", "description": "target", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "module", "in": "query", "description": "module", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "minites", "in": "query", "description": "minites", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«GitInfoVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/tunnel/stopProxy": {"get": {"tags": ["RemoteTunnelServiceImpl"], "summary": "通知Proxy停止（之后可以手工启动）", "operationId": "remoteTunnelStopProxyGet", "parameters": [{"name": "identifier", "in": "query", "description": "identifier", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/tunnel/stopTunnelTrace": {"get": {"tags": ["RemoteTunnelServiceImpl"], "summary": "关闭TunnelTrace", "operationId": "remoteTunnelStopTunnelTraceGet", "parameters": [{"name": "target", "in": "query", "description": "target", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "module", "in": "query", "description": "module", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«GitInfoVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/tunnel/systemInfo": {"get": {"tags": ["RemoteTunnelServiceImpl"], "summary": "获取目标端的系统信息", "operationId": "remoteTunnelSystemInfoGet", "parameters": [{"name": "target", "in": "query", "description": "target", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«Map»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/tunnel/template": {"post": {"tags": ["RemoteTunnelServiceImpl"], "summary": "创建一个批量导入Key", "operationId": "remoteTunnelTemplatePost", "parameters": [{"name": "teamId", "in": "query", "description": "teamId", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ProxyTemplateVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/tunnel/template/getOrCreate": {"get": {"tags": ["RemoteTunnelServiceImpl"], "summary": "获取一个有效的批量导入Key（如果不存在则创建）", "operationId": "remoteTunnelTemplateGetOrCreateGet", "parameters": [{"name": "teamId", "in": "query", "description": "teamId", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ProxyTemplateVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/tunnel/templates": {"get": {"tags": ["RemoteTunnelServiceImpl"], "summary": "创建一个批量导入Key模版", "operationId": "remoteTunnelTemplatesGet", "parameters": [{"name": "teamId", "in": "query", "description": "teamId", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«GatewayTemplateDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/tunnel/transit/probeProxy": {"post": {"tags": ["RemoteTunnelServiceImpl"], "summary": "通过中转（或门户）探测代理", "operationId": "remoteTunnelTransitProbeProxyPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProbeProxyRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/tunnel/transit/probeTcp": {"post": {"tags": ["RemoteTunnelServiceImpl"], "summary": "通过中转（或门户）探测代理", "operationId": "remoteTunnelTransitProbeTcpPost", "parameters": [{"name": "transitId", "in": "query", "description": "transitId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "host", "in": "query", "description": "host", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "port", "in": "query", "description": "port", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ProbeResult»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/tunnel/tunnel/disconnectToTarget": {"post": {"tags": ["RemoteTunnelServiceImpl"], "summary": "通知tunnel端断开到特定目标的连接", "operationId": "remoteTunnelTunnelDisconnectToTargetPost", "parameters": [{"name": "source", "in": "query", "description": "source", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "target", "in": "query", "description": "target", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ForceDisconnectResult»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/tunnel/tunnel/disconnectToTargetByType": {"post": {"tags": ["RemoteTunnelServiceImpl"], "summary": "通知tunnel端断开到所有特定类型目标的连接", "operationId": "remoteTunnelTunnelDisconnectToTargetByTypePost", "parameters": [{"name": "source", "in": "query", "description": "source", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "type", "in": "query", "description": "type", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ForceDisconnectResult»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/tunnel/tunnel/routerL1": {"get": {"tags": ["RemoteTunnelServiceImpl"], "summary": "获取Tunnel一级路由", "operationId": "remoteTunnelTunnelRouterL1Get", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«Map«string,string»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/tunnel/tunnel/routerL2": {"get": {"tags": ["RemoteTunnelServiceImpl"], "summary": "获取Tunnel二级路由", "operationId": "remoteTunnelTunnelRouterL2Get", "parameters": [{"name": "target", "in": "query", "description": "target", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«TunnelAddress»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/tunnel/tunnelConnStat": {"get": {"tags": ["RemoteTunnelServiceImpl"], "summary": "获取目标端的连接统计信息", "operationId": "remoteTunnelTunnelConnStatGet", "parameters": [{"name": "target", "in": "query", "description": "target", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TunnelConnStat»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/tunnel/uninstallProxy": {"get": {"tags": ["RemoteTunnelServiceImpl"], "summary": "通知Proxy卸载（删除cred文件并停止）", "operationId": "remoteTunnelUninstallProxyGet", "parameters": [{"name": "identifier", "in": "query", "description": "identifier", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/rpa/triggerRpaCloudWorkerCalc": {"get": {"tags": ["RpaRemoteServiceController"], "summary": "triggerRpaCloudWorkerCalc", "operationId": "remoteRpaTriggerRpaCloudWorkerCalcGet", "parameters": [{"name": "groupKey", "in": "query", "description": "groupKey", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/remote/ws/emitAppEvent": {"post": {"tags": ["WebsocketEmitterRemoteServiceController"], "summary": "emitAppEvent", "operationId": "remoteWsEmitAppEventPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmitAppEventRequest"}}}}, "responses": {"200": {"description": "OK"}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}}, "components": {"schemas": {"AccessControlMeta": {"title": "AccessControlMeta", "type": "object", "properties": {"endpointList": {"type": "array", "items": {"$ref": "#/components/schemas/RequestMeta"}}, "privilegeList": {"type": "array", "items": {"$ref": "#/components/schemas/PrivilegeMeta"}}, "publicAccessList": {"type": "array", "items": {"$ref": "#/components/schemas/RequestMeta"}}, "rateLimiterList": {"type": "array", "items": {"$ref": "#/components/schemas/RateLimiterMeta"}}, "serviceId": {"type": "string"}, "webhookTokens": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}}}, "CommonIdsRequest": {"title": "CommonIdsRequest", "type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "DwlNotifyRequest": {"title": "DwlNotifyRequest", "type": "object", "properties": {"auditId": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}}}, "DynamicForwardStat": {"title": "DynamicForwardStat", "type": "object", "properties": {"pipes": {"type": "integer", "format": "int32"}, "sessions": {"type": "integer", "format": "int32"}}}, "EmitAppEventRequest": {"title": "EmitAppEventRequest", "type": "object", "properties": {"data": {"type": "object"}, "deviceId": {"type": "string"}, "eventName": {"type": "string"}}}, "EndpointVo": {"title": "EndpointVo", "type": "object", "properties": {"bgpPro": {"type": "boolean"}, "direct": {"type": "boolean"}, "endpoint": {"type": "string"}, "groups": {"type": "array", "items": {"$ref": "#/components/schemas/TransitGroupDto"}}, "ipEndpoint": {"type": "string"}, "ipEndpointEnabled": {"type": "boolean"}, "load": {"type": "integer", "format": "int32"}, "probeCode": {"type": "integer", "format": "int32"}, "probeError": {"type": "string"}, "specialTrafficPrice": {"type": "integer", "format": "int32"}, "status": {"type": "string", "enum": ["Available", "Pending", "Unavailable"]}, "testingTime": {"type": "integer", "description": "接入点到IP测试时间，为-1表示未进行过测试或测试不可用", "format": "int32"}, "trafficPrice": {"type": "integer", "format": "int32"}, "transitId": {"type": "integer", "format": "int64"}, "transitIpv6": {"type": "boolean"}, "transitName": {"type": "string"}}}, "ExecResult": {"title": "ExecResult", "type": "object", "properties": {"cmd": {"type": "array", "items": {"type": "string"}}, "error": {"type": "string"}, "exitCode": {"type": "integer", "format": "int32"}, "out": {"type": "string"}}}, "FieldInfo": {"title": "FieldInfo", "type": "object", "properties": {"hashCode": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "path": {"type": "string"}, "printable": {"type": "boolean"}, "size": {"type": "integer", "format": "int32"}, "valueClass": {"type": "string"}, "valueString": {"type": "string"}}}, "FieldsInfo": {"title": "FieldsInfo", "type": "object", "properties": {"clazz": {"type": "string"}, "fields": {"type": "array", "items": {"$ref": "#/components/schemas/FieldInfo"}}}}, "FileDbInfo": {"title": "FileDbInfo", "type": "object", "properties": {"file": {"type": "string"}, "lastModified": {"type": "string", "format": "date-time"}, "version": {"type": "string"}}}, "FireOpsMessageRequest": {"title": "FireOpsMessageRequest", "type": "object", "properties": {"allTeam": {"type": "boolean", "description": "全部团队还是指定团队", "example": false}, "consoleUserId": {"type": "integer", "format": "int64"}, "content": {"type": "string", "description": "消息内容"}, "readOnce": {"type": "boolean", "description": "用户单次读", "example": false}, "roleCodes": {"type": "array", "description": "是否指定接收的角色", "items": {"type": "string", "enum": ["boss", "manager", "staff", "superadmin"]}}, "teamIds": {"type": "array", "description": "指定团队", "items": {"type": "integer", "format": "int64"}}, "title": {"type": "string", "description": "消息标题"}}}, "ForceDisconnectResult": {"title": "ForceDisconnectResult", "type": "object", "properties": {"disconnectedAddresses": {"type": "array", "items": {"$ref": "#/components/schemas/TunnelAddress"}}}}, "GatewayTemplateDto": {"title": "GatewayTemplateDto", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "identifier": {"type": "string"}, "importType": {"type": "string", "enum": ["Platform", "User"]}, "secretKey": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}, "token": {"type": "string"}, "valid": {"type": "boolean"}}}, "GitInfoVo": {"title": "GitInfoVo", "type": "object", "properties": {"branch": {"type": "string"}, "buildNumber": {"type": "string"}, "buildTime": {"type": "string", "format": "date-time"}, "buildVersion": {"type": "string"}, "commitIdAbbrev": {"type": "string"}, "commitIdFull": {"type": "string"}, "commitTime": {"type": "string", "format": "date-time"}}}, "HealthStatus": {"title": "HealthStatus", "type": "object", "properties": {"code": {"type": "string"}, "description": {"type": "string"}}}, "IpLocationDto": {"title": "IpLocationDto", "type": "object", "properties": {"city": {"type": "string"}, "cityEn": {"type": "string"}, "continent": {"type": "string"}, "continentCode": {"type": "string"}, "continentEn": {"type": "string"}, "country": {"type": "string"}, "countryCode": {"type": "string"}, "countryEn": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "geonameId": {"type": "integer", "format": "int32"}, "id": {"type": "integer", "format": "int64"}, "inEu": {"type": "boolean"}, "latitude": {"type": "number", "format": "double"}, "level": {"type": "string", "enum": ["City", "Continent", "Country", "District", "None", "Province", "Unknown"]}, "locale": {"type": "string"}, "longitude": {"type": "number", "format": "double"}, "postalCode": {"type": "string"}, "province": {"type": "string"}, "provinceCode": {"type": "string"}, "provinceEn": {"type": "string"}, "show": {"type": "boolean"}, "teamId": {"type": "integer", "format": "int64"}, "timezone": {"type": "string"}}}, "IpLocationResultVo": {"title": "IpLocationResultVo", "type": "object", "properties": {"currentProduct": {"$ref": "#/components/schemas/ProductInfo"}, "result": {"type": "object"}}}, "IpSocksDto": {"title": "IpSocksDto", "type": "object", "properties": {"authEnabled": {"type": "boolean"}, "creator": {"type": "integer", "format": "int64"}, "host": {"type": "string"}, "hostDomestic": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "ipId": {"type": "integer", "format": "int64"}, "ipv6": {"type": "boolean"}, "locationId": {"type": "integer", "format": "int64"}, "password": {"type": "string"}, "port": {"type": "integer", "format": "int32"}, "provider": {"type": "string"}, "proxyType": {"type": "string", "enum": ["http", "httpTunnel", "ipgo", "luminati", "socks5", "ssh", "vps"]}, "sshKey": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}, "username": {"type": "string"}}}, "IpWithLocationVo": {"title": "IpWithLocationVo", "type": "object", "properties": {"autoRenew": {"type": "boolean"}, "cloudProvider": {"type": "string"}, "cloudRegion": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "directDownTraffic": {"type": "integer", "format": "int64"}, "directUpTraffic": {"type": "integer", "format": "int64"}, "domestic": {"type": "boolean"}, "downTraffic": {"type": "integer", "format": "int64"}, "dynamic": {"type": "boolean"}, "eipId": {"type": "integer", "format": "int64"}, "enableWhitelist": {"type": "boolean"}, "expireStatus": {"type": "string", "description": "过期状态", "enum": ["Expired", "Expiring", "Normal"]}, "forbiddenLongLatitude": {"type": "boolean"}, "gatewayId": {"type": "integer", "format": "int64"}, "goodsId": {"type": "integer", "format": "int64"}, "goodsType": {"type": "string", "enum": ["Credit", "CreditPack", "ExclusiveIp", "FingerprintQuota", "IosDeveloperApprove", "Ip", "IpGo", "IpProxy", "MarketFlow", "None", "PluginPack", "PriceDifference", "ProxyTraffic", "RpaCaptcha", "RpaExecuteQuota", "RpaMobile", "RpaOpenAi", "RpaSendEmail", "RpaSendSms", "RpaSendWeChat", "Rpa_Voucher_Base", "Rpa_Voucher_Performance", "SharingIp", "ShopQuota", "ShopSecurityPolicy", "StorageQuota", "TeamMemberQuota", "Team<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TkPack", "TkPackTrail", "Tkshop", "TkshopEnterprise", "TkshopStandard", "Traffic", "TransitTraffic", "TransitTrafficV2", "UserExclusiveIp", "Voucher"]}, "id": {"type": "integer", "format": "int64"}, "importType": {"type": "string", "enum": ["Platform", "User"]}, "invalidTime": {"type": "string", "format": "date-time"}, "ip": {"type": "string"}, "ipv6": {"type": "boolean"}, "lastProbeTime": {"type": "string", "format": "date-time"}, "latitude": {"type": "number", "format": "double"}, "locale": {"type": "string"}, "location": {"$ref": "#/components/schemas/IpLocationDto"}, "locationId": {"type": "integer", "format": "int64"}, "longitude": {"type": "number", "format": "double"}, "name": {"type": "string"}, "networkType": {"type": "string", "enum": ["cloudIdc", "mobile", "proxyIdc", "residential", "unknown", "unknownIdc"]}, "operateStatus": {"type": "string", "enum": ["shared", "sharing", "sole", "transferring"]}, "originalTeam": {"type": "integer", "format": "int64"}, "periodUnit": {"type": "string", "enum": ["Buyout", "Byte", "GB", "GB天", "个", "个天", "分钟", "周", "天", "年", "张", "无", "月", "次"]}, "pipeType": {"type": "string", "enum": ["None", "Proxy", "Tunnel", "TunnelFailToProxy"]}, "preferTransit": {"type": "integer", "format": "int64"}, "probeError": {"type": "string"}, "protoType": {"type": "string", "enum": ["http", "httpTunnel", "ipgo", "luminati", "socks5", "ssh", "vps"]}, "providerName": {"type": "string", "description": "供应商名称"}, "realIp": {"type": "string"}, "refreshUrl": {"type": "string"}, "remoteLogin": {"type": "boolean"}, "renewPrice": {"type": "number", "format": "bigdecimal"}, "source": {"type": "string"}, "speedLimit": {"type": "integer", "format": "int32"}, "status": {"type": "string", "enum": ["Available", "Pending", "Unavailable"]}, "sticky": {"type": "boolean"}, "teamId": {"type": "integer", "format": "int64"}, "testingTime": {"type": "integer", "format": "int32"}, "timezone": {"type": "string"}, "traffic": {"type": "number", "format": "double"}, "trafficCurrency": {"type": "string", "enum": ["CREDIT", "RMB", "USD"]}, "trafficPrice": {"type": "number", "format": "bigdecimal"}, "trafficUnlimited": {"type": "boolean"}, "transitType": {"type": "string", "enum": ["Auto", "Direct", "Transit"]}, "tunnelTypes": {"type": "string"}, "upTraffic": {"type": "integer", "format": "int64"}, "valid": {"type": "boolean"}, "validEndDate": {"type": "string", "format": "date-time"}, "vpsId": {"type": "integer", "format": "int64"}}}, "IppIpWithLocationVo": {"title": "IppIpWithLocationVo", "type": "object", "properties": {"activeSessions": {"type": "integer", "format": "int32"}, "areaCode": {"type": "integer", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}, "directDownTraffic": {"type": "integer", "format": "int64"}, "directUpTraffic": {"type": "integer", "format": "int64"}, "domestic": {"type": "boolean"}, "dynamic": {"type": "boolean"}, "expireTime": {"type": "string", "format": "date-time"}, "forbiddenLongLatitude": {"type": "boolean"}, "host": {"type": "string"}, "hostDomestic": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "invalidTime": {"type": "string", "format": "date-time"}, "ippId": {"type": "integer", "format": "int64"}, "lastAllocTime": {"type": "string", "format": "date-time"}, "lastProbeTime": {"type": "string", "format": "date-time"}, "location": {"$ref": "#/components/schemas/IpLocationDto"}, "locationId": {"type": "integer", "format": "int64"}, "outboundIp": {"type": "string"}, "password": {"type": "string"}, "port": {"type": "integer", "format": "int32"}, "probeError": {"type": "string"}, "proxyType": {"type": "string"}, "refTeamIp": {"type": "integer", "format": "int64"}, "status": {"type": "string", "enum": ["Available", "Pending", "Unavailable"]}, "teamId": {"type": "integer", "format": "int64"}, "transitDownTraffic": {"type": "integer", "format": "int64"}, "transitUpTraffic": {"type": "integer", "format": "int64"}, "username": {"type": "string"}, "valid": {"type": "boolean"}}}, "IppParamVo": {"title": "IppParamVo", "type": "object", "properties": {"paramKey": {"type": "string"}, "paramType": {"type": "string", "enum": ["ApiEndpoint", "ApiEndpointRaw", "A<PERSON>H<PERSON><PERSON>", "ApiHttpMethod", "ApiPassword", "Api<PERSON>ueryP<PERSON><PERSON>", "ApiSignKey", "ApiUsername", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "GlobalWhitelist", "MinApiInterval", "MinApiNum", "ProxyPassword", "ProxyType", "ProxyUsername", "Proxy<PERSON><PERSON><PERSON><PERSON>", "Unknown"]}, "paramValue": {"type": "string"}}}, "MicroServiceInfoVo": {"title": "MicroServiceInfoVo", "type": "object", "properties": {"git": {"type": "object"}, "services": {"type": "array", "items": {"$ref": "#/components/schemas/ServiceHealthInfo"}}}}, "NotifyBankPayFailedRequest": {"title": "NotifyBankPayFailedRequest", "type": "object", "properties": {"orderId": {"type": "integer", "format": "int64"}, "payStatus": {"type": "string", "enum": ["CANCELED", "Created", "Locked", "PAID", "PartialRefund", "REFUNDED", "WAIT_CONFIRM"]}, "remarks": {"type": "string"}}}, "NotifyBankPaySuccessRequest": {"title": "NotifyBankPaySuccessRequest", "type": "object", "properties": {"orderId": {"type": "integer", "format": "int64"}, "remarks": {"type": "string"}, "thirdPartOrderNumber": {"type": "string"}}}, "ObjectInfo": {"title": "ObjectInfo", "type": "object", "properties": {"clazz": {"type": "string"}, "key": {"type": "string"}, "keyUrl": {"type": "string"}, "toString": {"type": "string"}}}, "PeerConnStat": {"title": "PeerConnStat", "type": "object", "properties": {"clientDfSessionStat": {"$ref": "#/components/schemas/DynamicForwardStat"}, "peerAddress": {"$ref": "#/components/schemas/TunnelAddress"}, "serverDfSessionStat": {"$ref": "#/components/schemas/DynamicForwardStat"}}}, "PrincipalSigningKeyResult": {"title": "PrincipalSigningKeyResult", "type": "object", "properties": {"algorithm": {"type": "string"}, "code": {"type": "integer", "format": "int32"}, "key": {"type": "string"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "PrivilegeMeta": {"title": "PrivilegeMeta", "type": "object", "properties": {"condition": {"type": "string"}, "methods": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "paths": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "principalType": {"type": "string", "enum": ["AK", "AkHmac", "AnonymousUser", "ApiWorker", "App", "ConsoleUser", "DeviceToken", "FbApp", "FbConsoleUser", "FbPortalUser", "GatewayProxy", "HYRuntime", "IP", "IsvAk", "JDBox", "None", "PartnerUser", "Portal", "PortalUser", "Proxy", "ProxyMgr", "ProxyTemplate", "ProxyTmp", "PublicGranted", "RpaExecutor", "ShopChannel", "ShopSession", "ShopSessionMonitorConsumer", "ShopSessionMonitorProducer", "ShopSessionV2", "System", "Transit", "Webhook"]}, "privileges": {"type": "array", "items": {"type": "string"}}, "roles": {"type": "array", "items": {"type": "string"}}, "serviceId": {"type": "string"}, "webhookTypes": {"type": "array", "items": {"type": "string"}}}}, "ProbeProxyRequest": {"title": "ProbeProxyRequest", "type": "object", "properties": {"parserType": {"type": "string", "enum": ["ifconfigme", "ipsb", "lumtest", "transit"]}, "proxyConfig": {"$ref": "#/components/schemas/ProxyConfig"}, "timeout": {"type": "integer", "format": "int32"}, "transitEndpoint": {"type": "string"}, "transitId": {"type": "integer", "format": "int64"}}}, "ProbeResult": {"title": "ProbeResult", "type": "object", "properties": {"connectTime": {"type": "integer", "format": "int64"}, "error": {"type": "string"}, "originalError": {"type": "string"}, "reachable": {"type": "boolean"}, "remoteIpEndpoint": {"type": "string"}, "success": {"type": "boolean"}, "testingTime": {"type": "integer", "format": "int64"}}}, "ProduceIpRequest": {"title": "ProduceIpRequest", "type": "object", "properties": {"connectTimeout": {"type": "integer", "format": "int32"}, "ippId": {"type": "integer", "format": "int64"}, "method": {"type": "string"}, "num": {"type": "integer", "format": "int32"}, "params": {"type": "array", "items": {"$ref": "#/components/schemas/IppParamVo"}}, "provider": {"type": "string"}, "readTimeout": {"type": "integer", "format": "int32"}}}, "ProduceIpResultVo": {"title": "ProduceIpResultVo", "type": "object", "properties": {"ips": {"type": "array", "items": {"$ref": "#/components/schemas/RotatingIp"}}, "remainQuantity": {"type": "integer", "format": "int32"}}}, "ProduceIpSpecVo": {"title": "ProduceIpSpecVo", "type": "object", "properties": {"headers": {"type": "object", "additionalProperties": {"type": "string"}, "description": "需要追加的请求头"}, "method": {"type": "string", "description": "请求方法，比如GET/POST等"}, "url": {"type": "string", "description": "请求URL，携带参数"}}}, "ProductInfo": {"title": "ProductInfo", "type": "object", "properties": {"name": {"type": "string"}, "principal": {"type": "string"}, "version": {"type": "string"}}}, "ProxyConfig": {"title": "ProxyConfig", "type": "object", "properties": {"host": {"type": "string"}, "ipVersion": {"type": "string", "enum": ["Auto", "IPv4", "IPv6"]}, "password": {"type": "string"}, "port": {"type": "integer", "format": "int32"}, "proxyType": {"type": "string", "enum": ["http", "httpTunnel", "https", "socks4", "socks5", "ssh"]}, "sshKey": {"type": "string"}, "username": {"type": "string"}}}, "ProxyProbeWithRemoteIp": {"title": "ProxyProbeWithRemoteIp", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "connectTime": {"type": "integer", "format": "int64"}, "error": {"type": "string"}, "handshakeTime": {"type": "integer", "format": "int64"}, "originalError": {"type": "string"}, "output": {"type": "string"}, "proto": {"type": "string"}, "reachable": {"type": "boolean"}, "remoteIp": {"type": "string"}, "remoteIpEndpoint": {"type": "string"}, "success": {"type": "boolean"}, "testingTime": {"type": "integer", "format": "int64"}}}, "ProxyTemplateVo": {"title": "ProxyTemplateVo", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "identifier": {"type": "string"}, "importType": {"type": "string", "enum": ["Platform", "User"]}, "teamId": {"type": "integer", "format": "int64"}, "token": {"type": "string"}, "valid": {"type": "boolean"}}}, "ProxyTokenVo": {"title": "ProxyTokenVo", "type": "object", "properties": {"expireTime": {"type": "string", "description": "过期时间", "format": "date-time"}, "identifier": {"type": "string", "description": "标识"}, "token": {"type": "string", "description": "Proxy授权码"}, "tokenEncoded": {"type": "boolean", "description": "token是否编码", "example": false}}}, "PublishAjaxEventRequest": {"title": "PublishAjaxEventRequest", "type": "object", "properties": {"data": {"type": "object"}, "eventName": {"type": "string"}, "resId": {"type": "string"}, "users": {"uniqueItems": true, "type": "array", "items": {"type": "integer", "format": "int64"}}}}, "QQWryInfo": {"title": "QQWryInfo", "type": "object", "properties": {"location": {"type": "string"}, "subInfo": {"type": "string"}}}, "RateKeyVo": {"title": "RateKeyVo", "type": "object", "properties": {"key": {"type": "string"}, "keyType": {"type": "string", "enum": ["Constant", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "None", "Principal", "QueryParam", "RemoteIp", "Session", "UserAgent"]}}}, "RateLimiterMeta": {"title": "RateLimiterMeta", "type": "object", "properties": {"keys": {"type": "array", "items": {"$ref": "#/components/schemas/RateKeyVo"}}, "methods": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "paths": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "principalType": {"type": "string", "enum": ["AK", "AkHmac", "AnonymousUser", "ApiWorker", "App", "ConsoleUser", "DeviceToken", "FbApp", "FbConsoleUser", "FbPortalUser", "GatewayProxy", "HYRuntime", "IP", "IsvAk", "JDBox", "None", "PartnerUser", "Portal", "PortalUser", "Proxy", "ProxyMgr", "ProxyTemplate", "ProxyTmp", "PublicGranted", "RpaExecutor", "ShopChannel", "ShopSession", "ShopSessionMonitorConsumer", "ShopSessionMonitorProducer", "ShopSessionV2", "System", "Transit", "Webhook"]}, "rate": {"type": "number", "format": "double"}, "scope": {"type": "string", "enum": ["Global", "Request", "Router"]}, "serviceId": {"type": "string"}}}, "ReScheduleOneShotRequest": {"title": "ReScheduleOneShotRequest", "type": "object", "properties": {"delay": {"type": "integer", "format": "int64"}, "matchKey": {"type": "string"}, "params": {"type": "array", "items": {"type": "object"}}, "rjobType": {"type": "string", "enum": ["AutoFinishRenewTkshop", "AutoInvalidateTkshop", "AutoRejectAudit", "AutoSyncTkVideo", "AutoUntagGhShop", "BlockTeam", "CheckAndBindIp", "CheckAndDeletePendingTeam", "CheckAndDestroyExpiredIp", "CheckAndInvalidatePluginPack", "CheckAndReleaseEip", "CheckAndReleaseInstance", "CheckAndSyncRpaCloudInstance", "CheckIPGORunning", "CheckTaskTimeout", "CheckTeamIpPending", "CheckTransitReady", "CheckTransitRecovery", "CleanMongoFile", "CloseIpTrafficPack", "CloseOvertimePlanTask", "CloseTeamSessionByTrafficAbort", "CrsUploadProductImage", "DelayCalcRpaFlowAttachmentSize", "DelayCalcShopDataSize", "DelayDeleteRpaCloudInstance", "DelayDeleteShop", "DelayDeleteShopCut", "DelayTurnOffRpaSilent", "DeleteTempFilePutOssPath", "ExecuteActivity", "ExecuteGhJobPlan", "ExecuteRpaPlan", "FlushJdboxGateway", "InvalidKolSubTeam", "JDEipAllocationWorker", "KOLWatchLiveRewards", "KolAutoAllocation", "MobileAccountHealthCheck", "MobileAccountMaintenance", "MobileAccountMessageCheck", "None", "NotifyExpiringIp", "NotifyGhHasNewMsg", "OfflineOrderProduceTimeout", "ProbeIpsOnce", "ResetBatchJDEip", "ResetBatchJDEipV2", "ResetJDBoxEip", "ResetJDEip", "ResetYgEip", "RpaLoopPlanLostCheck", "RpaTriggerCalc", "ScheduleGhPlanDurationTrigger", "ScheduleRpaDurationTrigger", "ShopSnapshotPlan", "SupportLeaveTeam", "SwitchIpWithKey", "SyncTkVideo", "Testing", "TkSendEmail", "TkSendEmailChecker", "TkSyncMediaDailyStat", "TkSyncMediaHomeStat", "UpdateClashProfile", "UpdateImageIdAfterCopied", "UpdateOpenapiTaskTimeout"]}, "unit": {"type": "string", "enum": ["DAYS", "HOURS", "MICROSECONDS", "MILLISECONDS", "MINUTES", "NANOSECONDS", "SECONDS"]}}}, "RefundRequest": {"title": "RefundRequest", "type": "object", "properties": {"backtrack": {"type": "boolean", "description": "实付金额是否原路返回，为空和false都表示将实付金额退到余额", "example": false}, "balanceAmount": {"type": "number", "description": "余额退多少，不可超过订单实际余额支付额。为空表示全额退", "format": "bigdecimal"}, "orderId": {"type": "integer", "format": "int64"}, "realPayAmount": {"type": "number", "description": "实付金额退多少，不可超过订单实际实付额。为空表示全额退", "format": "bigdecimal"}, "remarks": {"type": "string"}, "voucher": {"type": "boolean", "description": "是否退代金券，为空和true都表示退", "example": false}}}, "ReloadSmsProviderResultVo": {"title": "ReloadSmsProviderResultVo", "type": "object", "properties": {"currentProduct": {"$ref": "#/components/schemas/ProductInfo"}}}, "RemoteApiResult": {"title": "RemoteApiResult", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "RemoteProbeRemoteIpRequest": {"title": "RemoteProbeRemoteIpRequest", "type": "object", "properties": {"pc": {"$ref": "#/components/schemas/ProxyConfig"}, "timeout": {"type": "integer", "format": "int32"}, "url": {"type": "string"}}}, "RequestMeta": {"title": "RequestMeta", "type": "object", "properties": {"methods": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "paths": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "principalType": {"type": "string", "enum": ["AK", "AkHmac", "AnonymousUser", "ApiWorker", "App", "ConsoleUser", "DeviceToken", "FbApp", "FbConsoleUser", "FbPortalUser", "GatewayProxy", "HYRuntime", "IP", "IsvAk", "JDBox", "None", "PartnerUser", "Portal", "PortalUser", "Proxy", "ProxyMgr", "ProxyTemplate", "ProxyTmp", "PublicGranted", "RpaExecutor", "ShopChannel", "ShopSession", "ShopSessionMonitorConsumer", "ShopSessionMonitorProducer", "ShopSessionV2", "System", "Transit", "Webhook"]}, "serviceId": {"type": "string"}}}, "ResetIpLocationResultVo": {"title": "ResetIpLocationResultVo", "type": "object", "properties": {"currentLocation": {"$ref": "#/components/schemas/IpLocationDto"}, "currentProduct": {"$ref": "#/components/schemas/ProductInfo"}, "previousLocation": {"$ref": "#/components/schemas/IpLocationDto"}}}, "RotatingIp": {"title": "RotatingIp", "type": "object", "properties": {"city": {"type": "string"}, "cityCode": {"type": "string"}, "countryCode": {"type": "string"}, "expireTime": {"type": "string", "format": "date-time"}, "host": {"type": "string"}, "isp": {"type": "string"}, "outboundIp": {"type": "string"}, "password": {"type": "string"}, "port": {"type": "integer", "format": "int32"}, "provinceCode": {"type": "string"}, "proxyType": {"type": "string"}, "tunnel": {"type": "boolean"}, "username": {"type": "string"}}}, "RpcConnectedPeers": {"title": "RpcConnectedPeers", "type": "object", "properties": {"addresses": {"type": "array", "items": {"$ref": "#/components/schemas/TunnelAddress"}}, "tunnelAddress": {"$ref": "#/components/schemas/TunnelAddress"}}}, "ScheduleCronRequest": {"title": "ScheduleCronRequest", "type": "object", "properties": {"cronExpression": {"type": "string"}, "jobId": {"type": "string"}, "params": {"type": "array", "items": {"type": "object"}}, "rjobType": {"type": "string", "enum": ["AutoFinishRenewTkshop", "AutoInvalidateTkshop", "AutoRejectAudit", "AutoSyncTkVideo", "AutoUntagGhShop", "BlockTeam", "CheckAndBindIp", "CheckAndDeletePendingTeam", "CheckAndDestroyExpiredIp", "CheckAndInvalidatePluginPack", "CheckAndReleaseEip", "CheckAndReleaseInstance", "CheckAndSyncRpaCloudInstance", "CheckIPGORunning", "CheckTaskTimeout", "CheckTeamIpPending", "CheckTransitReady", "CheckTransitRecovery", "CleanMongoFile", "CloseIpTrafficPack", "CloseOvertimePlanTask", "CloseTeamSessionByTrafficAbort", "CrsUploadProductImage", "DelayCalcRpaFlowAttachmentSize", "DelayCalcShopDataSize", "DelayDeleteRpaCloudInstance", "DelayDeleteShop", "DelayDeleteShopCut", "DelayTurnOffRpaSilent", "DeleteTempFilePutOssPath", "ExecuteActivity", "ExecuteGhJobPlan", "ExecuteRpaPlan", "FlushJdboxGateway", "InvalidKolSubTeam", "JDEipAllocationWorker", "KOLWatchLiveRewards", "KolAutoAllocation", "MobileAccountHealthCheck", "MobileAccountMaintenance", "MobileAccountMessageCheck", "None", "NotifyExpiringIp", "NotifyGhHasNewMsg", "OfflineOrderProduceTimeout", "ProbeIpsOnce", "ResetBatchJDEip", "ResetBatchJDEipV2", "ResetJDBoxEip", "ResetJDEip", "ResetYgEip", "RpaLoopPlanLostCheck", "RpaTriggerCalc", "ScheduleGhPlanDurationTrigger", "ScheduleRpaDurationTrigger", "ShopSnapshotPlan", "SupportLeaveTeam", "SwitchIpWithKey", "SyncTkVideo", "Testing", "TkSendEmail", "TkSendEmailChecker", "TkSyncMediaDailyStat", "TkSyncMediaHomeStat", "UpdateClashProfile", "UpdateImageIdAfterCopied", "UpdateOpenapiTaskTimeout"]}}}, "ScheduleOneShotRequest": {"title": "ScheduleOneShotRequest", "type": "object", "properties": {"delay": {"type": "integer", "format": "int64"}, "params": {"type": "array", "items": {"type": "object"}}, "rjobType": {"type": "string", "enum": ["AutoFinishRenewTkshop", "AutoInvalidateTkshop", "AutoRejectAudit", "AutoSyncTkVideo", "AutoUntagGhShop", "BlockTeam", "CheckAndBindIp", "CheckAndDeletePendingTeam", "CheckAndDestroyExpiredIp", "CheckAndInvalidatePluginPack", "CheckAndReleaseEip", "CheckAndReleaseInstance", "CheckAndSyncRpaCloudInstance", "CheckIPGORunning", "CheckTaskTimeout", "CheckTeamIpPending", "CheckTransitReady", "CheckTransitRecovery", "CleanMongoFile", "CloseIpTrafficPack", "CloseOvertimePlanTask", "CloseTeamSessionByTrafficAbort", "CrsUploadProductImage", "DelayCalcRpaFlowAttachmentSize", "DelayCalcShopDataSize", "DelayDeleteRpaCloudInstance", "DelayDeleteShop", "DelayDeleteShopCut", "DelayTurnOffRpaSilent", "DeleteTempFilePutOssPath", "ExecuteActivity", "ExecuteGhJobPlan", "ExecuteRpaPlan", "FlushJdboxGateway", "InvalidKolSubTeam", "JDEipAllocationWorker", "KOLWatchLiveRewards", "KolAutoAllocation", "MobileAccountHealthCheck", "MobileAccountMaintenance", "MobileAccountMessageCheck", "None", "NotifyExpiringIp", "NotifyGhHasNewMsg", "OfflineOrderProduceTimeout", "ProbeIpsOnce", "ResetBatchJDEip", "ResetBatchJDEipV2", "ResetJDBoxEip", "ResetJDEip", "ResetYgEip", "RpaLoopPlanLostCheck", "RpaTriggerCalc", "ScheduleGhPlanDurationTrigger", "ScheduleRpaDurationTrigger", "ShopSnapshotPlan", "SupportLeaveTeam", "SwitchIpWithKey", "SyncTkVideo", "Testing", "TkSendEmail", "TkSendEmailChecker", "TkSyncMediaDailyStat", "TkSyncMediaHomeStat", "UpdateClashProfile", "UpdateImageIdAfterCopied", "UpdateOpenapiTaskTimeout"]}, "unit": {"type": "string", "enum": ["DAYS", "HOURS", "MICROSECONDS", "MILLISECONDS", "MINUTES", "NANOSECONDS", "SECONDS"]}}}, "ScheduleWithFixedDelayVo": {"title": "ScheduleWithFixedDelayVo", "type": "object", "properties": {"delay": {"type": "integer", "format": "int64"}, "initialDelay": {"type": "integer", "format": "int64"}, "jobId": {"type": "string"}, "params": {"type": "array", "items": {"type": "object"}}, "rjobType": {"type": "string", "enum": ["AutoFinishRenewTkshop", "AutoInvalidateTkshop", "AutoRejectAudit", "AutoSyncTkVideo", "AutoUntagGhShop", "BlockTeam", "CheckAndBindIp", "CheckAndDeletePendingTeam", "CheckAndDestroyExpiredIp", "CheckAndInvalidatePluginPack", "CheckAndReleaseEip", "CheckAndReleaseInstance", "CheckAndSyncRpaCloudInstance", "CheckIPGORunning", "CheckTaskTimeout", "CheckTeamIpPending", "CheckTransitReady", "CheckTransitRecovery", "CleanMongoFile", "CloseIpTrafficPack", "CloseOvertimePlanTask", "CloseTeamSessionByTrafficAbort", "CrsUploadProductImage", "DelayCalcRpaFlowAttachmentSize", "DelayCalcShopDataSize", "DelayDeleteRpaCloudInstance", "DelayDeleteShop", "DelayDeleteShopCut", "DelayTurnOffRpaSilent", "DeleteTempFilePutOssPath", "ExecuteActivity", "ExecuteGhJobPlan", "ExecuteRpaPlan", "FlushJdboxGateway", "InvalidKolSubTeam", "JDEipAllocationWorker", "KOLWatchLiveRewards", "KolAutoAllocation", "MobileAccountHealthCheck", "MobileAccountMaintenance", "MobileAccountMessageCheck", "None", "NotifyExpiringIp", "NotifyGhHasNewMsg", "OfflineOrderProduceTimeout", "ProbeIpsOnce", "ResetBatchJDEip", "ResetBatchJDEipV2", "ResetJDBoxEip", "ResetJDEip", "ResetYgEip", "RpaLoopPlanLostCheck", "RpaTriggerCalc", "ScheduleGhPlanDurationTrigger", "ScheduleRpaDurationTrigger", "ShopSnapshotPlan", "SupportLeaveTeam", "SwitchIpWithKey", "SyncTkVideo", "Testing", "TkSendEmail", "TkSendEmailChecker", "TkSyncMediaDailyStat", "TkSyncMediaHomeStat", "UpdateClashProfile", "UpdateImageIdAfterCopied", "UpdateOpenapiTaskTimeout"]}, "unit": {"type": "string", "enum": ["DAYS", "HOURS", "MICROSECONDS", "MILLISECONDS", "MINUTES", "NANOSECONDS", "SECONDS"]}}}, "ServiceHealthInfo": {"title": "ServiceHealthInfo", "type": "object", "properties": {"details": {"type": "object"}, "name": {"type": "string"}, "status": {"$ref": "#/components/schemas/HealthStatus"}}}, "ShopChannelTokenVo": {"title": "ShopChannelTokenVo", "type": "object", "properties": {"channelId": {"type": "integer", "description": "通道ID", "format": "int64"}, "channelSessionId": {"type": "integer", "description": "通道会话ID", "format": "int64"}, "dynamicStrategy": {"type": "string", "enum": ["Off", "<PERSON><PERSON><PERSON>", "SwitchOnSession"]}, "endpoints": {"type": "array", "description": "接入点的端点", "items": {"$ref": "#/components/schemas/EndpointVo"}}, "expireTime": {"type": "string", "description": "过期时间", "format": "date-time"}, "ip": {"description": "绑定的IP", "$ref": "#/components/schemas/IpWithLocationVo"}, "ipSocks": {"description": "代理协议的IP的代理信息", "$ref": "#/components/schemas/IpSocksDto"}, "ippIp": {"description": "IP池IP", "$ref": "#/components/schemas/IppIpWithLocationVo"}, "lanProxy": {"description": "本地代理（临时IP、OpenApi指定IP）", "$ref": "#/components/schemas/ShopLanProxyDto"}, "locationId": {"type": "integer", "format": "int64"}, "locationLevel": {"type": "string", "enum": ["City", "Continent", "Country", "District", "None", "Province", "Unknown"]}, "official": {"type": "boolean", "description": "官方链路", "example": false}, "primary": {"type": "boolean", "description": "是否是主通道", "example": false}, "proxyId": {"type": "integer", "description": "当前会话使用的Proxy ID", "format": "int64"}, "sessionId": {"type": "integer", "description": "全局会话ID", "format": "int64"}, "speedLimit": {"type": "integer", "description": "限速，0表示不限速；单位 kbps", "format": "int32"}, "token": {"type": "string"}, "transitType": {"type": "string", "description": "接入点连接方式", "enum": ["Auto", "Direct", "Transit"]}}}, "ShopDto": {"title": "ShopDto", "type": "object", "properties": {"account": {"type": "string", "description": "账户账号"}, "allowExtension": {"type": "boolean", "description": "是否允许用户自行安装插件", "example": false}, "allowMonitor": {"type": "boolean", "description": "会话是否允许监视", "example": false}, "allowSkip": {"type": "boolean", "description": "是否允许跳过敏感操作", "example": false}, "autoFill": {"type": "boolean", "description": "是否自动代填", "example": false}, "bookmarkBar": {"type": "string"}, "coordinateId": {"type": "string"}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time"}, "creatorId": {"type": "integer", "description": "创建者", "format": "int64"}, "deleteTime": {"type": "string", "format": "date-time"}, "deleting": {"type": "boolean"}, "description": {"type": "string", "description": "描述"}, "domainPolicy": {"type": "string", "enum": ["Blacklist", "None", "Whitelist"]}, "downTraffic": {"type": "integer", "description": "下行流量", "format": "int64"}, "exclusive": {"type": "boolean", "description": "是否独占访问", "example": false}, "extraProp": {"type": "string"}, "fingerprintId": {"type": "integer", "description": "绑定的指纹Id", "format": "int64"}, "fingerprintTemplateId": {"type": "integer", "description": "绑定的指纹模板Id（只有无状态账号才允许绑定指纹模板）", "format": "int64"}, "frontUrl": {"type": "string"}, "googleTranslateSpeed": {"type": "boolean"}, "homePage": {"type": "string"}, "homePageSites": {"type": "string"}, "id": {"type": "integer", "description": "id", "format": "int64"}, "imageForbiddenSize": {"type": "integer", "format": "int64"}, "intranetEnabled": {"type": "boolean"}, "ipSwitchCheckInterval": {"type": "integer", "format": "int32"}, "ipSwitchStrategy": {"type": "string", "enum": ["Abort", "<PERSON><PERSON>", "Off"]}, "lastAccessTime": {"type": "string", "format": "date-time"}, "lastAccessUser": {"type": "integer", "format": "int64"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "loginStatus": {"type": "string", "enum": ["Offline", "Online", "Unknown"]}, "markCode": {"type": "string", "description": "任务栏分身标记文字"}, "markCodeBg": {"type": "integer", "description": "任务栏分身标记背景颜色", "format": "int32"}, "monitorPerception": {"type": "boolean"}, "name": {"type": "string", "description": "账户名称"}, "nameBookmarkEnabled": {"type": "boolean"}, "operateStatus": {"type": "string", "enum": ["shared", "sharing", "sole", "transferring"]}, "operatingCategory": {"type": "string", "description": "经营品类", "enum": ["医药保健", "图书文具", "宠物用品", "家具建材", "家电电器", "工业用品", "户外运动", "手机数码", "手表眼镜", "护肤美妆", "母婴玩具", "汽车配件", "生活家居", "电商其他", "电脑平板", "艺术珠宝", "花园聚会", "计生情趣", "软件程序", "鞋服箱包", "音乐影视", "食品生鲜", "鲜花绿植"]}, "parentShopId": {"type": "integer", "format": "int64"}, "password": {"type": "string", "description": "密码"}, "platformId": {"type": "integer", "description": "平台ID", "format": "int64"}, "privateAddress": {"type": "string"}, "privateTitle": {"type": "string"}, "recordPerception": {"type": "boolean"}, "recordPolicy": {"type": "string", "enum": ["<PERSON><PERSON>", "Disabled", "Forced"]}, "requireIp": {"type": "boolean"}, "resourcePolicy": {"type": "integer", "format": "int32"}, "securityPolicyEnabled": {"type": "boolean"}, "securityPolicyUpdateTime": {"type": "string", "format": "date-time"}, "sharePolicyId": {"type": "string"}, "shopDataSize": {"type": "integer", "format": "int64"}, "stateless": {"type": "boolean"}, "statelessChangeFp": {"type": "boolean"}, "statelessSyncPolicy": {"type": "integer", "format": "int32"}, "syncPolicy": {"type": "integer", "format": "int32"}, "teamId": {"type": "integer", "description": "团队ID", "format": "int64"}, "trafficAlertStrategy": {"type": "string", "enum": ["Abort", "Off"]}, "trafficAlertThreshold": {"type": "integer", "format": "int32"}, "trafficSaving": {"type": "boolean"}, "type": {"type": "string", "enum": ["Global", "Local", "None"]}, "upTraffic": {"type": "integer", "description": "上行流量", "format": "int64"}, "webSecurity": {"type": "boolean"}}}, "ShopLanProxyDto": {"title": "ShopLanProxyDto", "type": "object", "properties": {"enabled": {"type": "boolean"}, "host": {"type": "string"}, "hostDomestic": {"type": "boolean"}, "hostLocationId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "latitude": {"type": "number", "format": "double"}, "locale": {"type": "string"}, "locationId": {"type": "integer", "format": "int64"}, "longitude": {"type": "number", "format": "double"}, "networkType": {"type": "string", "enum": ["UseDirect", "UseProxy", "UseSystem"]}, "password": {"type": "string"}, "port": {"type": "integer", "format": "int32"}, "probeOnSession": {"type": "boolean"}, "proxyType": {"type": "string"}, "remoteIp": {"type": "string"}, "sshKey": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}, "timezone": {"type": "string"}, "updateTime": {"type": "string", "format": "date-time"}, "username": {"type": "string"}}}, "ShopRouterVo": {"title": "ShopRouterVo", "type": "object", "properties": {"channelId": {"type": "integer", "format": "int64"}, "orderNo": {"type": "integer", "format": "int32"}, "rule": {"type": "string"}, "ruleType": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "DomainWildcard", "UrlPattern", "UrlWildcard"]}}}, "ShopTokenVo": {"title": "ShopTokenVo", "type": "object", "properties": {"channelTokens": {"type": "array", "description": "每个通道的Token", "items": {"$ref": "#/components/schemas/ShopChannelTokenVo"}}, "routers": {"type": "array", "description": "路由规则", "items": {"$ref": "#/components/schemas/ShopRouterVo"}}, "sessionId": {"type": "integer", "description": "会话ID", "format": "int64"}, "sessionProxyHost": {"type": "string", "description": "本地打开会话监听的地址"}, "teamId": {"type": "integer", "description": "所属团队", "format": "int64"}}, "description": "账户会话Token"}, "ShopWithPlatformVo": {"title": "ShopWithPlatformVo", "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "operatingCategory": {"type": "string", "enum": ["医药保健", "图书文具", "宠物用品", "家具建材", "家电电器", "工业用品", "户外运动", "手机数码", "手表眼镜", "护肤美妆", "母婴玩具", "汽车配件", "生活家居", "电商其他", "电脑平板", "艺术珠宝", "花园聚会", "计生情趣", "软件程序", "鞋服箱包", "音乐影视", "食品生鲜", "鲜花绿植"]}, "parentShopId": {"type": "integer", "format": "int64"}, "platformArea": {"type": "string", "enum": ["Argentina", "Australia", "Austria", "Belarus", "Belgium", "Bolivia", "Brazil", "Canada", "Chile", "China", "Colombia", "Costa_Rica", "Dominican", "Ecuador", "Egypt", "France", "Germany", "Global", "Guatemala", "Honduras", "HongKong", "India", "Indonesia", "Ireland", "Israel", "Italy", "Japan", "Kazakhstan", "Korea", "Malaysia", "Mexico", "Netherlands", "Nicaragua", "Panama", "Paraguay", "Peru", "Philippines", "Poland", "Portuguese", "Puerto_Rico", "Russia", "Salvador", "Saudi_Arabia", "Singapore", "Spain", "Sweden", "Switzerland", "Taiwan", "Thailand", "Turkey", "United_Arab_Emirates", "United_Kingdom", "United_States", "Uruguay", "Venezuela", "Vietnam"]}, "platformId": {"type": "integer", "format": "int64"}, "platformName": {"type": "string"}, "recordPolicy": {"type": "string", "enum": ["<PERSON><PERSON>", "Disabled", "Forced"]}, "securityPolicyEnabled": {"type": "boolean"}, "teamId": {"type": "integer", "format": "int64"}, "type": {"type": "string", "enum": ["Global", "Local", "None"]}}}, "SpyListBeansVo": {"title": "SpyListBeansVo", "type": "object", "properties": {"count": {"type": "integer", "format": "int32"}, "keys": {"type": "array", "items": {"$ref": "#/components/schemas/ObjectInfo"}}}}, "SpyObjectVo": {"title": "SpyObjectVo", "type": "object", "properties": {"classes": {"type": "array", "items": {"$ref": "#/components/schemas/FieldsInfo"}}}}, "SpyVo": {"title": "SpyVo", "type": "object", "properties": {"beans": {"$ref": "#/components/schemas/SpyListBeansVo"}, "object": {"$ref": "#/components/schemas/SpyObjectVo"}}}, "StartProxyForwardResult": {"title": "StartProxyForwardResult", "type": "object", "properties": {"alreadyStarted": {"type": "boolean"}, "bindIp": {"type": "string"}, "port": {"type": "integer", "format": "int32"}, "proxyId": {"type": "integer", "format": "int64"}}}, "StartProxyForwardVo": {"title": "StartProxyForwardVo", "type": "object", "properties": {"auth": {"type": "boolean"}, "bindIp": {"type": "string"}, "enabled": {"type": "boolean"}, "forwardIp": {"type": "string"}, "password": {"type": "string"}, "port": {"type": "integer", "format": "int32"}, "proxyId": {"type": "integer", "format": "int64"}, "proxyType": {"type": "string"}, "username": {"type": "string"}, "whitelist": {"type": "array", "items": {"type": "string"}}, "whitelistEnabled": {"type": "boolean"}}}, "StopProxyForwardVo": {"title": "StopProxyForwardVo", "type": "object", "properties": {"ports": {"type": "array", "items": {"type": "integer", "format": "int32"}}}}, "SwitchBatchProxyResult": {"title": "SwitchBatchProxyResult", "type": "object", "properties": {"resultMap": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/SwitchProxyResult"}}}}, "SwitchProxyResult": {"title": "SwitchProxyResult", "type": "object", "properties": {"failError": {"type": "string"}, "probeTransitId": {"type": "integer", "format": "int64"}, "probeUrl": {"type": "string"}, "proxyConfig": {"$ref": "#/components/schemas/ProxyConfig"}, "resultMap": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/ProxyProbeWithRemoteIp"}}, "socks": {"$ref": "#/components/schemas/IpSocksDto"}, "success": {"type": "boolean"}}}, "TaskDto": {"title": "TaskDto", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "detail": {"type": "string"}, "finishTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "progress": {"type": "integer", "format": "int32"}, "remarks": {"type": "string"}, "status": {"type": "string", "enum": ["Abort", "Fail", "Pending", "Running", "Success", "Timeout"]}, "targetId": {"type": "integer", "format": "int64"}, "taskType": {"type": "string", "enum": ["BatchUpdateBandwidth", "CleanColdTable", "CleanMongo", "CleanTable", "CopyTkCreatorToClient", "CrsTransferOrder", "DbTransfer", "DeleteIps", "FireOpsMessage", "ImportAccounts", "ImportIp", "ImportIppIps", "ImportIps", "ImportShop", "OpenaiChatGenerate", "OpenaiChatTranslate", "ProbeBatchLaunchInstance", "ProbeIps", "RebootIp", "RefreshClash", "RefreshExtensions", "RepairGhLiveTime", "RepairKolLiveRate", "RepairKolLiveTime", "RepairOps", "RepairTkCreatorFollower", "ResetJdEip", "ReviseIpHostLocation", "ShardTableOps", "ShardTeamTableSql", "SshChangePort", "SshCommands", "SshCommandsBatchLaunchInstance", "SyncKolCreator", "SyncKolRegionMap", "TkSendEmail", "TransferShardTable", "TransferTable", "TransferTagResource", "TransferTkCreator", "UpgradeGhMessage", "UploadAiKnowledge", "UploadDiskFile", "UserRefreshIp"]}, "teamId": {"type": "integer", "format": "int64"}}}, "TeamTypeChangedRequest": {"title": "TeamTypeChangedRequest", "type": "object", "properties": {"platformTypes": {"type": "array", "items": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "Ins", "<PERSON><PERSON><PERSON>", "Line", "TikTok", "Whatsapp", "Xiaohongshu", "<PERSON><PERSON>", "tkshop"]}}, "teamId": {"type": "integer", "format": "int64"}, "teamType": {"type": "string", "enum": ["crs", "gh", "krShop", "normal", "partner", "plugin", "tk", "tkshop"]}}}, "TransitConnectionInfo": {"title": "TransitConnectionInfo", "type": "object", "properties": {"domestic": {"type": "boolean"}, "endpoints": {"type": "string"}, "favorite": {"type": "boolean"}, "forceReconnect": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "ping": {"type": "integer", "format": "int32"}, "working": {"type": "boolean"}}}, "TransitGroupDto": {"title": "TransitGroupDto", "type": "object", "properties": {"autoAllocate": {"type": "boolean"}, "createTime": {"type": "string", "format": "date-time"}, "description": {"type": "string"}, "domestic": {"type": "boolean"}, "free": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "importType": {"type": "string", "enum": ["Platform", "User"]}, "jump": {"type": "boolean"}, "location": {"type": "string"}, "name": {"type": "string"}}}, "TunnelAddress": {"title": "TunnelAddress", "type": "object", "properties": {"identity": {"type": "string"}, "type": {"type": "string"}}}, "TunnelConnStat": {"title": "TunnelConnStat", "type": "object", "properties": {"clientDfSessionStat": {"$ref": "#/components/schemas/DynamicForwardStat"}, "peerTypeStatMap": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}}, "serverDfSessionStat": {"$ref": "#/components/schemas/DynamicForwardStat"}, "statMap": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/PeerConnStat"}}}}, "UpdateProxyForwardAuth": {"title": "UpdateProxyForwardAuth", "type": "object", "properties": {"auth": {"type": "boolean"}, "password": {"type": "string"}, "proxyId": {"type": "integer", "format": "int64"}, "username": {"type": "string"}, "whitelist": {"type": "array", "items": {"type": "string"}}, "whitelistEnabled": {"type": "boolean"}}}, "UserResourceVo": {"title": "UserResourceVo", "type": "object", "properties": {"hidden": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "resourceId": {"type": "integer", "format": "int64"}, "resourceType": {"type": "string", "enum": ["AK", "Activity", "Audit", "BlockElements", "Cloud", "CrsOrder", "CrsProduct", "DiskFile", "FingerPrint", "FingerPrintTemplate", "Gateway", "GhCreator", "GhGifter", "GhJobPlan", "GhUser", "GhVideoCreator", "GiftCardPack", "InsTeamUser", "InsUser", "Invoice", "Ip", "IpPool", "IppIp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "KakaoFriend", "KolCreator", "MobileAccount", "None", "Orders", "PluginTeamPack", "Record", "RpaFlow", "RpaTask", "RpaTaskItem", "RpaVoucher", "Shop", "ShopSession", "Tag", "TeamDiskRoot", "TeamMobile", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TkCreator", "TkTeamPack", "Tkshop", "<PERSON>ks<PERSON><PERSON>uyer", "TkshopCreator", "TrafficPack", "TunnelVps", "Users", "View", "Voucher", "XhsAccount"]}, "teamId": {"type": "integer", "format": "int64"}, "userId": {"type": "integer", "format": "int64"}}}, "UserVo": {"title": "UserVo", "type": "object", "properties": {"account": {"type": "string", "description": "账号"}, "avatar": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "email": {"type": "string", "description": "邮箱"}, "gender": {"type": "string", "enum": ["FEMALE", "MALE", "UNSPECIFIC"]}, "id": {"type": "integer", "format": "int64"}, "nickname": {"type": "string"}, "partnerId": {"type": "integer", "format": "int64"}, "phone": {"type": "string", "description": "手机"}, "residentCity": {"type": "string"}, "signature": {"type": "string"}, "status": {"type": "string", "enum": ["ACTIVE", "BLOCK", "DELETED", "INACTIVE"]}, "tenant": {"type": "integer", "format": "int64"}, "userType": {"type": "string", "enum": ["NORMAL", "PARTNER", "SHADOW"]}, "weixin": {"type": "string"}}}, "WebResult": {"title": "WebResult", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«ExecResult»": {"title": "WebResult«ExecResult»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/ExecResult"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«FileDbInfo»": {"title": "WebResult«FileDbInfo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/FileDbInfo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«ForceDisconnectResult»": {"title": "WebResult«ForceDisconnectResult»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/ForceDisconnectResult"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«GitInfoVo»": {"title": "WebResult«GitInfoVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/GitInfoVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«IpLocationResultVo»": {"title": "WebResult«IpLocationResultVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/IpLocationResultVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«GatewayTemplateDto»»": {"title": "WebResult«List«GatewayTemplateDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/GatewayTemplateDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«TunnelAddress»»": {"title": "WebResult«List«TunnelAddress»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TunnelAddress"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«Map«string,string»»": {"title": "WebResult«Map«string,string»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object", "additionalProperties": {"type": "string"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«Map»": {"title": "WebResult«Map»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«MicroServiceInfoVo»": {"title": "WebResult«MicroServiceInfoVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/MicroServiceInfoVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«ProbeResult»": {"title": "WebResult«ProbeResult»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/ProbeResult"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«ProduceIpResultVo»": {"title": "WebResult«ProduceIpResultVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/ProduceIpResultVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«ProduceIpSpecVo»": {"title": "WebResult«ProduceIpSpecVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/ProduceIpSpecVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«ProxyProbeWithRemoteIp»": {"title": "WebResult«ProxyProbeWithRemoteIp»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/ProxyProbeWithRemoteIp"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«ProxyTemplateVo»": {"title": "WebResult«ProxyTemplateVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/ProxyTemplateVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«ProxyTokenVo»": {"title": "WebResult«ProxyTokenVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/ProxyTokenVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«QQWryInfo»": {"title": "WebResult«QQWryInfo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/QQWryInfo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«ReloadSmsProviderResultVo»": {"title": "WebResult«ReloadSmsProviderResultVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/ReloadSmsProviderResultVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«ResetIpLocationResultVo»": {"title": "WebResult«ResetIpLocationResultVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/ResetIpLocationResultVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«RpcConnectedPeers»": {"title": "WebResult«RpcConnectedPeers»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/RpcConnectedPeers"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«ShopTokenVo»": {"title": "WebResult«ShopTokenVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/ShopTokenVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«SpyVo»": {"title": "WebResult«SpyVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/SpyVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«StartProxyForwardResult»": {"title": "WebResult«StartProxyForwardResult»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/StartProxyForwardResult"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«SwitchBatchProxyResult»": {"title": "WebResult«SwitchBatchProxyResult»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/SwitchBatchProxyResult"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«SwitchProxyResult»": {"title": "WebResult«SwitchProxyResult»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/SwitchProxyResult"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TaskDto»": {"title": "WebResult«TaskDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TaskDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TransitConnectionInfo»": {"title": "WebResult«TransitConnectionInfo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TransitConnectionInfo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TunnelAddress»": {"title": "WebResult«TunnelAddress»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TunnelAddress"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TunnelConnStat»": {"title": "WebResult«TunnelConnStat»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TunnelConnStat"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«boolean»": {"title": "WebResult«boolean»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "boolean"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«int»": {"title": "WebResult«int»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«long»": {"title": "WebResult«long»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "integer", "format": "int64"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«string»": {"title": "WebResult«string»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "string"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}}}}
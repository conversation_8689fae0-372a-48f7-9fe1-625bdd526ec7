{"openapi": "3.0.3", "info": {"title": "Open API", "version": "App version 1.0,Spring Boot Version:2.7.14"}, "servers": [{"url": "http://dev.thinkoncloud.cn", "description": "Inferred Url"}], "tags": [{"name": "Openapi 管理API", "description": "Open Ak Controller"}], "paths": {"/api/openak/ak": {"post": {"tags": ["OpenAkController"], "summary": "创建AK", "operationId": "openakAkPost", "parameters": [{"name": "userId", "in": "query", "description": "userId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«OpenapiAkDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openak/ak/page": {"get": {"tags": ["OpenAkController"], "summary": "查询AK", "operationId": "openakAkPageGet", "parameters": [{"name": "creator", "in": "query", "description": "creator", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "userId", "in": "query", "description": "userId", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "id", "in": "query", "description": "id", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "accessKeyId", "in": "query", "description": "accessKeyId", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "status", "in": "query", "description": "status", "required": false, "style": "form", "schema": {"type": "string", "enum": ["Deleted", "Disabled", "Enabled"]}}, {"name": "createTimeFrom", "in": "query", "description": "createTimeFrom", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "createTimeTo", "in": "query", "description": "createTimeTo", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "pageNum", "in": "query", "description": "pageNum", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«OpenapiAkDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openak/ak/{akId}": {"get": {"tags": ["OpenAkController"], "summary": "查看AK详情", "operationId": "openakAkByAkIdGet", "parameters": [{"name": "akId", "in": "path", "description": "akId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«OpenapiAkDto»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openak/ak/{akId}/status": {"put": {"tags": ["OpenAkController"], "summary": "修改AK状态", "operationId": "openakAkByAkIdStatusPut", "parameters": [{"name": "akId", "in": "path", "description": "akId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "status", "in": "query", "description": "status", "required": true, "style": "form", "schema": {"type": "string", "enum": ["Deleted", "Disabled", "Enabled"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openak/openapi/config": {"get": {"tags": ["OpenAkController"], "summary": "获取当前团队openapi配置", "operationId": "openakOpenapiConfigGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TeamOpenapiDto»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openak/openapi/currentRate": {"get": {"tags": ["OpenAkController"], "summary": "获取当前团队在当前周期内使用Openapi次数", "operationId": "openakOpenapiCurrentRateGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«long»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/openak/openapi/open": {"put": {"tags": ["OpenAkController"], "summary": "启用/禁用 Open API", "operationId": "openakOpenapiOpenPut", "parameters": [{"name": "enabled", "in": "query", "description": "enabled", "required": true, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}}, "components": {"schemas": {"OpenapiAkDto": {"title": "OpenapiAkDto", "type": "object", "properties": {"accessKeyId": {"type": "string"}, "accessKeySecret": {"type": "string"}, "akType": {"type": "string", "enum": ["Global", "Partner", "PortalTeamMember"]}, "createTime": {"type": "string", "format": "date-time"}, "creator": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "status": {"type": "string", "enum": ["Deleted", "Disabled", "Enabled"]}, "teamId": {"type": "integer", "format": "int64"}, "updateTime": {"type": "string", "format": "date-time"}, "userId": {"type": "integer", "format": "int64"}}}, "PageResult«OpenapiAkDto»": {"title": "PageResult«OpenapiAkDto»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/OpenapiAkDto"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "TeamOpenapiDto": {"title": "TeamOpenapiDto", "type": "object", "properties": {"enabled": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "rate": {"type": "integer", "format": "int32"}, "rateControl": {"type": "boolean"}, "rateType": {"type": "string", "enum": ["day", "hour", "minute", "month", "second", "week", "year"]}}}, "WebResult": {"title": "WebResult", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«OpenapiAkDto»": {"title": "WebResult«OpenapiAkDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/OpenapiAkDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«OpenapiAkDto»»": {"title": "WebResult«PageResult«OpenapiAkDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«OpenapiAkDto»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TeamOpenapiDto»": {"title": "WebResult«TeamOpenapiDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TeamOpenapiDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«long»": {"title": "WebResult«long»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "integer", "format": "int64"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}}, "securitySchemes": {"Authorization": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}, "x-dm-team-id": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-dm-team-id", "in": "header"}}}}
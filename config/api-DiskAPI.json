{"openapi": "3.0.3", "info": {"title": "Disk API", "version": "App version 1.0,Spring Boot Version:2.7.14"}, "servers": [{"url": "http://dev.thinkoncloud.cn", "description": "Inferred Url"}], "tags": [{"name": "存储空间API", "description": "Disk Space Controller"}, {"name": "网盘API", "description": "Disk Controller"}], "paths": {"/api/disk/checkPathGranted": {"get": {"tags": ["DiskController"], "summary": "判断是否对路径有权限", "operationId": "diskCheckPathGrantedGet", "parameters": [{"name": "path", "in": "query", "description": "path", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«boolean»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/disk/collect": {"post": {"tags": ["DiskController"], "summary": "收藏文件（夹）", "operationId": "diskCollectPost", "parameters": [{"name": "diskType", "in": "query", "description": "diskType", "required": true, "style": "form", "schema": {"type": "string", "enum": ["LocalDisk", "TeamDisk", "UserDisk"]}}, {"name": "name", "in": "query", "description": "name", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/disk/copy": {"post": {"tags": ["DiskController"], "summary": "文件（夹）", "operationId": "diskCopyPost", "parameters": [{"name": "sourceDiskType", "in": "query", "description": "sourceDiskType", "required": true, "style": "form", "schema": {"type": "string", "enum": ["LocalDisk", "TeamDisk", "UserDisk"]}}, {"name": "targetDiskType", "in": "query", "description": "targetDiskType", "required": true, "style": "form", "schema": {"type": "string", "enum": ["LocalDisk", "TeamDisk", "UserDisk"]}}, {"name": "srcName", "in": "query", "description": "srcName", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "targetName", "in": "query", "description": "targetName", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/disk/directory": {"post": {"tags": ["DiskController"], "summary": "创建目录（无任务）", "operationId": "diskDirectoryPost", "parameters": [{"name": "name", "in": "query", "description": "相对网盘根目录的目录名", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "diskType", "in": "query", "description": "diskType", "required": true, "style": "form", "schema": {"type": "string", "enum": ["LocalDisk", "TeamDisk", "UserDisk"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«CreateFileResult»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/disk/favorites/page": {"get": {"tags": ["DiskController"], "summary": "分页查询收藏列表", "operationId": "diskFavoritesPageGet", "parameters": [{"name": "collectTimeFrom", "in": "query", "description": "collectTimeFrom", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "collectTimeTo", "in": "query", "description": "collectTimeTo", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "query", "in": "query", "description": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "sortField", "in": "query", "description": "sortField", "required": false, "style": "form", "schema": {"type": "string", "enum": ["collectTime", "id", "name", "size"]}}, {"name": "sortOrder", "in": "query", "description": "sortOrder", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}, {"name": "pageNum", "in": "query", "description": "pageNum", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«FavoriteDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/disk/file": {"post": {"tags": ["DiskController"], "summary": "创建文件", "operationId": "diskFilePost", "parameters": [{"name": "name", "in": "query", "description": "相对网盘根目录的文件名", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "mineType", "in": "query", "description": "mineType", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "diskType", "in": "query", "description": "diskType", "required": true, "style": "form", "schema": {"type": "string", "enum": ["LocalDisk", "TeamDisk", "UserDisk"]}}, {"name": "size", "in": "query", "description": "size", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "confirm", "in": "query", "description": "confirm", "required": false, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«CreateFileResult»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/disk/file/{fileId}/finished": {"put": {"tags": ["DiskController"], "summary": "更新上传文件的状态", "operationId": "diskFileByFileIdFinishedPut", "parameters": [{"name": "fileId", "in": "path", "description": "fileId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "status", "in": "query", "description": "status", "required": true, "style": "form", "schema": {"type": "string", "enum": ["Abort", "Fail", "Pending", "Running", "Success", "Timeout"]}}, {"name": "remarks", "in": "query", "description": "remarks", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/disk/fileOrDirectory": {"delete": {"tags": ["DiskController"], "summary": "删除文件或目录（会递归删除子目录和文件）", "operationId": "diskFileOrDirectoryDelete", "parameters": [{"name": "name", "in": "query", "description": "相对网盘根目录的目录名或文件名", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "diskType", "in": "query", "description": "diskType", "required": true, "style": "form", "schema": {"type": "string", "enum": ["LocalDisk", "TeamDisk", "UserDisk"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/disk/move": {"post": {"tags": ["DiskController"], "summary": "移动文件（夹）", "operationId": "diskMovePost", "parameters": [{"name": "sourceDiskType", "in": "query", "description": "sourceDiskType", "required": true, "style": "form", "schema": {"type": "string", "enum": ["LocalDisk", "TeamDisk", "UserDisk"]}}, {"name": "targetDiskType", "in": "query", "description": "targetDiskType", "required": true, "style": "form", "schema": {"type": "string", "enum": ["LocalDisk", "TeamDisk", "UserDisk"]}}, {"name": "srcName", "in": "query", "description": "srcName", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "targetName", "in": "query", "description": "targetName", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/disk/preSignUrl": {"get": {"tags": ["DiskController"], "summary": "获取下载签名URL", "operationId": "diskPreSignUrlGet", "parameters": [{"name": "diskType", "in": "query", "description": "diskType", "required": true, "style": "form", "schema": {"type": "string", "enum": ["LocalDisk", "TeamDisk", "UserDisk"]}}, {"name": "name", "in": "query", "description": "name", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/disk/prepareName": {"get": {"tags": ["DiskController"], "summary": "查询可用的上传路径", "operationId": "diskPrepareNameGet", "parameters": [{"name": "diskType", "in": "query", "description": "diskType", "required": true, "style": "form", "schema": {"type": "string", "enum": ["LocalDisk", "TeamDisk", "UserDisk"]}}, {"name": "name", "in": "query", "description": "name", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/disk/team/grantToUsers": {"post": {"tags": ["DiskController"], "summary": "团队网盘根目录授权给用户", "operationId": "diskTeamGrantToUsersPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DiskRootGrant2UsersRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/disk/team/list": {"get": {"tags": ["DiskController"], "summary": "列出团队网盘中的文件", "operationId": "diskTeamListGet", "parameters": [{"name": "nextM<PERSON><PERSON>", "in": "query", "description": "nextM<PERSON><PERSON>", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "path", "in": "query", "description": "path", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "limit", "in": "query", "description": "limit", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«OssListResult»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/disk/team/root": {"get": {"tags": ["DiskController"], "summary": "获取团队网盘根目录的详情", "operationId": "diskTeamRootGet", "parameters": [{"name": "path", "in": "query", "description": "path", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TeamDiskRootVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/disk/team/signature": {"get": {"tags": ["DiskController"], "summary": "获取团队网盘签名", "operationId": "diskTeamSignatureGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«DiskStsVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/disk/triggerChanged": {"get": {"tags": ["DiskController"], "summary": "客户端出发一个网盘路径修改事件", "operationId": "diskTriggerChangedGet", "parameters": [{"name": "path", "in": "query", "description": "path", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "eventType", "in": "query", "description": "eventType", "required": true, "style": "form", "schema": {"type": "string", "enum": ["Created", "Deleted", "Modified"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/disk/user/signature": {"get": {"tags": ["DiskController"], "summary": "获取个人网盘签名", "operationId": "diskUserSignatureGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«DiskStsVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/disk/diskSpacePriceInfo": {"get": {"tags": ["DiskSpaceController"], "summary": "getDiskSpacePriceInfo", "operationId": "diskDiskSpacePriceInfoGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«DiskSpacePriceVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/disk/getFileKeepDays": {"get": {"tags": ["DiskSpaceController"], "summary": "获取团队录像和rpa文件保留天数", "operationId": "diskGetFileKeepDaysGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«FileKeepDaysVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/disk/spaceGrid": {"get": {"tags": ["DiskSpaceController"], "summary": "空间分布", "operationId": "diskSpaceGridGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«DiskTreeNode»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/disk/spaceStat": {"get": {"tags": ["DiskSpaceController"], "summary": "使用空间概览", "operationId": "diskSpaceStatGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«DiskStatVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/disk/top10Files": {"get": {"tags": ["DiskSpaceController"], "summary": "返回10大文件", "operationId": "diskTop10FilesGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«DiskFileItem»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/disk/updateFileKeepDays": {"put": {"tags": ["DiskSpaceController"], "summary": "设置团队录像和rpa文件保留天数", "operationId": "diskUpdateFileKeepDaysPut", "parameters": [{"name": "recordKeepDays", "in": "query", "description": "recordKeepDays", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "rpaKeepDays", "in": "query", "description": "rpaKeepDays", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/disk/updateStorageAlert": {"put": {"tags": ["DiskSpaceController"], "summary": "修改云盘存储空间告警", "operationId": "diskUpdateStorageAlertPut", "parameters": [{"name": "storageAlertCredit", "in": "query", "description": "storageAlertCredit", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}}, "components": {"schemas": {"CreateFileResult": {"title": "CreateFileResult", "type": "object", "properties": {"exists": {"type": "boolean"}, "fileId": {"type": "integer", "format": "int64"}, "filePath": {"type": "string"}, "userHeaders": {"type": "object", "additionalProperties": {"type": "string"}}}}, "DepartmentDto": {"title": "DepartmentDto", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "hidden": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "invitingAuditEnabled": {"type": "boolean"}, "invitingEnabled": {"type": "boolean"}, "name": {"type": "string"}, "parentId": {"type": "integer", "format": "int64"}, "sortNumber": {"type": "integer", "format": "int32"}, "teamId": {"type": "integer", "format": "int64"}}}, "DiskFileItem": {"title": "DiskFileItem", "type": "object", "properties": {"category": {"type": "string", "enum": ["RecordSlice", "RpaFile", "ShopData", "TeamDisk", "UserDisk"]}, "filePath": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "lastModified": {"type": "string", "format": "date-time"}, "name": {"type": "string"}, "size": {"type": "integer", "format": "int64"}}}, "DiskRootGrant2UsersRequest": {"title": "DiskRootGrant2UsersRequest", "type": "object", "properties": {"cleanFirst": {"type": "boolean", "description": "清除网盘文件已有的用户与组织单元授权关系", "example": false}, "confirm": {"type": "boolean"}, "departmentIdList": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "files": {"type": "array", "items": {"type": "string"}}, "userIdList": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "DiskSpacePriceVo": {"title": "DiskSpacePriceVo", "type": "object", "properties": {"creditYesterday": {"type": "integer", "description": "昨日消耗花瓣", "format": "int32"}, "price": {"type": "number", "description": "单价,花瓣/GB*天", "format": "bigdecimal"}, "quotaTraffic": {"type": "number", "description": "免费配额，GB", "format": "double"}, "storageAlertCredit": {"type": "integer", "format": "int32"}}}, "DiskStatVo": {"title": "DiskStatVo", "type": "object", "properties": {"myUserDiskSize": {"type": "integer", "format": "int64"}, "otherSize": {"type": "integer", "format": "int64"}, "otherUserDiskSize": {"type": "integer", "format": "int64"}, "recordFileSize": {"type": "integer", "format": "int64"}, "rpaFileSize": {"type": "integer", "format": "int64"}, "shopDataSize": {"type": "integer", "format": "int64"}, "teamDiskSize": {"type": "integer", "format": "int64"}}}, "DiskStsVo": {"title": "DiskStsVo", "type": "object", "properties": {"baseDir": {"type": "string"}, "downloadEndpoint": {"type": "string"}, "stsToken": {"$ref": "#/components/schemas/StsPostSignature"}}}, "DiskTreeNode": {"title": "DiskTreeNode", "type": "object", "properties": {"fullPath": {"type": "string"}, "lastModified": {"type": "string", "format": "date-time"}, "name": {"type": "string"}, "size": {"type": "integer", "format": "int64"}, "subNodes": {"type": "array", "items": {}}}}, "FavoriteDto": {"title": "FavoriteDto", "type": "object", "properties": {"collectTime": {"type": "string", "format": "date-time"}, "collector": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "resourceId": {"type": "integer", "format": "int64"}, "resourceType": {"type": "string", "enum": ["AK", "Activity", "Audit", "BlockElements", "Cloud", "CrsOrder", "CrsProduct", "DiskFile", "FingerPrint", "FingerPrintTemplate", "Gateway", "GhCreator", "GhGifter", "GhJobPlan", "GhUser", "GhVideoCreator", "GiftCardPack", "InsTeamUser", "InsUser", "Invoice", "Ip", "IpPool", "IppIp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "KakaoFriend", "KolCreator", "MobileAccount", "None", "Orders", "PluginTeamPack", "Record", "RpaFlow", "RpaTask", "RpaTaskItem", "RpaVoucher", "Shop", "ShopSession", "Tag", "TeamDiskRoot", "TeamMobile", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TkCreator", "TkTeamPack", "Tkshop", "<PERSON>ks<PERSON><PERSON>uyer", "TkshopCreator", "TrafficPack", "TunnelVps", "Users", "View", "Voucher", "XhsAccount"]}, "size": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}, "url": {"type": "string"}}}, "FileKeepDaysVo": {"title": "FileKeepDaysVo", "type": "object", "properties": {"recordKeepDays": {"type": "integer", "format": "int32"}, "rpaKeepDays": {"type": "integer", "format": "int32"}, "teamId": {"type": "integer", "format": "int64"}}}, "MemberVo": {"title": "MemberVo", "type": "object", "properties": {"account": {"type": "string", "description": "账号"}, "avatar": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "email": {"type": "string", "description": "邮箱"}, "gender": {"type": "string", "enum": ["FEMALE", "MALE", "UNSPECIFIC"]}, "id": {"type": "integer", "format": "int64"}, "nickname": {"type": "string"}, "partnerId": {"type": "integer", "format": "int64"}, "phone": {"type": "string", "description": "手机"}, "residentCity": {"type": "string"}, "roleCode": {"type": "string", "enum": ["boss", "manager", "staff", "superadmin"]}, "signature": {"type": "string"}, "status": {"type": "string", "enum": ["ACTIVE", "BLOCK", "DELETED", "INACTIVE"]}, "teamNickname": {"type": "string"}, "tenant": {"type": "integer", "format": "int64"}, "userType": {"type": "string", "enum": ["NORMAL", "PARTNER", "SHADOW"]}, "weixin": {"type": "string"}}}, "OssListResult": {"title": "OssListResult", "type": "object", "properties": {"files": {"type": "array", "items": {"$ref": "#/components/schemas/StorageItemMeta"}}, "nextMarker": {"type": "string"}, "truncated": {"type": "boolean"}}}, "PageResult«FavoriteDto»": {"title": "PageResult«FavoriteDto»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/FavoriteDto"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "StorageItemMeta": {"title": "StorageItemMeta", "type": "object", "properties": {"broken": {"type": "boolean"}, "contentLength": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "lastModified": {"type": "string", "format": "date-time"}, "md5sum": {"type": "string"}, "path": {"type": "string"}, "userMetadata": {"type": "object", "additionalProperties": {"type": "string"}}}}, "StsPostSignature": {"title": "StsPostSignature", "type": "object", "properties": {"accessKeyId": {"type": "string"}, "accessKeySecret": {"type": "string"}, "bucketName": {"type": "string"}, "expiration": {"type": "string"}, "fileVal": {"type": "string"}, "policy": {"type": "string"}, "provider": {"type": "string"}, "region": {"type": "string"}, "securityToken": {"type": "string"}, "serverTime": {"type": "string", "format": "date-time"}, "url": {"type": "string"}}}, "TeamDiskRootVo": {"title": "TeamDiskRootVo", "type": "object", "properties": {"bucketId": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "filePath": {"type": "string"}, "folder": {"type": "boolean"}, "grantDepartmentList": {"type": "array", "description": "授权的部门", "items": {"$ref": "#/components/schemas/DepartmentDto"}}, "grantUserVoList": {"type": "array", "description": "授权给的用户", "items": {"$ref": "#/components/schemas/MemberVo"}}, "id": {"type": "integer", "format": "int64"}, "mineType": {"type": "string"}, "name": {"type": "string"}, "remarks": {"type": "string"}, "size": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}}}, "WebResult": {"title": "WebResult", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«CreateFileResult»": {"title": "WebResult«CreateFileResult»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/CreateFileResult"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«DiskSpacePriceVo»": {"title": "WebResult«DiskSpacePriceVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/DiskSpacePriceVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«DiskStatVo»": {"title": "WebResult«DiskStatVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/DiskStatVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«DiskStsVo»": {"title": "WebResult«DiskStsVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/DiskStsVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«FileKeepDaysVo»": {"title": "WebResult«FileKeepDaysVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/FileKeepDaysVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«DiskFileItem»»": {"title": "WebResult«List«DiskFileItem»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/DiskFileItem"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«DiskTreeNode»»": {"title": "WebResult«List«DiskTreeNode»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/DiskTreeNode"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«OssListResult»": {"title": "WebResult«OssListResult»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/OssListResult"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«FavoriteDto»»": {"title": "WebResult«PageResult«FavoriteDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«FavoriteDto»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TeamDiskRootVo»": {"title": "WebResult«TeamDiskRootVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TeamDiskRootVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«boolean»": {"title": "WebResult«boolean»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "boolean"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«string»": {"title": "WebResult«string»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "string"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}}, "securitySchemes": {"Authorization": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}, "x-dm-team-id": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-dm-team-id", "in": "header"}}}}
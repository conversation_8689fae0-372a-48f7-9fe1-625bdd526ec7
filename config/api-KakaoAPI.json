{"openapi": "3.0.3", "info": {"title": "Kakao API", "version": "App version 1.0,Spring Boot Version:2.7.14"}, "servers": [{"url": "http://dev.thinkoncloud.cn", "description": "Inferred Url"}], "tags": [{"name": "Kakao Account API", "description": "<PERSON><PERSON><PERSON> Account Controller"}, {"name": "Kakao Chat API", "description": "<PERSON><PERSON><PERSON>"}, {"name": "Kakao Friend API", "description": "Ka<PERSON>o Friend Controller"}], "paths": {"/api/gh/kakao/account/create": {"post": {"tags": ["KakaoAccountController"], "summary": "创建账号", "operationId": "ghKakaoAccountCreatePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/KakaoAccountDto"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«KakaoAccountDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/kakao/account/list": {"get": {"tags": ["KakaoAccountController"], "summary": "查询 kakao 账号（可见的）", "operationId": "ghKakaoAccountListGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«KakaoAccountDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/kakao/account/sync": {"post": {"tags": ["KakaoAccountController"], "summary": "同步（创建）kakao 账号", "operationId": "ghKakaoAccountSyncPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/KakaoSyncAccountRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«KakaoAccountDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/kakao/account/{id}": {"post": {"tags": ["KakaoAccountController"], "summary": "修改账号", "operationId": "ghKakaoAccountByIdPost", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/KakaoAccountDto"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«KakaoAccountDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/kakao/chat/page": {"get": {"tags": ["KakaoChatController"], "summary": "分页查询chat列表", "operationId": "ghKakaoChatPageGet", "parameters": [{"name": "accountId", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "mobileId", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "pageNum", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "sortField", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "sortOrder", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«KakaoChatDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/kakao/chat/syncBatch": {"post": {"tags": ["KakaoChatController"], "summary": "批量同步（创建）kakao chats", "operationId": "ghKakaoChatSyncBatchPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/KakaoBatchSyncChatRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«KakaoChatDto»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/kakao/chat/{id}": {"get": {"tags": ["KakaoChatController"], "summary": "查询chat详情", "operationId": "ghKakaoChatByIdGet", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«KakaoChatDetailVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/kakao/chat/{id}/pageMessage": {"get": {"tags": ["KakaoChatController"], "summary": "分页查询chat message", "operationId": "ghKakaoChatByIdPageMessageGet", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "pageNum", "in": "query", "description": "pageNum", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«KakaoChatMessageDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/kakao/friend/create": {"post": {"tags": ["KakaoFriendController"], "summary": "创建Kakao联系人", "operationId": "ghKakaoFriendCreatePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateKakaoFriendVo"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«KakaoFriendDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/kakao/friend/delete": {"post": {"tags": ["KakaoFriendController"], "summary": "批量删除 联系人", "operationId": "ghKakaoFriendDeletePost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonIdsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«int»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/kakao/friend/page": {"get": {"tags": ["KakaoFriendController"], "summary": "分页查询kakao friend", "operationId": "ghKakaoFriendPageGet", "parameters": [{"name": "accountId", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "filterByFavorite", "in": "query", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "lastSyncTimeFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "lastSyncTimeTo", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "mobileId", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "pageNum", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "query", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "sortField", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "sortOrder", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}, {"name": "tagIds", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "tagLc", "in": "query", "description": "标签的逻辑条件，支持OR|AND", "required": false, "style": "form", "schema": {"type": "string", "enum": ["AND", "NOT", "OR"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«KakaoFriendDetailVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/kakao/friend/syncBatch": {"post": {"tags": ["KakaoFriendController"], "summary": "批量同步（创建）kakao friend", "operationId": "ghKakaoFriendSyncBatchPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/KakaoBatchSyncFriendRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«KakaoFriendDto»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/gh/kakao/friend/{id}": {"get": {"tags": ["KakaoFriendController"], "summary": "获取Kakao联系人", "operationId": "ghKakaoFriendByIdGet", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«KakaoFriendDetailVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "put": {"tags": ["KakaoFriendController"], "summary": "修改Kakao联系人", "operationId": "ghKakaoFriendByIdPut", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/KakaoFriendDto"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«KakaoFriendDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}}, "components": {"schemas": {"CommonIdsRequest": {"title": "CommonIdsRequest", "type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "CreateKakaoFriendVo": {"title": "CreateKakaoFriendVo", "type": "object", "properties": {"accountId": {"type": "integer", "format": "int64"}, "address": {"type": "string"}, "areaCode": {"type": "string"}, "avatar": {"type": "string"}, "consignee": {"type": "string"}, "consigneePcc": {"type": "string"}, "countryCode": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "description": {"type": "string"}, "email": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "mobileId": {"type": "integer", "format": "int64"}, "nickname": {"type": "string"}, "phone": {"type": "string"}, "postcode": {"type": "string"}, "tags": {"type": "array", "items": {"type": "string"}}, "teamId": {"type": "integer", "format": "int64"}}}, "KakaoAccountDto": {"title": "KakaoAccountDto", "type": "object", "properties": {"account": {"type": "string"}, "address": {"type": "string"}, "areaCode": {"type": "string"}, "avatar": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "description": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "lastSyncChatTime": {"type": "string", "format": "date-time"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "mobileId": {"type": "integer", "format": "int64"}, "nickname": {"type": "string"}, "phone": {"type": "string"}, "postcode": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}, "valid": {"type": "boolean"}}}, "KakaoBatchSyncChatRequest": {"title": "KakaoBatchSyncChatRequest", "type": "object", "properties": {"chats": {"type": "array", "items": {"$ref": "#/components/schemas/KakaoChatDocument"}}, "mobileId": {"type": "integer", "format": "int64"}}}, "KakaoBatchSyncFriendRequest": {"title": "KakaoBatchSyncFriendRequest", "type": "object", "properties": {"friends": {"type": "array", "items": {"$ref": "#/components/schemas/KakaoFriendDocument"}}, "mobileId": {"type": "integer", "format": "int64"}}}, "KakaoChatDetailVo": {"title": "KakaoChatDetailVo", "type": "object", "properties": {"accountId": {"type": "integer", "format": "int64"}, "avatar": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "friendId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "lastChatTime": {"type": "string", "format": "date-time"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "lastText": {"type": "string"}, "members": {"type": "array", "description": "参与人", "items": {"$ref": "#/components/schemas/KakaoFriendDto"}}, "mobileId": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "room": {"type": "boolean"}, "teamId": {"type": "integer", "format": "int64"}}}, "KakaoChatDocument": {"title": "KakaoChatDocument", "type": "object", "properties": {"avatar": {"type": "string", "description": "chat头像URL"}, "avatarBase64": {"type": "string", "description": "头像Base64"}, "avatarImageExt": {"type": "string", "description": "头像扩展名"}, "lastChatTime": {"type": "integer", "description": "最后聊天时间", "format": "int64"}, "lastText": {"type": "string", "description": "最后的消息文本"}, "messages": {"type": "array", "description": "聊天室的消息", "items": {"$ref": "#/components/schemas/KakaoChatMessageDocument"}}, "name": {"type": "string", "description": "chat名称，必填，不重复"}, "room": {"type": "boolean", "description": "是否聊天室,必填", "example": false}}}, "KakaoChatDto": {"title": "KakaoChatDto", "type": "object", "properties": {"accountId": {"type": "integer", "format": "int64"}, "avatar": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "friendId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "lastChatTime": {"type": "string", "format": "date-time"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "lastText": {"type": "string"}, "mobileId": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "room": {"type": "boolean"}, "teamId": {"type": "integer", "format": "int64"}}}, "KakaoChatMessageDocument": {"title": "KakaoChatMessageDocument", "type": "object", "properties": {"content": {"type": "string", "description": "发送内容"}, "mine": {"type": "boolean", "description": "是我发送还是其他成员发送，必填", "example": false}, "sendTime": {"type": "integer", "description": "发送时间戳,必填", "format": "int64"}, "sender": {"type": "string", "description": "发送者的昵称，mine=false时必填"}}}, "KakaoChatMessageDto": {"title": "KakaoChatMessageDto", "type": "object", "properties": {"chatId": {"type": "integer", "format": "int64"}, "content": {"type": "string"}, "friendId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "kakaoMessageType": {"type": "string", "enum": ["text"]}, "lastSyncTime": {"type": "string", "format": "date-time"}, "mine": {"type": "boolean"}, "sendTime": {"type": "string", "format": "date-time"}, "sender": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}}}, "KakaoFriendDetailVo": {"title": "KakaoFriendDetailVo", "type": "object", "properties": {"accountId": {"type": "integer", "format": "int64"}, "address": {"type": "string"}, "areaCode": {"type": "string"}, "avatar": {"type": "string"}, "consignee": {"type": "string"}, "consigneePcc": {"type": "string"}, "countryCode": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "description": {"type": "string"}, "email": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "mobileId": {"type": "integer", "format": "int64"}, "nickname": {"type": "string"}, "phone": {"type": "string"}, "postcode": {"type": "string"}, "tags": {"type": "array", "items": {"$ref": "#/components/schemas/TagDto"}}, "teamId": {"type": "integer", "format": "int64"}}}, "KakaoFriendDocument": {"title": "KakaoFriendDocument", "type": "object", "properties": {"areaCode": {"type": "string", "description": "手机区号"}, "avatar": {"type": "string", "description": "头像URL"}, "avatarBase64": {"type": "string", "description": "头像Base64"}, "avatarImageExt": {"type": "string", "description": "头像扩展名"}, "email": {"type": "string", "description": "邮箱"}, "nickname": {"type": "string", "description": "昵称，必填，确保唯一"}, "phone": {"type": "string", "description": "手机号"}}}, "KakaoFriendDto": {"title": "KakaoFriendDto", "type": "object", "properties": {"accountId": {"type": "integer", "format": "int64"}, "address": {"type": "string"}, "areaCode": {"type": "string"}, "avatar": {"type": "string"}, "consignee": {"type": "string"}, "consigneePcc": {"type": "string"}, "countryCode": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "description": {"type": "string"}, "email": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "mobileId": {"type": "integer", "format": "int64"}, "nickname": {"type": "string"}, "phone": {"type": "string"}, "postcode": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}}}, "KakaoSyncAccountRequest": {"title": "KakaoSyncAccountRequest", "type": "object", "properties": {"areaCode": {"type": "string", "description": "手机区号"}, "avatar": {"type": "string", "description": "头像URL"}, "avatarBase64": {"type": "string", "description": "头像Base64"}, "avatarImageExt": {"type": "string", "description": "头像扩展名"}, "description": {"type": "string"}, "mobileId": {"type": "integer", "description": "所属手机ID", "format": "int64"}, "nickname": {"type": "string", "description": "昵称（也是账号）"}, "phone": {"type": "string", "description": "手机号，全部（包括区号）"}}}, "PageResult«KakaoChatDto»": {"title": "PageResult«KakaoChatDto»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/KakaoChatDto"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«KakaoChatMessageDto»": {"title": "PageResult«KakaoChatMessageDto»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/KakaoChatMessageDto"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«KakaoFriendDetailVo»": {"title": "PageResult«KakaoFriendDetailVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/KakaoFriendDetailVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "TagDto": {"title": "TagDto", "type": "object", "properties": {"bizCode": {"type": "string"}, "color": {"type": "integer", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}, "expireTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "resourceType": {"type": "string", "enum": ["AK", "Activity", "Audit", "BlockElements", "Cloud", "CrsOrder", "CrsProduct", "DiskFile", "FingerPrint", "FingerPrintTemplate", "Gateway", "GhCreator", "GhGifter", "GhJobPlan", "GhUser", "GhVideoCreator", "GiftCardPack", "InsTeamUser", "InsUser", "Invoice", "Ip", "IpPool", "IppIp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "KakaoFriend", "KolCreator", "MobileAccount", "None", "Orders", "PluginTeamPack", "Record", "RpaFlow", "RpaTask", "RpaTaskItem", "RpaVoucher", "Shop", "ShopSession", "Tag", "TeamDiskRoot", "TeamMobile", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TkCreator", "TkTeamPack", "Tkshop", "<PERSON>ks<PERSON><PERSON>uyer", "TkshopCreator", "TrafficPack", "TunnelVps", "Users", "View", "Voucher", "XhsAccount"]}, "system": {"type": "boolean"}, "tag": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}}}, "WebResult«KakaoAccountDto»": {"title": "WebResult«KakaoAccountDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/KakaoAccountDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«KakaoChatDetailVo»": {"title": "WebResult«KakaoChatDetailVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/KakaoChatDetailVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«KakaoFriendDetailVo»": {"title": "WebResult«KakaoFriendDetailVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/KakaoFriendDetailVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«KakaoFriendDto»": {"title": "WebResult«KakaoFriendDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/KakaoFriendDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«KakaoAccountDto»»": {"title": "WebResult«List«KakaoAccountDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/KakaoAccountDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«KakaoChatDto»»": {"title": "WebResult«List«KakaoChatDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/KakaoChatDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«KakaoFriendDto»»": {"title": "WebResult«List«KakaoFriendDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/KakaoFriendDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«KakaoChatDto»»": {"title": "WebResult«PageResult«KakaoChatDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«KakaoChatDto»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«KakaoChatMessageDto»»": {"title": "WebResult«PageResult«KakaoChatMessageDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«KakaoChatMessageDto»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«KakaoFriendDetailVo»»": {"title": "WebResult«PageResult«KakaoFriendDetailVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«KakaoFriendDetailVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«int»": {"title": "WebResult«int»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}}, "securitySchemes": {"Authorization": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}, "x-dm-team-id": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-dm-team-id", "in": "header"}}}}
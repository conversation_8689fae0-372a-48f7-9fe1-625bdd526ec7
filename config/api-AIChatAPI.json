{"openapi": "3.0.3", "info": {"title": "AI Chat API", "version": "App version 1.0,Spring Boot Version:2.7.14"}, "servers": [{"url": "http://dev.thinkoncloud.cn", "description": "Inferred Url"}], "tags": [{"name": "AI对话", "description": "Conversation Controller"}, {"name": "AI对话/语音识别", "description": "Transcription Controller"}], "paths": {"/api/ai/conversation/cancelChat": {"put": {"tags": ["ConversationController"], "summary": "取消一个聊天回复", "operationId": "aiConversationCancelChatPut", "parameters": [{"name": "chatJobId", "in": "query", "description": "chatJobId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ai/conversation/conversation/{conversationId}": {"delete": {"tags": ["ConversationController"], "summary": "清空对话的历史记录", "operationId": "aiConversationConversationByConversationIdDelete", "parameters": [{"name": "conversationId", "in": "path", "description": "conversationId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ai/conversation/findConversation": {"get": {"tags": ["ConversationController"], "summary": "获取对话信息", "operationId": "aiConversationFindConversationGet", "parameters": [{"name": "conversationType", "in": "query", "description": "conversationType", "required": true, "style": "form", "schema": {"type": "string", "enum": ["helper", "rpa_editor", "visitor_chat"]}}, {"name": "resourceId", "in": "query", "description": "有些对话是绑定到特定的资源上的，如rpa编辑器助理绑定了流程id", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«AiConversationDto»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ai/conversation/message/{messageId}": {"delete": {"tags": ["ConversationController"], "summary": "删除消息", "operationId": "aiConversationMessageByMessageIdDelete", "parameters": [{"name": "messageId", "in": "path", "description": "messageId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ai/conversation/visitor/cancelChat": {"put": {"tags": ["ConversationController"], "summary": "访客取消一个聊天回复", "operationId": "aiConversationVisitorCancelChatPut", "parameters": [{"name": "chatJobId", "in": "query", "description": "chatJobId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ai/conversation/visitor/findConversation": {"get": {"tags": ["ConversationController"], "summary": "网站访客获取对话信息。调用之前请确保cookie: toc-tg-visitor 已经存在，否则会抛异常", "operationId": "aiConversationVisitorFindConversationGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«AiConversationDto»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ai/conversation/visitor/{conversationId}/chat/stream": {"get": {"tags": ["ConversationController"], "summary": "网站访客发送消息，生成回复。调用之前请确保cookie: toc-tg-visitor 已经存在，否则会抛异常", "operationId": "aiConversationVisitorByConversationIdChatStreamGet", "parameters": [{"name": "conversationId", "in": "path", "description": "conversationId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "content", "in": "query", "description": "消息内容", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SseEmitter"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ai/conversation/visitor/{conversationId}/messages": {"get": {"tags": ["ConversationController"], "summary": "获取某个网站访客对话的消息列表。调用之前请确保cookie: toc-tg-visitor 已经存在，否则会抛异常", "operationId": "aiConversationVisitorByConversationIdMessagesGet", "parameters": [{"name": "conversationId", "in": "path", "description": "conversationId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "lastMsgId", "in": "query", "description": "拉取该消息之前的@limit条消息，为空表示拉最新的", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "limit", "in": "query", "description": "拉取多少条消息", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«AiConversationMsgDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ai/conversation/{conversationId}/chat/stream": {"get": {"tags": ["ConversationController"], "summary": "streamChat", "operationId": "aiConversationByConversationIdChatStreamGet", "parameters": [{"name": "conversationId", "in": "path", "description": "conversationId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "content", "in": "query", "description": "消息内容", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/event-stream": {"schema": {"$ref": "#/components/schemas/SseEmitter"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ai/conversation/{conversationId}/messages": {"get": {"tags": ["ConversationController"], "summary": "获取某个对话的消息列表", "operationId": "aiConversationByConversationIdMessagesGet", "parameters": [{"name": "conversationId", "in": "path", "description": "conversationId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "lastMsgId", "in": "query", "description": "拉取该消息之前的@limit条消息，为空表示拉最新的", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "limit", "in": "query", "description": "拉取多少条消息", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«AiConversationMsgDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ai/conversation/{conversationId}/rpaChat/stream": {"get": {"tags": ["ConversationController"], "summary": "streamRpaChat", "operationId": "aiConversationByConversationIdRpaChatStreamGet", "parameters": [{"name": "conversationId", "in": "path", "description": "conversationId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "content", "in": "query", "description": "消息内容", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/event-stream": {"schema": {"$ref": "#/components/schemas/SseEmitter"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ai/transcription/getAliNlsConfig": {"get": {"tags": ["TranscriptionController"], "summary": "获取实现阿里云实时语音识别的配置", "operationId": "aiTranscriptionGetAliNlsConfigGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«AliNlsDto»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ai/transcription/getXFAsrConfig": {"get": {"tags": ["TranscriptionController"], "summary": "获取实现讯飞语音识别的配置", "operationId": "aiTranscriptionGetXFAsrConfigGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«XFAsrConfig»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ai/transcription/transcribe": {"post": {"tags": ["TranscriptionController"], "summary": "语音识别(使用的是openai的接口)", "operationId": "aiTranscriptionTranscribePost", "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["audio"], "type": "object", "properties": {"audio": {"type": "string", "description": "audio", "format": "binary"}}}}}}, "responses": {"200": {"description": "OK", "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ai/transcription/visitor/getXFAsrConfig": {"get": {"tags": ["TranscriptionController"], "summary": "网站访客获取实现讯飞语音识别的配置", "operationId": "aiTranscriptionVisitorGetXFAsrConfigGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«XFAsrConfig»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}}, "components": {"schemas": {"AiConversationDto": {"title": "AiConversationDto", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "lastInteractTime": {"type": "string", "format": "date-time"}, "limitChat": {"type": "boolean"}, "mask": {"type": "string"}, "resourceId": {"type": "integer", "format": "int64"}, "status": {"type": "string", "enum": ["banned", "closed", "normal"]}, "teamId": {"type": "integer", "format": "int64"}, "type": {"type": "string", "enum": ["helper", "rpa_editor", "visitor_chat"]}, "userId": {"type": "integer", "format": "int64"}}}, "AiConversationMsgDto": {"title": "AiConversationMsgDto", "type": "object", "properties": {"content": {"type": "string"}, "conversation": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "extraInfo": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "role": {"type": "string", "enum": ["assistant", "hy", "system", "user"]}, "sensitive": {"type": "boolean", "description": "该句话是否触发了敏感词", "example": false}, "teamId": {"type": "integer", "format": "int64"}, "type": {"type": "string", "enum": ["action", "error", "pre_action", "rpa_script", "text"]}, "userId": {"type": "integer", "format": "int64"}}}, "AliNlsDto": {"title": "AliNlsDto", "type": "object", "properties": {"appKey": {"type": "string"}, "expireTime": {"type": "integer", "description": "后台保证每次返回的token都至少有12个小时的有效期", "format": "int64"}, "token": {"type": "string"}, "url": {"type": "string"}}}, "Consumer«Throwable»": {"title": "Consumer«Throwable»", "type": "object"}, "DataWithMediaType": {"title": "DataWithMediaType", "type": "object", "properties": {"data": {"type": "object"}, "mediaType": {"$ref": "#/components/schemas/MediaType"}}}, "DefaultCallback": {"title": "De<PERSON>ult<PERSON><PERSON><PERSON>", "type": "object", "properties": {"delegate": {"$ref": "#/components/schemas/Runnable"}}}, "ErrorCallback": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "object", "properties": {"delegate": {"$ref": "#/components/schemas/Consumer«Throwable»"}}}, "Handler": {"title": "Handler", "type": "object"}, "MediaType": {"title": "MediaType", "type": "object", "properties": {"parameters": {"type": "object", "additionalProperties": {"type": "string"}}, "subtype": {"type": "string"}, "toStringValue": {"type": "string"}, "type": {"type": "string"}}}, "Runnable": {"title": "Runnable", "type": "object"}, "SseEmitter": {"title": "SseEmitter", "type": "object", "properties": {"complete": {"type": "boolean"}, "completionCallback": {"$ref": "#/components/schemas/DefaultCallback"}, "earlySendAttempts": {"uniqueItems": true, "type": "array", "items": {"$ref": "#/components/schemas/DataWithMediaType"}}, "errorCallback": {"$ref": "#/components/schemas/ErrorCallback"}, "failure": {"$ref": "#/components/schemas/Throwable"}, "handler": {"$ref": "#/components/schemas/Handler"}, "sendFailed": {"type": "boolean"}, "timeout": {"type": "integer", "format": "int64"}, "timeoutCallback": {"$ref": "#/components/schemas/DefaultCallback"}}}, "StackTraceElement": {"title": "StackTraceElement", "type": "object", "properties": {"classLoaderName": {"type": "string"}, "className": {"type": "string"}, "fileName": {"type": "string"}, "lineNumber": {"type": "integer", "format": "int32"}, "methodName": {"type": "string"}, "moduleName": {"type": "string"}, "moduleVersion": {"type": "string"}, "nativeMethod": {"type": "boolean"}}}, "Throwable": {"title": "Throwable", "type": "object", "properties": {"cause": {}, "localizedMessage": {"type": "string"}, "message": {"type": "string"}, "stackTrace": {"type": "array", "items": {"$ref": "#/components/schemas/StackTraceElement"}}, "suppressed": {"type": "array", "items": {}}}}, "WebResult": {"title": "WebResult", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«AiConversationDto»": {"title": "WebResult«AiConversationDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/AiConversationDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«AliNlsDto»": {"title": "WebResult«AliNlsDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/AliNlsDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«AiConversationMsgDto»»": {"title": "WebResult«List«AiConversationMsgDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/AiConversationMsgDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«XFAsrConfig»": {"title": "WebResult«XFAsrConfig»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/XFAsrConfig"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«string»": {"title": "WebResult«string»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "string"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "XFAsrConfig": {"title": "XFAsrConfig", "type": "object", "properties": {"appId": {"type": "string"}, "url": {"type": "string"}}}}, "securitySchemes": {"Authorization": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}, "x-dm-team-id": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-dm-team-id", "in": "header"}}}}
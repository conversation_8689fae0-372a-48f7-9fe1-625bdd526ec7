{"openapi": "3.0.3", "info": {"title": "Account API", "version": "App version 1.0,Spring Boot Version:2.7.14"}, "servers": [{"url": "http://dev.thinkoncloud.cn", "description": "Inferred Url"}], "tags": [{"name": "net-probe监控API", "description": "Net Probe Controller"}], "paths": {"/api/netprobe/commandResult/{id}": {"post": {"tags": ["NetProbeController"], "summary": "汇报结果", "operationId": "netprobeCommandResultByIdPost", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReportCommandResult"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/netprobe/fetchCommand/{id}": {"get": {"tags": ["NetProbeController"], "summary": "fetchCommand", "operationId": "netprobeFetchCommandByIdGet", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/netprobe/fetchProbe/{id}": {"get": {"tags": ["NetProbeController"], "summary": "fetch<PERSON>robe", "operationId": "netprobeFetchProbeByIdGet", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/netprobe/probeResult/{id}": {"post": {"tags": ["NetProbeController"], "summary": "汇报结果", "operationId": "netprobeProbeResultByIdPost", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReportProbeResult"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}}, "components": {"schemas": {"ReportCommandResult": {"title": "ReportCommandResult", "type": "object", "properties": {"error": {"type": "string"}, "executeTime": {"type": "integer", "format": "int32"}, "exitCode": {"type": "integer", "format": "int32"}, "output": {"type": "string"}, "status": {"type": "string", "enum": ["Cancelled", "Failed", "Pending", "Running", "Scheduled", "Succeed", "Timeout"]}}}, "ReportProbeResult": {"title": "ReportProbeResult", "type": "object", "properties": {"connectTime": {"type": "integer", "format": "int32"}, "downloadTime": {"type": "integer", "format": "int32"}, "error": {"type": "string"}, "size": {"type": "integer", "format": "int64"}, "status": {"type": "string", "enum": ["Cancelled", "Failed", "Pending", "Running", "Scheduled", "Succeed", "Timeout"]}}}, "WebResult": {"title": "WebResult", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}}, "securitySchemes": {"Authorization": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}, "x-dm-team-id": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-dm-team-id", "in": "header"}}}}
{"openapi": "3.0.3", "info": {"title": "Meta API", "version": "App version 1.0,Spring Boot Version:2.7.14"}, "servers": [{"url": "http://dev.thinkoncloud.cn", "description": "Inferred Url"}], "tags": [{"name": "Chromium内核API", "description": "Chromium Kernel Controller"}, {"name": "FAQ元数据API", "description": "Meta Faq Controller"}, {"name": "GEO元数据API", "description": "Meta Geo Controller"}, {"name": "IP元数据API", "description": "Meta Ip Controller"}, {"name": "remote-meta-service-impl", "description": "Remote Meta Service Impl"}, {"name": "元数据API", "description": "Meta Controller"}, {"name": "平台元数据API", "description": "Meta Platform Controller"}, {"name": "汇报http结果", "description": "Client Http Controller"}, {"name": "获取客户端IP API", "description": "Transit My Ip Controller"}], "paths": {"/api/meta/chromium/checkUpdate": {"get": {"tags": ["ChromiumKernelController"], "summary": "用来检查某个内核版本是否有更新", "operationId": "metaChromiumCheckUpdateGet", "parameters": [{"name": "platform", "in": "query", "description": "platform", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "oemName", "in": "query", "description": "oemName", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "version", "in": "query", "description": "version", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ChromiumKernelVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/meta/chromium/findSuitableKernel": {"get": {"tags": ["ChromiumKernelController"], "summary": "用来获取一个打开指定ua version的内核", "operationId": "metaChromiumFindSuitableKernelGet", "parameters": [{"name": "platform", "in": "query", "description": "platform", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "oemName", "in": "query", "description": "oemName", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "uaVersion", "in": "query", "description": "uaVersion", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ChromiumKernelVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/clientHttpResult": {"post": {"tags": ["ClientHttpController"], "summary": "汇报结果", "operationId": "clientHttpResultPost", "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/meta/app/versionConfigs": {"get": {"tags": ["MetaController"], "summary": "获取App版本配置信息", "operationId": "metaAppVersionConfigsGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«AppVersionConfig»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/meta/cloud/providers": {"get": {"tags": ["MetaController"], "summary": "支持的云厂商列表", "operationId": "metaCloudProvidersGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«CloudProviderVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/meta/cloud/regions": {"get": {"tags": ["MetaController"], "summary": "获取云厂商的区域列表", "operationId": "metaCloudRegionsGet", "parameters": [{"name": "provider", "in": "query", "description": "provider", "required": false, "style": "form", "schema": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "aws", "aws_cn", "aws_ls", "azure", "azure_cn", "baidu", "bao<PERSON><PERSON>", "bluevps", "dmit", "ecloud10086", "googlecloud", "hua<PERSON>", "huayang", "huo<PERSON>", "jdbox", "jdcloud", "jdeip", "lan", "oracle", "other", "qcloud", "raincloud", "ucloud", "vlcloud", "vps", "yge<PERSON>"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«CloudProviderVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/meta/creditTypes": {"get": {"tags": ["MetaController"], "summary": "获取花瓣类型信息", "operationId": "metaCreditTypesGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«CreditTypeVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/meta/exchange/list": {"get": {"tags": ["MetaController"], "summary": "getExchangeList", "operationId": "metaExchangeListGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«ExchangeDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/meta/exchange/rate": {"get": {"tags": ["MetaController"], "summary": "获取货币转换汇率", "operationId": "metaExchangeRateGet", "parameters": [{"name": "from", "in": "query", "description": "from", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "to", "in": "query", "description": "to", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«double»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/meta/sysOpConfig": {"get": {"tags": ["MetaController"], "summary": "查询运营配置", "operationId": "metaSysOpConfigGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«SystemOpConfig»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/meta/tk/pack/paramDef": {"get": {"tags": ["MetaController"], "summary": "获取TK套餐参数类型", "operationId": "metaTkPackParamDefGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«TkPackParamVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/meta/faq/page": {"get": {"tags": ["MetaFaqController"], "summary": "分页查询FAQ", "operationId": "metaFaqPageGet", "parameters": [{"name": "title", "in": "query", "description": "title", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "hide", "in": "query", "description": "hide", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "pageNum", "in": "query", "description": "pageNum", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«KfFaqDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/meta/countries": {"get": {"tags": ["MetaGeoController"], "summary": "获取国家（地区）列表", "operationId": "metaCountriesGet", "parameters": [{"name": "show", "in": "query", "description": "是否按照show字段过滤", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "name", "in": "query", "description": "是否按名称模糊匹配过滤", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "includeLocation", "in": "query", "description": "includeLocation", "required": false, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«CountryVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/meta/locales": {"get": {"tags": ["MetaGeoController"], "summary": "获取所有语言", "operationId": "metaLocalesGet", "parameters": [{"name": "countryCode", "in": "query", "description": "countryCode", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«LocaleVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/meta/provinces": {"get": {"tags": ["MetaGeoController"], "summary": "获取省份", "operationId": "metaProvincesGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«string»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/meta/timezones": {"get": {"tags": ["MetaGeoController"], "summary": "获取所有时区", "operationId": "metaTimezonesGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«TimezoneVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/meta/ip": {"get": {"tags": ["MetaIpController"], "summary": "获取客户端IP", "operationId": "metaIpGet", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "string"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/meta/ip/checkers": {"get": {"tags": ["MetaIpController"], "summary": "获取IP检查器", "operationId": "metaIpCheckersGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RemoteIpCheckerConfig»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/meta/ip/location": {"post": {"tags": ["MetaIpController"], "summary": "添加ip归属地", "operationId": "metaIpLocationPost", "parameters": [{"name": "countryCode", "in": "query", "description": "countryCode", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "province", "in": "query", "description": "province", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "city", "in": "query", "description": "city", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«IpLocationDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/meta/ip/location/countries": {"get": {"tags": ["MetaIpController"], "summary": "查询国家级别地理位置", "operationId": "metaIpLocationCountriesGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«IpLocationDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/meta/ip/location/country": {"get": {"tags": ["MetaIpController"], "summary": "查询特定国家级别地理位置", "operationId": "metaIpLocationCountryGet", "parameters": [{"name": "countryCode", "in": "query", "description": "countryCode", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«IpLocationDto»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/meta/ip/location/{locationId}": {"get": {"tags": ["MetaIpController"], "summary": "查询位置信息", "operationId": "metaIpLocationByLocationIdGet", "parameters": [{"name": "locationId", "in": "path", "description": "locationId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«IpLocationDto»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/meta/ip/location/{locationId}/hierarchy": {"get": {"tags": ["MetaIpController"], "summary": "查询位置信息层次", "operationId": "metaIpLocationByLocationIdHierarchyGet", "parameters": [{"name": "locationId", "in": "path", "description": "locationId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«IpLocationDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/meta/ip/locationTree": {"get": {"tags": ["MetaIpController"], "summary": "返回ip归属地列表（树结构）", "operationId": "metaIpLocationTreeGet", "parameters": [{"name": "countryCode", "in": "query", "description": "countryCode", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«ProvinceDetailVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/meta/ip/myLocation": {"get": {"tags": ["MetaIpController"], "summary": "获取客户端IP（或指定IP）的位置信息", "operationId": "metaIpMyLocationGet", "parameters": [{"name": "ip", "in": "query", "description": "ip", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "force", "in": "query", "description": "force", "required": false, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«IpLocationVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/meta/ip/provinceCityList": {"get": {"tags": ["MetaIpController"], "summary": "获取IP库中的省份-城市列表", "operationId": "metaIpProvinceCityListGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«ProvinceCityListVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/meta/ip/search": {"get": {"tags": ["MetaIpController"], "summary": "搜索位置", "description": "匹配城市、省、国家", "operationId": "metaIpSearchGet", "parameters": [{"name": "query", "in": "query", "description": "query", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "pageNum", "in": "query", "description": "pageNum", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«IpLocationDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/meta/ip/searchCity": {"get": {"tags": ["MetaIpController"], "summary": "搜索城市", "description": "可以不设置countryCode和provinceCode，只搜素city（匹配中英文）", "operationId": "metaIpSearchCityGet", "parameters": [{"name": "countryCode", "in": "query", "description": "countryCode", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "provinceCode", "in": "query", "description": "provinceCode", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "query", "in": "query", "description": "query", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«IpLocationDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/meta/ip/searchProvince": {"get": {"tags": ["MetaIpController"], "summary": "根据countryCode搜索省", "operationId": "metaIpSearchProvinceGet", "parameters": [{"name": "countryCode", "in": "query", "description": "countryCode", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "query", "in": "query", "description": "query", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«ProvinceVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/meta/ip138": {"get": {"tags": ["MetaIpController"], "summary": "获取客户端IP（或指定IP）的位置信息", "operationId": "metaIp138Get", "parameters": [{"name": "ip", "in": "query", "description": "ip", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "force", "in": "query", "description": "force", "required": false, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«IpDataVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/meta/areas": {"get": {"tags": ["MetaPlatformController"], "summary": "查询当前系统所有账号地区列表", "operationId": "metaAreasGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«AreaVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/meta/platform/{id}": {"get": {"tags": ["MetaPlatformController"], "summary": "特定平台数据", "operationId": "metaPlatformByIdGet", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ShopPlatformVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/meta/platformTypes": {"get": {"tags": ["MetaPlatformController"], "summary": "所有平台类型数据", "operationId": "metaPlatformTypesGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«PlatformTypeDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/meta/platforms": {"get": {"tags": ["MetaPlatformController"], "summary": "所有平台数据", "operationId": "metaPlatformsGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«PlatformVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/meta/ip/location": {"get": {"tags": ["RemoteMetaServiceImpl"], "summary": "getLocation", "operationId": "remoteMetaIpLocationGet", "parameters": [{"name": "ip", "in": "query", "description": "ip", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«IpLocationResultVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/meta/ip/locationProviderVersion": {"get": {"tags": ["RemoteMetaServiceImpl"], "summary": "getIpLocationProviderVersion", "operationId": "remoteMetaIpLocationProviderVersionGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«IpLocationResultVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/meta/ip/reloadLocationProvider": {"get": {"tags": ["RemoteMetaServiceImpl"], "summary": "reloadIpLocationProvider", "operationId": "remoteMetaIpReloadLocationProviderGet", "parameters": [{"name": "geoFile", "in": "query", "description": "geoFile", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "qqwryFile", "in": "query", "description": "qqwryFile", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«IpLocationResultVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/transitMyIp": {"get": {"tags": ["TransitMyIpController"], "summary": "获取客户端IP", "operationId": "transitMyIpGet", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "string"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}}, "components": {"schemas": {"AppPlatformVersionConfig": {"title": "AppPlatformVersionConfig", "type": "object", "properties": {"appVersion": {"type": "string"}, "betaAppVersion": {"type": "string"}, "betaBuildNo": {"type": "integer", "format": "int32"}, "browserVersion": {"type": "integer", "format": "int32"}, "currentBuildNo": {"type": "integer", "format": "int32"}, "earlyTryVersion": {"type": "integer", "format": "int32"}, "minBuildNo": {"type": "integer", "format": "int32"}, "platfrom": {"type": "string", "enum": ["android", "linux", "macos", "unknown", "web", "windows", "windows7"]}}}, "AppVersionConfig": {"title": "AppVersionConfig", "type": "object", "properties": {"betaUserIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "versionConfigs": {"type": "array", "items": {"$ref": "#/components/schemas/AppPlatformVersionConfig"}}}}, "AreaVo": {"title": "AreaVo", "type": "object", "properties": {"area": {"type": "string", "enum": ["Argentina", "Australia", "Austria", "Belarus", "Belgium", "Bolivia", "Brazil", "Canada", "Chile", "China", "Colombia", "Costa_Rica", "Dominican", "Ecuador", "Egypt", "France", "Germany", "Global", "Guatemala", "Honduras", "HongKong", "India", "Indonesia", "Ireland", "Israel", "Italy", "Japan", "Kazakhstan", "Korea", "Malaysia", "Mexico", "Netherlands", "Nicaragua", "Panama", "Paraguay", "Peru", "Philippines", "Poland", "Portuguese", "Puerto_Rico", "Russia", "Salvador", "Saudi_Arabia", "Singapore", "Spain", "Sweden", "Switzerland", "Taiwan", "Thailand", "Turkey", "United_Arab_Emirates", "United_Kingdom", "United_States", "Uruguay", "Venezuela", "Vietnam"]}, "desc": {"type": "string"}}}, "ChromiumKernelVo": {"title": "ChromiumKernelVo", "type": "object", "properties": {"buildNumber": {"type": "integer", "description": "构建号，最好填 yyyyMMdd{index}", "format": "int32"}, "description": {"type": "string"}, "downloadLink": {"type": "string", "description": "下载地址"}, "id": {"type": "integer", "format": "int64"}, "maxUa": {"type": "integer", "description": "允许模拟的最大的UA版本号，如125，客户端要根据min_ua和max_ua在已经下载的内核列表里找到一个合适的内核来打开会话", "format": "int32"}, "minUa": {"type": "integer", "description": "允许模拟的最小的UA版本号，如119", "format": "int32"}, "name": {"type": "string"}, "oem": {"type": "string"}, "platform": {"type": "string", "description": "'windows10' | 'windows7' | 'macos_arm' | 'macos_x64' | 'linux' //该版本内核能安装在哪个操作系统上"}, "updateTime": {"type": "string", "description": "这个内核的更新时间", "format": "date-time"}, "versionNumber": {"type": "integer", "description": "主版本号，如 125", "format": "int32"}}}, "CloudProviderVo": {"title": "CloudProviderVo", "type": "object", "properties": {"desc": {"type": "string"}, "provider": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "aws", "aws_cn", "aws_ls", "azure", "azure_cn", "baidu", "bao<PERSON><PERSON>", "bluevps", "dmit", "ecloud10086", "googlecloud", "hua<PERSON>", "huayang", "huo<PERSON>", "jdbox", "jdcloud", "jdeip", "lan", "oracle", "other", "qcloud", "raincloud", "ucloud", "vlcloud", "vps", "yge<PERSON>"]}, "regions": {"type": "array", "items": {"$ref": "#/components/schemas/CloudRegionVo"}}}}, "CloudRegionVo": {"title": "CloudRegionVo", "type": "object", "properties": {"area": {"type": "string", "enum": ["中东", "中国", "亚太", "北美", "南极洲", "南美", "欧洲", "非洲"]}, "city": {"type": "string"}, "country": {"type": "string"}, "ipLocation": {"$ref": "#/components/schemas/IpLocationDto"}, "location": {"type": "string"}, "region": {"type": "string"}}}, "CountryVo": {"title": "CountryVo", "type": "object", "properties": {"code": {"type": "string"}, "continentCode": {"type": "string"}, "continentName": {"type": "string"}, "continentNameEn": {"type": "string"}, "geonameId": {"type": "integer", "format": "int32"}, "id": {"type": "integer", "format": "int64"}, "inEu": {"type": "boolean"}, "location": {"$ref": "#/components/schemas/IpLocationDto"}, "name": {"type": "string"}, "nameEn": {"type": "string"}, "show": {"type": "boolean"}}}, "CreditTypeVo": {"title": "CreditTypeVo", "type": "object", "properties": {"creditType": {"type": "string", "enum": ["BuyCredit", "ConsumeCredit", "FingerprintQuota", "GiftCard", "InitPresent", "IosDeveloperApprove", "IpOver<PERSON><PERSON><PERSON><PERSON>raffic", "OrderRefund", "Present", "RpaCaptcha", "RpaExecuteQuota", "RpaMarketFlow", "RpaOpenAi", "RpaSendEmail", "RpaSendSms", "RpaSendWeChat", "ShopQuota", "ShopSecurityPolicy", "StorageQuota", "TeamMemberQuota", "Team<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TransferIn", "TransferOut", "TransitTraffic"]}, "decrease": {"type": "boolean", "description": "是否是支出", "example": false}, "desc": {"type": "string"}}}, "ExchangeDto": {"title": "ExchangeDto", "type": "object", "properties": {"countryCode": {"type": "string"}, "currency": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "rate": {"type": "number", "format": "double"}, "region": {"type": "string"}, "show": {"type": "boolean"}, "symbol": {"type": "string"}, "updateTime": {"type": "string", "format": "date-time"}}}, "HuaYongCheckerConfig": {"title": "HuaYongCheckerConfig", "type": "object", "properties": {"domestic": {"type": "boolean"}, "endpoint": {"type": "string"}, "ipv6": {"type": "boolean"}}}, "IpDataVo": {"title": "IpDataVo", "type": "object", "properties": {"city": {"type": "string"}, "country": {"type": "string"}, "countryCode": {"type": "string"}, "district": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "ip": {"type": "string"}, "isp": {"type": "string"}, "location": {"$ref": "#/components/schemas/IpLocationDto"}, "locationId": {"type": "integer", "format": "int64"}, "province": {"type": "string"}, "revisedLocationId": {"type": "integer", "format": "int64"}, "revisedProvider": {"type": "string"}, "tag": {"type": "string"}, "updateTime": {"type": "string", "format": "date-time"}, "zip": {"type": "string"}, "zone": {"type": "string"}}}, "IpLocationDto": {"title": "IpLocationDto", "type": "object", "properties": {"city": {"type": "string"}, "cityEn": {"type": "string"}, "continent": {"type": "string"}, "continentCode": {"type": "string"}, "continentEn": {"type": "string"}, "country": {"type": "string"}, "countryCode": {"type": "string"}, "countryEn": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "geonameId": {"type": "integer", "format": "int32"}, "id": {"type": "integer", "format": "int64"}, "inEu": {"type": "boolean"}, "latitude": {"type": "number", "format": "double"}, "level": {"type": "string", "enum": ["City", "Continent", "Country", "District", "None", "Province", "Unknown"]}, "locale": {"type": "string"}, "longitude": {"type": "number", "format": "double"}, "postalCode": {"type": "string"}, "province": {"type": "string"}, "provinceCode": {"type": "string"}, "provinceEn": {"type": "string"}, "show": {"type": "boolean"}, "teamId": {"type": "integer", "format": "int64"}, "timezone": {"type": "string"}}}, "IpLocationResultVo": {"title": "IpLocationResultVo", "type": "object", "properties": {"currentProduct": {"$ref": "#/components/schemas/ProductInfo"}, "result": {"type": "object"}}}, "IpLocationVo": {"title": "IpLocationVo", "type": "object", "properties": {"city": {"type": "string"}, "cityEn": {"type": "string"}, "continent": {"type": "string"}, "continentCode": {"type": "string"}, "continentEn": {"type": "string"}, "country": {"type": "string"}, "countryCode": {"type": "string"}, "countryEn": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "domestic": {"type": "boolean"}, "geonameId": {"type": "integer", "format": "int32"}, "id": {"type": "integer", "format": "int64"}, "inEu": {"type": "boolean"}, "ip": {"type": "string"}, "latitude": {"type": "number", "format": "double"}, "level": {"type": "string", "enum": ["City", "Continent", "Country", "District", "None", "Province", "Unknown"]}, "locale": {"type": "string"}, "longitude": {"type": "number", "format": "double"}, "postalCode": {"type": "string"}, "province": {"type": "string"}, "provinceCode": {"type": "string"}, "provinceEn": {"type": "string"}, "show": {"type": "boolean"}, "teamId": {"type": "integer", "format": "int64"}, "timezone": {"type": "string"}}}, "KfFaqDto": {"title": "KfFaqDto", "type": "object", "properties": {"hide": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "sortNo": {"type": "integer", "format": "int32"}, "title": {"type": "string"}, "updateTime": {"type": "string", "format": "date-time"}, "url": {"type": "string"}}}, "LocaleVo": {"title": "LocaleVo", "type": "object", "properties": {"displayName": {"type": "string"}, "locale": {"type": "string"}}}, "PageResult«IpLocationDto»": {"title": "PageResult«IpLocationDto»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/IpLocationDto"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«KfFaqDto»": {"title": "PageResult«KfFaqDto»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/KfFaqDto"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PlatformTypeDto": {"title": "PlatformTypeDto", "type": "object", "properties": {"category": {"type": "string", "enum": ["IM", "Mail", "Other", "Payment", "Shop", "SocialMedia"]}, "defaultArea": {"type": "string", "enum": ["Argentina", "Australia", "Austria", "Belarus", "Belgium", "Bolivia", "Brazil", "Canada", "Chile", "China", "Colombia", "Costa_Rica", "Dominican", "Ecuador", "Egypt", "France", "Germany", "Global", "Guatemala", "Honduras", "HongKong", "India", "Indonesia", "Ireland", "Israel", "Italy", "Japan", "Kazakhstan", "Korea", "Malaysia", "Mexico", "Netherlands", "Nicaragua", "Panama", "Paraguay", "Peru", "Philippines", "Poland", "Portuguese", "Puerto_Rico", "Russia", "Salvador", "Saudi_Arabia", "Singapore", "Spain", "Sweden", "Switzerland", "Taiwan", "Thailand", "Turkey", "United_Arab_Emirates", "United_Kingdom", "United_States", "Uruguay", "Venezuela", "Vietnam"]}, "description": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "show": {"type": "boolean"}, "type": {"type": "string"}}}, "PlatformVo": {"title": "PlatformVo", "type": "object", "properties": {"category": {"type": "string", "enum": ["IM", "Mail", "Other", "Payment", "Shop", "SocialMedia"]}, "defaultArea": {"type": "string", "enum": ["Argentina", "Australia", "Austria", "Belarus", "Belgium", "Bolivia", "Brazil", "Canada", "Chile", "China", "Colombia", "Costa_Rica", "Dominican", "Ecuador", "Egypt", "France", "Germany", "Global", "Guatemala", "Honduras", "HongKong", "India", "Indonesia", "Ireland", "Israel", "Italy", "Japan", "Kazakhstan", "Korea", "Malaysia", "Mexico", "Netherlands", "Nicaragua", "Panama", "Paraguay", "Peru", "Philippines", "Poland", "Portuguese", "Puerto_Rico", "Russia", "Salvador", "Saudi_Arabia", "Singapore", "Spain", "Sweden", "Switzerland", "Taiwan", "Thailand", "Turkey", "United_Arab_Emirates", "United_Kingdom", "United_States", "Uruguay", "Venezuela", "Vietnam"]}, "endpoints": {"type": "array", "items": {"$ref": "#/components/schemas/ShopPlatformDetail"}}, "show": {"type": "boolean"}, "type": {"type": "string"}}}, "ProductInfo": {"title": "ProductInfo", "type": "object", "properties": {"name": {"type": "string"}, "principal": {"type": "string"}, "version": {"type": "string"}}}, "ProvinceCityListVo": {"title": "ProvinceCityListVo", "type": "object", "properties": {"city": {"type": "array", "items": {"type": "string"}}, "province": {"type": "string"}}}, "ProvinceDetailVo": {"title": "ProvinceDetailVo", "type": "object", "properties": {"cities": {"type": "array", "items": {"$ref": "#/components/schemas/IpLocationDto"}}, "city": {"type": "string"}, "cityEn": {"type": "string"}, "continent": {"type": "string"}, "continentCode": {"type": "string"}, "continentEn": {"type": "string"}, "country": {"type": "string"}, "countryCode": {"type": "string"}, "countryEn": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "geonameId": {"type": "integer", "format": "int32"}, "id": {"type": "integer", "format": "int64"}, "inEu": {"type": "boolean"}, "latitude": {"type": "number", "format": "double"}, "level": {"type": "string", "enum": ["City", "Continent", "Country", "District", "None", "Province", "Unknown"]}, "locale": {"type": "string"}, "longitude": {"type": "number", "format": "double"}, "postalCode": {"type": "string"}, "province": {"type": "string"}, "provinceCode": {"type": "string"}, "provinceEn": {"type": "string"}, "show": {"type": "boolean"}, "teamId": {"type": "integer", "format": "int64"}, "timezone": {"type": "string"}}}, "ProvinceVo": {"title": "ProvinceVo", "type": "object", "properties": {"countryCode": {"type": "string"}, "province": {"type": "string"}, "provinceCode": {"type": "string"}, "provinceEn": {"type": "string"}}}, "RemoteIpCheckerConfig": {"title": "RemoteIpCheckerConfig", "type": "object", "properties": {"checkers": {"type": "array", "items": {"$ref": "#/components/schemas/RemoteIpProviderConfig"}}, "huaYongCheckers": {"type": "array", "items": {"$ref": "#/components/schemas/HuaYongCheckerConfig"}}, "probeUrl": {"type": "string"}}}, "RemoteIpProviderConfig": {"title": "RemoteIpProviderConfig", "type": "object", "properties": {"dataIndex": {"type": "array", "items": {"type": "string"}}, "isDefault": {"type": "boolean"}, "provider": {"type": "string"}, "url": {"type": "string"}}}, "ShopPlatformDetail": {"title": "ShopPlatformDetail", "type": "object", "properties": {"area": {"type": "string", "enum": ["Argentina", "Australia", "Austria", "Belarus", "Belgium", "Bolivia", "Brazil", "Canada", "Chile", "China", "Colombia", "Costa_Rica", "Dominican", "Ecuador", "Egypt", "France", "Germany", "Global", "Guatemala", "Honduras", "HongKong", "India", "Indonesia", "Ireland", "Israel", "Italy", "Japan", "Kazakhstan", "Korea", "Malaysia", "Mexico", "Netherlands", "Nicaragua", "Panama", "Paraguay", "Peru", "Philippines", "Poland", "Portuguese", "Puerto_Rico", "Russia", "Salvador", "Saudi_Arabia", "Singapore", "Spain", "Sweden", "Switzerland", "Taiwan", "Thailand", "Turkey", "United_Arab_Emirates", "United_Kingdom", "United_States", "Uruguay", "Venezuela", "Vietnam"]}, "category": {"type": "string", "enum": ["IM", "Mail", "Other", "Payment", "Shop", "SocialMedia"]}, "country": {"type": "string"}, "countryEn": {"type": "string"}, "frontUrl": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "loginUrl": {"type": "string"}, "name": {"type": "string"}, "typeName": {"type": "string"}}}, "ShopPlatformVo": {"title": "ShopPlatformVo", "type": "object", "properties": {"area": {"type": "string", "enum": ["Argentina", "Australia", "Austria", "Belarus", "Belgium", "Bolivia", "Brazil", "Canada", "Chile", "China", "Colombia", "Costa_Rica", "Dominican", "Ecuador", "Egypt", "France", "Germany", "Global", "Guatemala", "Honduras", "HongKong", "India", "Indonesia", "Ireland", "Israel", "Italy", "Japan", "Kazakhstan", "Korea", "Malaysia", "Mexico", "Netherlands", "Nicaragua", "Panama", "Paraguay", "Peru", "Philippines", "Poland", "Portuguese", "Puerto_Rico", "Russia", "Salvador", "Saudi_Arabia", "Singapore", "Spain", "Sweden", "Switzerland", "Taiwan", "Thailand", "Turkey", "United_Arab_Emirates", "United_Kingdom", "United_States", "Uruguay", "Venezuela", "Vietnam"]}, "category": {"type": "string", "enum": ["IM", "Mail", "Other", "Payment", "Shop", "SocialMedia"]}, "frontUrl": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "loginUrl": {"type": "string"}, "name": {"type": "string"}, "typeName": {"type": "string"}}}, "SystemOpConfig": {"title": "SystemOpConfig", "type": "object", "properties": {"downloadAppOnLogin": {"type": "boolean"}, "recordHttpLog": {"type": "boolean"}}}, "TimezoneVo": {"title": "TimezoneVo", "type": "object", "properties": {"offset": {"type": "integer", "format": "int32"}, "timezone": {"type": "string"}}}, "TkPackParamVo": {"title": "TkPackParamVo", "type": "object", "properties": {"label": {"type": "string"}, "paramKey": {"type": "string"}}}, "WebResult": {"title": "WebResult", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«AppVersionConfig»": {"title": "WebResult«AppVersionConfig»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/AppVersionConfig"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«ChromiumKernelVo»": {"title": "WebResult«ChromiumKernelVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/ChromiumKernelVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«IpDataVo»": {"title": "WebResult«IpDataVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/IpDataVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«IpLocationDto»": {"title": "WebResult«IpLocationDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/IpLocationDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«IpLocationResultVo»": {"title": "WebResult«IpLocationResultVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/IpLocationResultVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«IpLocationVo»": {"title": "WebResult«IpLocationVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/IpLocationVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«AreaVo»»": {"title": "WebResult«List«AreaVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/AreaVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«CloudProviderVo»»": {"title": "WebResult«List«CloudProviderVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/CloudProviderVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«CountryVo»»": {"title": "WebResult«List«CountryVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/CountryVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«CreditTypeVo»»": {"title": "WebResult«List«CreditTypeVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/CreditTypeVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«ExchangeDto»»": {"title": "WebResult«List«ExchangeDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ExchangeDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«IpLocationDto»»": {"title": "WebResult«List«IpLocationDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/IpLocationDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«LocaleVo»»": {"title": "WebResult«List«LocaleVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/LocaleVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«PlatformTypeDto»»": {"title": "WebResult«List«PlatformTypeDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/PlatformTypeDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«PlatformVo»»": {"title": "WebResult«List«PlatformVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/PlatformVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«ProvinceCityListVo»»": {"title": "WebResult«List«ProvinceCityListVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ProvinceCityListVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«ProvinceDetailVo»»": {"title": "WebResult«List«ProvinceDetailVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ProvinceDetailVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«ProvinceVo»»": {"title": "WebResult«List«ProvinceVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ProvinceVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«TimezoneVo»»": {"title": "WebResult«List«TimezoneVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TimezoneVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«TkPackParamVo»»": {"title": "WebResult«List«TkPackParamVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TkPackParamVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«string»»": {"title": "WebResult«List«string»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"type": "string"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«IpLocationDto»»": {"title": "WebResult«PageResult«IpLocationDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«IpLocationDto»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«KfFaqDto»»": {"title": "WebResult«PageResult«KfFaqDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«KfFaqDto»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«RemoteIpCheckerConfig»": {"title": "WebResult«RemoteIpCheckerConfig»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/RemoteIpCheckerConfig"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«ShopPlatformVo»": {"title": "WebResult«ShopPlatformVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/ShopPlatformVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«SystemOpConfig»": {"title": "WebResult«SystemOpConfig»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/SystemOpConfig"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«double»": {"title": "WebResult«double»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "number", "format": "double"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}}, "securitySchemes": {"Authorization": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}, "x-dm-team-id": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-dm-team-id", "in": "header"}}}}
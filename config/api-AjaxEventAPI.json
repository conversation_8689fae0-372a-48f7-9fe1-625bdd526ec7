{"openapi": "3.0.3", "info": {"title": "Remote API", "version": "App version 1.0,Spring Boot Version:2.7.14"}, "servers": [{"url": "http://dev.thinkoncloud.cn", "description": "Inferred Url"}], "tags": [{"name": "后台事件推送", "description": "Ajax Event Controller"}, {"name": "后台往前台推送事件相关的接口", "description": "Ajax Event Remote Service Controller"}], "paths": {"/api/ajax-event/batch-un-listener": {"put": {"tags": ["AjaxEventController"], "summary": "取消监听某个事件", "operationId": "ajaxEventBatchUnListenerPut", "parameters": [{"name": "pageId", "in": "query", "description": "pageId", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "resIds", "in": "query", "description": "resIds", "required": true, "style": "form", "explode": true, "schema": {"type": "string"}}, {"name": "eventName", "in": "query", "description": "eventName", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ajax-event/batchListener": {"put": {"tags": ["AjaxEventController"], "summary": "监听某个事件", "operationId": "ajaxEventBatchListenerPut", "parameters": [{"name": "pageId", "in": "query", "description": "pageId", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "resIds", "in": "query", "description": "resIds", "required": true, "style": "form", "explode": true, "schema": {"type": "string"}}, {"name": "eventName", "in": "query", "description": "eventName", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ajax-event/listener": {"put": {"tags": ["AjaxEventController"], "summary": "监听某个事件", "operationId": "ajaxEventListenerPut", "parameters": [{"name": "pageId", "in": "query", "description": "pageId", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "resId", "in": "query", "description": "resId", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "eventName", "in": "query", "description": "eventName", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "needDetail", "in": "query", "description": "needDetail", "required": false, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ajax-event/query": {"get": {"tags": ["AjaxEventController"], "summary": "定时轮循某个页面的服务器推送事件", "operationId": "ajaxEventQueryGet", "parameters": [{"name": "pageId", "in": "query", "description": "pageId", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«AjaxEventVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ajax-event/un-listener": {"put": {"tags": ["AjaxEventController"], "summary": "取消监听某个事件", "operationId": "ajaxEventUnListenerPut", "parameters": [{"name": "pageId", "in": "query", "description": "pageId", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "resId", "in": "query", "description": "resId", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "eventName", "in": "query", "description": "eventName", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ajax-event/ws_ready": {"put": {"tags": ["AjaxEventController"], "summary": "通知websocket已经就绪", "operationId": "ajaxEventWs_readyPut", "parameters": [{"name": "pageId", "in": "query", "description": "pageId", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/remote/ajax-event/publish": {"post": {"tags": ["AjaxEventRemoteServiceController"], "summary": "向前台推送一个事件", "operationId": "remoteAjaxEventPublishPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PublishAjaxEventRequest"}}}}, "responses": {"200": {"description": "OK"}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}}, "components": {"schemas": {"AjaxEventVo": {"title": "AjaxEventVo", "type": "object", "properties": {"data": {"type": "object"}, "name": {"type": "string"}, "resId": {"type": "string"}}}, "PublishAjaxEventRequest": {"title": "PublishAjaxEventRequest", "type": "object", "properties": {"data": {"type": "object"}, "eventName": {"type": "string"}, "resId": {"type": "string"}, "users": {"uniqueItems": true, "type": "array", "items": {"type": "integer", "format": "int64"}}}}, "WebResult": {"title": "WebResult", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«AjaxEventVo»»": {"title": "WebResult«List«AjaxEventVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/AjaxEventVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List»": {"title": "WebResult«List»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"type": "object"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}}, "securitySchemes": {"Authorization": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}, "x-dm-team-id": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-dm-team-id", "in": "header"}}}}
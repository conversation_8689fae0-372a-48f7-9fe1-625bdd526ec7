import Functions from './../src/constants/Functions';

export default [
  {
    path: '/login',
    layout: false,
    name: '登录',
    locale: '登录',
    hideInMenu: true,
    component: './UserLogin/Login',
  },
  {
    path: '/register',
    layout: false,
    name: '注册',
    locale: '注册',
    hideInMenu: true,
    component: './UserLogin/Register',
  },
  {
    path: '/newCombo',
    hideInMenu: true,
    component: './NewCombo',
  },
  {
    path: '/findPassword',
    layout: false,
    name: '找回密码',
    locale: '找回密码',
    hideInMenu: true,
    component: './UserLogin/FindPassword',
  },
  {
    path: '/invite/:inviteCode',
    layout: false,
    name: '邀请注册',
    locale: '邀请注册',
    hideInMenu: true,
    component: './UserLogin/Invite',
  },
  {
    path: '/link/invite-join-team/:code',
    layout: false,
    name: '团队邀请',
    locale: '团队邀请',
    hideInMenu: true,
    component: './Link/InviteJoinTeam',
  },
  {
    path: '/',
    hideInMenu: true,
    component: './Redirect',
    layout: false,
  },
  {
    key: 'team',
    hideInMenu: true,
    path: '/team/:teamId',
    component: '@/layouts/team',
    routes: [
      {
        name: '主播轨迹详情',
        locale: '主播轨迹详情',
        hideInMenu: true,
        component: './Tracks',
        path: '/team/:teamId/tracks/:id',
      },

      {
        name: '任务池',
        locale: '任务池',
        component: './Task',
        path: '/team/:teamId/:module/taskTodo',
        meta: {
          icon: 'jihua_24',
          iconType: 'color',
        },
        wrappers: ['@/wrappers/func'],
      },
      {
        name: '历史任务',
        locale: '历史任务',
        component: './TaskHistory',
        path: '/team/:teamId/:module/taskHistory',
        meta: {
          icon: 'renwu_24',
          iconType: 'color',
        },
        wrappers: ['@/wrappers/func'],
      },
      {
        name: '消息中心',
        locale: '消息中心',
        component: './MessageCenter',
        path: '/team/:teamId/:module/messageCenter',
        meta: {
          icon: 'meitishebei_24',
          iconType: 'color',
        },
        wrappers: ['@/wrappers/func'],
      },
      {
        name: '待回复的消息',
        locale: '待回复的消息',
        component: './ReplyMessage',
        path: '/team/:teamId/:module/replyMessage',
        meta: {
          icon: 'jishixiaoxi',
          iconType: 'color',
        },
        wrappers: ['@/wrappers/func'],
      },
      {
        name: '系统设置',
        locale: '系统设置',
        component: './Setting',
        path: '/team/:teamId/:module/setting',
        meta: {
          icon: 'shezhi_24',
          iconType: 'color',
        },
        functionCodes: [Functions.KOL_LIST],
        wrappers: ['@/wrappers/func'],
      },
      {
        name: 'Kakao',
        path: '/team/:teamId/kakao',
        meta: {
          icon: 'Kakao_24',
          iconType: 'color',
          platformType: 'Kakao',
        },
        routes: [
          {
            name: '全部联系人',
            locale: '全部联系人',
            path: '/team/:teamId/kakao/contacts',
            component: './Kakao/Contacts',
            meta: {
              icon: 'tuandui_24',
              iconType: 'color',
            },
            wrappers: ['@/wrappers/func'],
          },
          {
            path: '/team/:teamId/kakao/contacts/:deviceId',
            component: './Kakao/Contacts',
            meta: {
              key: 'contacts',
            },
            hideInMenu: true,
          },
          {
            name: '全部聊天',
            locale: '全部聊天',
            path: '/team/:teamId/kakao/chats',
            component: './Kakao/Chats',
            meta: {
              icon: 'daishouquandianpu_24',
              iconType: 'color',
            },
            wrappers: ['@/wrappers/func'],
          },
          {
            path: '/team/:teamId/kakao/chats/:deviceId',
            component: './Kakao/Chats',
            meta: {
              key: 'chats',
            },
            hideInMenu: true,
          },
          {
            hideInMenu: true,
            redirect: '/team/:teamId/kakao/contacts',
          },
        ],
      },
      {
        name: 'TikTok',
        path: '/team/:teamId/tiktok',
        meta: {
          icon: 'TikTok_24',
          iconType: 'color',
          platformType: 'TikTok',
        },
        routes: [
          {
            name: '公海的',
            locale: '公海的',
            meta: {
              icon: 'gongyouIP_24',
              iconType: 'color',
            },
            functionCodes: [Functions.KOL_LIST],
            wrappers: ['@/wrappers/func'],
            path: '/team/:teamId/tiktok/store',
            routes: [
              {
                name: '主播达人',
                locale: '主播达人',
                path: '/team/:teamId/tiktok/store/live',
                component: './TikTok/Live',
                meta: {
                  icon: 'daren_24',
                  iconType: 'color',
                },
                routes: [
                  {
                    name: '主播达人',
                    locale: '主播达人',
                    path: '/team/:teamId/tiktok/store/live',
                    hideInMenu: true,
                  },
                  {
                    name: '达人详情',
                    locale: '达人详情',
                    path: '/team/:teamId/tiktok/store/live/:id',
                    hideInMenu: true,
                  },
                ],
              },

              {
                name: '视频达人',
                locale: '视频达人',
                path: '/team/:teamId/tiktok/store/video',
                component: './TikTok/Video',
                meta: {
                  icon: 'duanshipin_24',
                  iconType: 'color',
                },
              },
              {
                name: '刷榜大哥',
                locale: '刷榜大哥',
                path: '/team/:teamId/tiktok/store/gifter',
                component: './TikTok/Gifter',
                meta: {
                  icon: 'zuanshi',
                  iconType: 'color',
                },
                routes: [
                  {
                    name: '刷榜大哥',
                    locale: '刷榜大哥',
                    path: '/team/:teamId/tiktok/store/gifter',
                    hideInMenu: true,
                  },
                  {
                    name: '用户详情',
                    locale: '用户详情',
                    path: '/team/:teamId/tiktok/store/gifter/:id',
                    hideInMenu: true,
                  },
                ],
              },
              {
                name: '普通用户',
                locale: '普通用户',
                path: '/team/:teamId/tiktok/store/user',
                component: './TikTok/User',
                meta: {
                  icon: 'renwu_241',
                  iconType: 'color',
                },
              },
            ],
          },
          {
            name: '团队关注的',
            locale: '团队关注的',
            path: '/team/:teamId/tiktok/favor',
            meta: {
              iconType: 'color',
              icon: 'shoucang_24',
            },
            functionCodes: [Functions.KOL_LIST],
            wrappers: ['@/wrappers/func'],
            routes: [
              {
                name: '主播达人',
                locale: '主播达人',
                path: '/team/:teamId/tiktok/favor/live',
                component: './TikTok/Live',
                meta: {
                  icon: 'daren_24',
                  iconType: 'color',
                },
                routes: [
                  {
                    name: '主播达人',
                    locale: '主播达人',
                    path: '/team/:teamId/tiktok/favor/live',
                    hideInMenu: true,
                  },
                  {
                    name: '达人详情',
                    locale: '达人详情',
                    path: '/team/:teamId/tiktok/favor/live/:id',
                    hideInMenu: true,
                  },
                ],
              },
              {
                name: '视频达人',
                locale: '视频达人',
                path: '/team/:teamId/tiktok/favor/video',
                component: './TikTok/Video',
                meta: {
                  icon: 'duanshipin_24',
                  iconType: 'color',
                },
              },
              {
                name: '刷榜大哥',
                locale: '刷榜大哥',
                path: '/team/:teamId/tiktok/favor/gifter',
                component: './TikTok/Gifter',
                meta: {
                  icon: 'zuanshi',
                  iconType: 'color',
                },
                routes: [
                  {
                    name: '刷榜大哥',
                    locale: '刷榜大哥',
                    path: '/team/:teamId/tiktok/favor/gifter',
                    hideInMenu: true,
                  },
                  {
                    name: '用户详情',
                    locale: '用户详情',
                    path: '/team/:teamId/tiktok/favor/gifter/:id',
                    hideInMenu: true,
                  },
                ],
              },
              {
                name: '普通用户',
                locale: '普通用户',
                path: '/team/:teamId/tiktok/favor/user',
                component: './TikTok/User',
                meta: {
                  icon: 'renwu_241',
                  iconType: 'color',
                },
              },
            ],
          },
          {
            name: '已建联的',
            locale: '已建联的',
            meta: {
              icon: 'xietongshifangwen_24',
              iconType: 'color',
            },
            path: '/team/:teamId/tiktok/sent',
            functionCodes: [Functions.KOL_LIST],
            wrappers: ['@/wrappers/func'],
            routes: [
              {
                name: '主播达人',
                locale: '主播达人',
                path: '/team/:teamId/tiktok/sent/live',
                component: './TikTok/Live',
                meta: {
                  icon: 'daren_24',
                  iconType: 'color',
                },
                routes: [
                  {
                    name: '主播达人',
                    locale: '主播达人',
                    path: '/team/:teamId/tiktok/sent/live',
                    hideInMenu: true,
                  },
                  {
                    name: '达人详情',
                    locale: '达人详情',
                    path: '/team/:teamId/tiktok/sent/live/:id',
                    hideInMenu: true,
                  },
                ],
              },
              {
                name: '视频达人',
                locale: '视频达人',
                path: '/team/:teamId/tiktok/sent/video',
                component: './TikTok/Video',
                meta: {
                  icon: 'duanshipin_24',
                  iconType: 'color',
                },
              },
              {
                name: '刷榜大哥',
                locale: '刷榜大哥',
                path: '/team/:teamId/tiktok/sent/gifter',
                component: './TikTok/Gifter',
                meta: {
                  icon: 'zuanshi',
                  iconType: 'color',
                },
                routes: [
                  {
                    name: '刷榜大哥',
                    locale: '刷榜大哥',
                    path: '/team/:teamId/tiktok/sent/gifter',
                    hideInMenu: true,
                  },
                  {
                    name: '用户详情',
                    locale: '用户详情',
                    path: '/team/:teamId/tiktok/sent/gifter/:id',
                    hideInMenu: true,
                  },
                ],
              },
              {
                name: '普通用户',
                locale: '普通用户',
                path: '/team/:teamId/tiktok/sent/user',
                component: './TikTok/User',
                meta: {
                  icon: 'renwu_241',
                  iconType: 'color',
                },
              },
            ],
          },
          {
            name: '分配给我的',
            locale: '分配给我的',
            path: '/team/:teamId/tiktok/mine',
            meta: {
              icon: 'ziyouIP_24',
              iconType: 'color',
            },
            routes: [
              {
                name: '浏览器分身',
                locale: '浏览器分身',
                path: '/team/:teamId/tiktok/mine/browser',
                component: './ShopManage/ShopList',
                meta: {
                  icon: 'Chrome_24',
                  iconType: 'color',
                },
              },
              {
                name: '手机账号',
                locale: '手机账号',
                path: '/team/:teamId/tiktok/mine/mobile',
                component: './TikTok/Mine/MobileAccount',
                meta: {
                  icon: 'yidongwangluoIP_24',
                  iconType: 'color',
                },
              },
              {
                name: '我的自动计划',
                locale: '我的自动计划',
                path: '/team/:teamId/tiktok/mine/plan',
                component: './TikTok/Mine/Plan',
                meta: {
                  icon: 'riqi_24',
                  iconType: 'color',
                },
              },
              {
                name: '主播达人',
                locale: '主播达人',
                path: '/team/:teamId/tiktok/mine/live',
                component: './TikTok/Live',
                meta: {
                  icon: 'daren_24',
                  iconType: 'color',
                },
                routes: [
                  {
                    name: '主播达人',
                    locale: '主播达人',
                    path: '/team/:teamId/tiktok/mine/live',
                    hideInMenu: true,
                  },
                  {
                    name: '达人详情',
                    locale: '达人详情',
                    path: '/team/:teamId/tiktok/mine/live/:id',
                    hideInMenu: true,
                  },
                ],
              },
              {
                name: '视频达人',
                locale: '视频达人',
                path: '/team/:teamId/tiktok/mine/video',
                component: './TikTok/Video',
                meta: {
                  icon: 'duanshipin_24',
                  iconType: 'color',
                },
              },
              {
                name: '刷榜大哥',
                locale: '刷榜大哥',
                path: '/team/:teamId/tiktok/mine/gifter',
                component: './TikTok/Gifter',
                meta: {
                  icon: 'zuanshi',
                  iconType: 'color',
                },
                routes: [
                  {
                    name: '刷榜大哥',
                    locale: '刷榜大哥',
                    path: '/team/:teamId/tiktok/mine/gifter',
                    hideInMenu: true,
                  },
                  {
                    name: '用户详情',
                    locale: '用户详情',
                    path: '/team/:teamId/tiktok/mine/gifter/:id',
                    hideInMenu: true,
                  },
                ],
              },
              {
                name: '普通用户',
                locale: '普通用户',
                path: '/team/:teamId/tiktok/mine/user',
                component: './TikTok/User',
                meta: {
                  icon: 'renwu_241',
                  iconType: 'color',
                },
              },
            ],
          },
          {
            name: '正在开播的',
            locale: '正在开播的',
            path: '/team/:teamId/tiktok/online',
            meta: {
              icon: 'maikefeng_24',
              iconType: 'color',
            },
            routes: [
              {
                name: '主播达人',
                locale: '主播达人',
                path: '/team/:teamId/tiktok/online/live',
                component: './TikTok/Online',
                meta: {
                  icon: 'daren_24',
                  iconType: 'color',
                },
              },
            ],
          },
          {
            name: '已屏蔽的',
            locale: '已屏蔽的',
            path: '/team/:teamId/tiktok/ignore',
            meta: {
              icon: 'gongnengpingbi_24',
              iconType: 'color',
            },
            functionCodes: [Functions.KOL_LIST],
            wrappers: ['@/wrappers/func'],
            routes: [
              {
                name: '主播达人',
                locale: '主播达人',
                path: '/team/:teamId/tiktok/ignore/live',
                component: './TikTok/Live',
                meta: {
                  icon: 'daren_24',
                  iconType: 'color',
                },
                routes: [
                  {
                    name: '主播达人',
                    locale: '主播达人',
                    path: '/team/:teamId/tiktok/ignore/live',
                    hideInMenu: true,
                  },
                  {
                    name: '达人详情',
                    locale: '达人详情',
                    path: '/team/:teamId/tiktok/ignore/live/:id',
                    hideInMenu: true,
                  },
                ],
              },
              {
                name: '视频达人',
                locale: '视频达人',
                path: '/team/:teamId/tiktok/ignore/video',
                component: './TikTok/Video',
                meta: {
                  icon: 'duanshipin_24',
                  iconType: 'color',
                },
              },
              {
                name: '刷榜大哥',
                locale: '刷榜大哥',
                path: '/team/:teamId/tiktok/ignore/gifter',
                component: './TikTok/Gifter',
                meta: {
                  icon: 'zuanshi',
                  iconType: 'color',
                },
                routes: [
                  {
                    name: '刷榜大哥',
                    locale: '刷榜大哥',
                    path: '/team/:teamId/tiktok/ignore/gifter',
                    hideInMenu: true,
                  },
                  {
                    name: '用户详情',
                    locale: '用户详情',
                    path: '/team/:teamId/tiktok/ignore/gifter/:id',
                    hideInMenu: true,
                  },
                ],
              },
              {
                name: '普通用户',
                locale: '普通用户',
                path: '/team/:teamId/tiktok/ignore/user',
                component: './TikTok/User',
                meta: {
                  icon: 'renwu_241',
                  iconType: 'color',
                },
              },
            ],
          },
          {
            name: '数据统计',
            locale: '数据统计',
            path: '/team/:teamId/:module/statistics',
            component: './Statistics',
            meta: {
              icon: 'shuju_24',
              iconType: 'color',
            },
            functionCodes: [Functions.KOL_LIST],
            wrappers: ['@/wrappers/func'],
          },
          {
            hideInMenu: true,
            redirect: '/team/:teamId/tiktok/store/live',
          },
        ],
      },
      {
        name: 'Instagram',
        path: '/team/:teamId/ins',
        meta: {
          icon: 'TikTok_24',
          iconType: 'color',
          platformType: 'Ins',
        },
        routes: [
          {
            name: '公海的',
            locale: '公海的',
            meta: {
              icon: 'gongyouIP_24',
              iconType: 'color',
            },
            component: './Instagram',
            path: '/team/:teamId/ins/store',
          },
          {
            name: '团队关注的',
            locale: '团队关注的',
            path: '/team/:teamId/ins/favor',
            component: './Instagram',
            meta: {
              iconType: 'color',
              icon: 'shoucang_24',
            },
          },
          {
            name: '已建联的',
            locale: '已建联的',
            meta: {
              icon: 'xietongshifangwen_24',
              iconType: 'color',
            },
            path: '/team/:teamId/ins/sent',
            component: './Instagram',
          },
          {
            name: '分配给我的',
            locale: '分配给我的',
            path: '/team/:teamId/ins/mine',
            meta: {
              icon: 'ziyouIP_24',
              iconType: 'color',
            },
            routes: [
              {
                name: '浏览器分身',
                locale: '浏览器分身',
                path: '/team/:teamId/ins/mine/browser',
                component: './ShopManage/ShopList',
                meta: {
                  icon: 'Chrome_24',
                  iconType: 'color',
                },
              },
              {
                name: '手机账号',
                locale: '手机账号',
                path: '/team/:teamId/ins/mine/mobile',
                component: './TikTok/Mine/MobileAccount',
                meta: {
                  icon: 'yidongwangluoIP_24',
                  iconType: 'color',
                },
              },
              {
                name: 'Ins 达人',
                locale: 'Ins 达人',
                path: '/team/:teamId/ins/mine/users',
                component: './Instagram',
                meta: {
                  icon: 'daren_24',
                  iconType: 'color',
                },
              },
            ],
          },

          {
            hideInMenu: true,
            redirect: '/team/:teamId/ins/store',
          },
        ],
      },

      {
        name: '小红书',
        locale: '小红书',
        path: '/team/:teamId/xhs',
        meta: {
          icon: 'Xiaohongshu_24',
          iconType: 'color',
          platformType: 'Xiaohongshu',
        },
        routes: [
          {
            name: '社媒账号矩阵',
            locale: '社媒账号矩阵',
            meta: {
              icon: 'paishe',
              iconType: 'color',
            },
            path: '/team/:teamId/xhs/SocialMedia',
            component: './Xhs',
          },
          {
            name: '辅助账号',
            locale: '辅助账号',
            meta: {
              icon: 'quanbushouquanchengyuan_24',
              iconType: 'color',
            },
            path: '/team/:teamId/xhs/Assistant',
            component: './Xhs',
          },
        ],
      },
      {
        name: '分配给我的',
        path: '/team/:teamId/kol/todo',
        component: './Mobile',
        hideInMenu: true,
        layout: false,
        routes: [
          {
            name: '分配给我的',
            locale: '分配给我的',
            path: '/team/:teamId/kol/todo',
            component: './Mobile/entry',
            hideInMenu: true,
          },
          {
            name: '分配给我的',
            locale: '分配给我的',
            path: '/team/:teamId/kol/todo/:type/:param?',
            component: './Mobile/list',
            hideInMenu: true,
          },
        ],
      },
    ],
  },
  // 这个必须放到最后
  {
    component: './404',
    hideInMenu: true,
  },
  {
    path: '*',
    hideInMenu: true,
    component: './Redirect',
  },
];

{"openapi": "3.0.3", "info": {"title": "Account API", "version": "App version 1.0,Spring Boot Version:2.7.14"}, "servers": [{"url": "http://dev.thinkoncloud.cn", "description": "Inferred Url"}], "tags": [{"name": "Device Register", "description": "Device Register Controller"}, {"name": "OAuth2 第三方登录", "description": "O Auth 2 Controller"}, {"name": "Ops API", "description": "Ops Strategy Controller"}, {"name": "Ops Webhook API", "description": "Ops Webhook Controller"}, {"name": "Resource API", "description": "Resource Controller"}, {"name": "krShop团队API", "description": "Kr Shop Controller"}, {"name": "团队API", "description": "Team Controller"}, {"name": "团队成员API", "description": "Team Member Controller"}, {"name": "团队收藏相关API", "description": "Team Favorite Controller"}, {"name": "团队设置API", "description": "Team Settings Controller"}, {"name": "实名认证API-其他", "description": "Team Verify Controller"}, {"name": "实名认证API-支付宝", "description": "Alipay Team Verify Controller"}, {"name": "支持 API", "description": "Support Controller"}, {"name": "收藏相关API", "description": "Favorite Controller"}, {"name": "权限API", "description": "Function Controller"}, {"name": "注册API", "description": "Register Controller"}, {"name": "演示团队API", "description": "Demo Team Controller"}, {"name": "用户管理API", "description": "User Controller"}, {"name": "用户设置API", "description": "User Settings Controller"}, {"name": "用户邀请API", "description": "User Promotion Controller"}, {"name": "登录API", "description": "Login Controller"}, {"name": "登录设备API", "description": "Login Device Controller"}, {"name": "账户API", "description": "Account Controller"}, {"name": "跨域访问账户API", "description": "Cors Account Controller"}, {"name": "部门API", "description": "Department Controller"}], "paths": {"/api/account/avatar": {"post": {"tags": ["AccountController"], "summary": "更新当前用户头像", "operationId": "accountAvatarPost", "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/account/avatar/{file}": {"get": {"tags": ["AccountController"], "summary": "查看用户头像", "operationId": "accountAvatarByFileGet", "parameters": [{"name": "file", "in": "path", "description": "file", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/account/curUser": {"get": {"tags": ["AccountController"], "summary": "查询当前用户信息", "operationId": "accountCurUserGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«UserDetailVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/account/currentRole": {"get": {"tags": ["AccountController"], "summary": "获取当前用户在当前团队中的角色", "operationId": "accountCurrentRoleGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RoleVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/account/currentUser": {"get": {"tags": ["AccountController"], "summary": "查询当前用户信息", "operationId": "accountCurrentUserGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«UserDetailVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/account/email": {"put": {"tags": ["AccountController"], "summary": "更换邮箱", "operationId": "accountEmailPut", "parameters": [{"name": "email", "in": "query", "description": "邮箱", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "verifyCode", "in": "query", "description": "验证码", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/account/nicknameAndSignature": {"put": {"tags": ["AccountController"], "summary": "修改用户昵称和签名", "operationId": "accountNicknameAndSignaturePut", "parameters": [{"name": "nickname", "in": "query", "description": "nickname", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "signature", "in": "query", "description": "signature", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/account/notifyWeixinBind/{teamId}": {"put": {"tags": ["AccountController"], "summary": "微信号绑定成功后触发活动事件", "operationId": "accountNotifyWeixinBindByTeamIdPut", "parameters": [{"name": "teamId", "in": "path", "description": "teamId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/account/phone": {"put": {"tags": ["AccountController"], "summary": "更换手机号", "operationId": "accountPhonePut", "parameters": [{"name": "areaCode", "in": "query", "description": "areaCode", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "phone", "in": "query", "description": "手机号", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "verifyCode", "in": "query", "description": "验证码", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/account/setPwdAndNickname": {"post": {"tags": ["AccountController"], "summary": "设置登录密码和昵称", "operationId": "accountSetPwdAndNicknamePost", "parameters": [{"name": "nickname", "in": "query", "description": "昵称", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "updateToken", "in": "query", "description": "updateToken", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "password", "in": "query", "description": "password", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/account/setPwdWhenEmpty": {"post": {"tags": ["AccountController"], "summary": "设置登录密码和昵称", "operationId": "accountSetPwdWhenEmptyPost", "parameters": [{"name": "password", "in": "query", "description": "password", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/account/unbindEmail": {"put": {"tags": ["AccountController"], "summary": "解绑邮箱", "operationId": "accountUnbindEmailPut", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/account/unbindPhone": {"put": {"tags": ["AccountController"], "summary": "解绑手机号", "operationId": "accountUnbindPhonePut", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/account/updatePwd": {"post": {"tags": ["AccountController"], "summary": "修改登录密码", "operationId": "accountUpdatePwdPost", "parameters": [{"name": "originPassword", "in": "query", "description": "原始登录密码", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "curPass<PERSON>", "in": "query", "description": "新的登录密码", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/account/updatePwdByVerifyCode": {"post": {"tags": ["AccountController"], "summary": "通过短信验证码修改密码", "operationId": "accountUpdatePwdByVerifyCodePost", "parameters": [{"name": "areaCode", "in": "query", "description": "areaCode", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "account", "in": "query", "description": "账号", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "password", "in": "query", "description": "密码", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "verifyCode", "in": "query", "description": "验证码", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/account/{userId}/avatar": {"get": {"tags": ["AccountController"], "summary": "根据用户id查看用户头像", "operationId": "accountByUserIdAvatarGet", "parameters": [{"name": "userId", "in": "path", "description": "userId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/verify/alipay/callback": {"get": {"tags": ["AlipayTeamVerifyController"], "summary": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "operationId": "teamVerifyAlipayCallbackGet", "parameters": [{"name": "app_id", "in": "query", "description": "app_id", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "cert_verify_id", "in": "query", "description": "cert_verify_id", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "auth_code", "in": "query", "description": "auth_code", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/verify/alipay/preconsult": {"post": {"tags": ["AlipayTeamVerifyController"], "summary": "录入用户实名信息预咨询", "operationId": "teamVerifyAlipayPreconsultPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AlipayPreconsultRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«AlipayPreconsultResult»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/verify/alipay/result/{verifyId}": {"get": {"tags": ["AlipayTeamVerifyController"], "summary": "查询认证结果", "description": "只有在发起认证2小时内才能查询结果", "operationId": "teamVerifyAlipayResultByVerifyIdGet", "parameters": [{"name": "verifyId", "in": "path", "description": "verifyId", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«AlipayVerifyResultVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/link/invite-join-team": {"post": {"tags": ["AccountController"], "summary": "发送邀请成员加入团队的链接", "operationId": "linkInviteJoinTeamPost", "parameters": [{"name": "departmentId", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "emailList", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "roleCode", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["boss", "manager", "staff", "superadmin"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/link/invite-join-team/{token}": {"get": {"tags": ["AccountController"], "summary": "邀请成员加入团队的链接响应", "operationId": "linkInviteJoinTeamByTokenGet", "parameters": [{"name": "token", "in": "path", "description": "token", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«InviteJoinTeamCheckResult»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/link/invite-join-team/{token}/confirm": {"post": {"tags": ["AccountController"], "summary": "邀请成员加入团队的链接响应", "operationId": "linkInviteJoinTeamByTokenConfirmPost", "parameters": [{"name": "token", "in": "path", "description": "token", "required": true, "style": "simple", "schema": {"type": "string"}}, {"name": "password", "in": "query", "description": "password", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«ConfirmInviteJoinTeamResult»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/cors/account/currentUser": {"get": {"tags": ["CorsAccountController"], "summary": "跨域查询当前用户信息", "operationId": "corsAccountCurrentUserGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«UserDetailVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/demo/team": {"get": {"tags": ["DemoTeamController"], "summary": "获取演示团队信息", "operationId": "demoTeamGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TeamDto»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/demo/team/checkAndJoin": {"post": {"tags": ["DemoTeamController"], "summary": "当前用户加入演示团队（如果未加入）", "operationId": "demoTeamCheckAndJoinPost", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«CheckAndJoinDemoTeamResult»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/department/": {"post": {"tags": ["DepartmentController"], "summary": "添加部门", "operationId": "departmentPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DepartmentParamVo"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«DepartmentDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/department/member/move": {"put": {"tags": ["DepartmentController"], "summary": "移动成员", "operationId": "departmentMemberMovePut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DepartmentMemberMoveParamVo"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/department/member/{userId}": {"put": {"tags": ["DepartmentController"], "summary": "编辑成员", "operationId": "departmentMemberByUserIdPut", "parameters": [{"name": "userId", "in": "path", "description": "userId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DepartmentMemberUpdateParamVo"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/department/orders": {"put": {"tags": ["DepartmentController"], "summary": "移动部门顺序", "operationId": "departmentOrdersPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateDepartmentOrderParamVo"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/department/tree": {"get": {"tags": ["DepartmentController"], "summary": "列出部门树,根据当前用户拥有的最大部门权限", "operationId": "departmentTreeGet", "parameters": [{"name": "includeMembers", "in": "query", "description": "是否包含成员，默认：false", "required": false, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«DepartmentTreeNode»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/department/{departmentId}": {"get": {"tags": ["DepartmentController"], "summary": "查询部门", "operationId": "departmentByDepartmentIdGet", "parameters": [{"name": "departmentId", "in": "path", "description": "departmentId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«DepartmentDto»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "put": {"tags": ["DepartmentController"], "summary": "修改部门信息", "operationId": "departmentByDepartmentIdPut", "parameters": [{"name": "departmentId", "in": "path", "description": "departmentId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "name", "in": "query", "description": "name", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "delete": {"tags": ["DepartmentController"], "summary": "删除部门", "operationId": "departmentByDepartmentIdDelete", "parameters": [{"name": "departmentId", "in": "path", "description": "departmentId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/department/{departmentId}/grantResources": {"put": {"tags": ["DepartmentController"], "summary": "给部门授权特定资源权限（更新不是追加）", "operationId": "departmentByDepartmentIdGrantResourcesPut", "parameters": [{"name": "departmentId", "in": "path", "description": "departmentId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GrantDepartmentResourcesRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/department/{departmentId}/grantedResources": {"get": {"tags": ["DepartmentController"], "summary": "给部门授权特定资源权限（更新不是追加）", "operationId": "departmentByDepartmentIdGrantedResourcesGet", "parameters": [{"name": "departmentId", "in": "path", "description": "departmentId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "resourceType", "in": "query", "description": "resourceType", "required": true, "style": "form", "schema": {"type": "string", "enum": ["AK", "Activity", "Audit", "BlockElements", "Cloud", "CrsOrder", "CrsProduct", "DiskFile", "FingerPrint", "FingerPrintTemplate", "Gateway", "GhCreator", "GhGifter", "GhJobPlan", "GhUser", "GhVideoCreator", "GiftCardPack", "InsTeamUser", "InsUser", "Invoice", "Ip", "IpPool", "IppIp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "KakaoFriend", "KolCreator", "MobileAccount", "None", "Orders", "PluginTeamPack", "Record", "RpaFlow", "RpaTask", "RpaTaskItem", "RpaVoucher", "Shop", "ShopSession", "Tag", "TeamDiskRoot", "TeamMobile", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TkCreator", "TkTeamPack", "Tkshop", "<PERSON>ks<PERSON><PERSON>uyer", "TkshopCreator", "TrafficPack", "TunnelVps", "Users", "View", "Voucher", "XhsAccount"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«long»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/department/{departmentId}/member": {"post": {"tags": ["DepartmentController"], "summary": "添加部门成员", "operationId": "departmentByDepartmentIdMemberPost", "parameters": [{"name": "departmentId", "in": "path", "description": "departmentId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/department/{departmentId}/members": {"get": {"tags": ["DepartmentController"], "summary": "列出部门成员", "operationId": "departmentByDepartmentIdMembersGet", "parameters": [{"name": "departmentId", "in": "path", "description": "departmentId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«UserDepartmentVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/department/{id}/invitingCode": {"get": {"tags": ["DepartmentController"], "summary": "查看部门邀请码", "operationId": "departmentByIdInvitingCodeGet", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/department/{id}/invitingEnabled": {"put": {"tags": ["DepartmentController"], "summary": "开启/关闭部门邀约", "operationId": "departmentByIdInvitingEnabledPut", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "invitingEnabled", "in": "query", "description": "invitingEnabled", "required": true, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/department/{id}/refreshInvitingCode": {"put": {"tags": ["DepartmentController"], "summary": "刷新部门邀请码", "operationId": "departmentByIdRefreshInvitingCodePut", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/department/{id}/resourceIds": {"get": {"tags": ["DepartmentController"], "summary": "获取部门授权资源ID列表", "operationId": "departmentByIdResourceIdsGet", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "resourceType", "in": "query", "description": "resourceType", "required": true, "style": "form", "schema": {"type": "string", "enum": ["AK", "Activity", "Audit", "BlockElements", "Cloud", "CrsOrder", "CrsProduct", "DiskFile", "FingerPrint", "FingerPrintTemplate", "Gateway", "GhCreator", "GhGifter", "GhJobPlan", "GhUser", "GhVideoCreator", "GiftCardPack", "InsTeamUser", "InsUser", "Invoice", "Ip", "IpPool", "IppIp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "KakaoFriend", "KolCreator", "MobileAccount", "None", "Orders", "PluginTeamPack", "Record", "RpaFlow", "RpaTask", "RpaTaskItem", "RpaVoucher", "Shop", "ShopSession", "Tag", "TeamDiskRoot", "TeamMobile", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TkCreator", "TkTeamPack", "Tkshop", "<PERSON>ks<PERSON><PERSON>uyer", "TkshopCreator", "TrafficPack", "TunnelVps", "Users", "View", "Voucher", "XhsAccount"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«long»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/device/register": {"post": {"tags": ["DeviceRegisterController"], "summary": "设备注册", "operationId": "deviceRegisterPost", "parameters": [{"name": "sign", "in": "query", "description": "sign", "required": true, "style": "form", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«HyRuntimeDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/device/reportClientIp": {"get": {"tags": ["DeviceRegisterController"], "summary": "汇报真实IP", "operationId": "deviceReportClientIpGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/favorites": {"get": {"tags": ["FavoriteController"], "summary": "查询我的所有收藏", "operationId": "favoritesGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«FavoriteDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "post": {"tags": ["FavoriteController"], "summary": "添加收藏", "operationId": "favoritesPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddFavorateRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«int»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/favorites/byType/{resourceType}": {"get": {"tags": ["FavoriteController"], "summary": "按类型查询收藏列表", "operationId": "favoritesByTypeByResourceTypeGet", "parameters": [{"name": "resourceType", "in": "path", "description": "resourceType", "required": true, "style": "simple", "schema": {"type": "string", "enum": ["AK", "Activity", "Audit", "BlockElements", "Cloud", "CrsOrder", "CrsProduct", "DiskFile", "FingerPrint", "FingerPrintTemplate", "Gateway", "GhCreator", "GhGifter", "GhJobPlan", "GhUser", "GhVideoCreator", "GiftCardPack", "InsTeamUser", "InsUser", "Invoice", "Ip", "IpPool", "IppIp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "KakaoFriend", "KolCreator", "MobileAccount", "None", "Orders", "PluginTeamPack", "Record", "RpaFlow", "RpaTask", "RpaTaskItem", "RpaVoucher", "Shop", "ShopSession", "Tag", "TeamDiskRoot", "TeamMobile", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TkCreator", "TkTeamPack", "Tkshop", "<PERSON>ks<PERSON><PERSON>uyer", "TkshopCreator", "TrafficPack", "TunnelVps", "Users", "View", "Voucher", "XhsAccount"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«FavoriteDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/favorites/delete": {"post": {"tags": ["FavoriteController"], "summary": "取消收藏", "operationId": "favoritesDeletePost", "parameters": [{"name": "resourceType", "in": "query", "description": "resourceType", "required": true, "style": "form", "schema": {"type": "string", "enum": ["AK", "Activity", "Audit", "BlockElements", "Cloud", "CrsOrder", "CrsProduct", "DiskFile", "FingerPrint", "FingerPrintTemplate", "Gateway", "GhCreator", "GhGifter", "GhJobPlan", "GhUser", "GhVideoCreator", "GiftCardPack", "InsTeamUser", "InsUser", "Invoice", "Ip", "IpPool", "IppIp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "KakaoFriend", "KolCreator", "MobileAccount", "None", "Orders", "PluginTeamPack", "Record", "RpaFlow", "RpaTask", "RpaTaskItem", "RpaVoucher", "Shop", "ShopSession", "Tag", "TeamDiskRoot", "TeamMobile", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TkCreator", "TkTeamPack", "Tkshop", "<PERSON>ks<PERSON><PERSON>uyer", "TkshopCreator", "TrafficPack", "TunnelVps", "Users", "View", "Voucher", "XhsAccount"]}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonIdsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«int»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/favorites/detailByType/{resourceType}": {"get": {"tags": ["FavoriteController"], "summary": "按类型查询收藏列表", "operationId": "favoritesDetailByTypeByResourceTypeGet", "parameters": [{"name": "resourceType", "in": "path", "description": "resourceType", "required": true, "style": "simple", "schema": {"type": "string", "enum": ["AK", "Activity", "Audit", "BlockElements", "Cloud", "CrsOrder", "CrsProduct", "DiskFile", "FingerPrint", "FingerPrintTemplate", "Gateway", "GhCreator", "GhGifter", "GhJobPlan", "GhUser", "GhVideoCreator", "GiftCardPack", "InsTeamUser", "InsUser", "Invoice", "Ip", "IpPool", "IppIp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "KakaoFriend", "KolCreator", "MobileAccount", "None", "Orders", "PluginTeamPack", "Record", "RpaFlow", "RpaTask", "RpaTaskItem", "RpaVoucher", "Shop", "ShopSession", "Tag", "TeamDiskRoot", "TeamMobile", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TkCreator", "TkTeamPack", "Tkshop", "<PERSON>ks<PERSON><PERSON>uyer", "TkshopCreator", "TrafficPack", "TunnelVps", "Users", "View", "Voucher", "XhsAccount"]}}, {"name": "pageNum", "in": "query", "description": "pageNum", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«FavoriteDetailVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/favorites/page": {"get": {"tags": ["FavoriteController"], "summary": "分页查询收藏列表", "operationId": "favoritesPageGet", "parameters": [{"name": "resourceType", "in": "query", "description": "resourceType", "required": false, "style": "form", "schema": {"type": "string", "enum": ["AK", "Activity", "Audit", "BlockElements", "Cloud", "CrsOrder", "CrsProduct", "DiskFile", "FingerPrint", "FingerPrintTemplate", "Gateway", "GhCreator", "GhGifter", "GhJobPlan", "GhUser", "GhVideoCreator", "GiftCardPack", "InsTeamUser", "InsUser", "Invoice", "Ip", "IpPool", "IppIp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "KakaoFriend", "KolCreator", "MobileAccount", "None", "Orders", "PluginTeamPack", "Record", "RpaFlow", "RpaTask", "RpaTaskItem", "RpaVoucher", "Shop", "ShopSession", "Tag", "TeamDiskRoot", "TeamMobile", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TkCreator", "TkTeamPack", "Tkshop", "<PERSON>ks<PERSON><PERSON>uyer", "TkshopCreator", "TrafficPack", "TunnelVps", "Users", "View", "Voucher", "XhsAccount"]}}, {"name": "collectTimeFrom", "in": "query", "description": "collectTimeFrom", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "collectTimeTo", "in": "query", "description": "collectTimeTo", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "query", "in": "query", "description": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "sortField", "in": "query", "description": "sortField", "required": false, "style": "form", "schema": {"type": "string", "enum": ["collectTime", "id", "name", "size"]}}, {"name": "sortOrder", "in": "query", "description": "sortOrder", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}, {"name": "pageNum", "in": "query", "description": "pageNum", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«FavoriteDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/funciton/metas": {"get": {"tags": ["FunctionController"], "summary": "获取权限的元数据（包含角色默认授权信息）", "operationId": "funcitonMetasGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«FunctionMetaVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/krshop/gbsConfig": {"get": {"tags": ["KrShopController"], "summary": "获取GBS配置", "operationId": "krshopGbsConfigGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«GbsConfig»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "put": {"tags": ["KrShopController"], "summary": "设置GBS", "operationId": "krshopGbsConfigPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GbsConfig"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/account/login": {"post": {"tags": ["LoginController"], "summary": "登录", "operationId": "accountLoginPost", "parameters": [{"name": "account", "in": "query", "description": "account", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "password", "in": "query", "description": "password", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "rememberMe", "in": "query", "description": "rememberMe", "required": false, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«LoginResultVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/account/loginByCode": {"post": {"tags": ["LoginController"], "summary": "验证码登录", "operationId": "accountLoginByCodePost", "parameters": [{"name": "areaCode", "in": "query", "description": "areaCode", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "account", "in": "query", "description": "account", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "verifyCode", "in": "query", "description": "verifyCode", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "rememberMe", "in": "query", "description": "rememberMe", "required": false, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«LoginResultVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/account/logout": {"get": {"tags": ["LoginController"], "summary": "注销当前会话或token", "operationId": "accountLogoutGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«boolean»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/account/passwordPolicy": {"get": {"tags": ["LoginController"], "summary": "获取系统的密码策略", "operationId": "accountPasswordPolicyGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PasswordPolicyVo"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/account/preLogin": {"get": {"tags": ["LoginController"], "summary": "检查账号预登陆信息", "operationId": "accountPreLoginGet", "parameters": [{"name": "account", "in": "query", "description": "account", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PreLoginVo"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/account/productConfig": {"get": {"tags": ["LoginController"], "summary": "系统基本配置信息", "operationId": "accountProductConfigGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductConfigVo"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/account/refreshToken": {"get": {"tags": ["LoginController"], "summary": "更新JWT", "operationId": "accountRefreshTokenGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«JwtResultVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/account/remoteLocation": {"get": {"tags": ["LoginController"], "summary": "获取当前客户端的位置信息", "operationId": "accountRemoteLocationGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RemoteLocationVo"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/account/device/current": {"get": {"tags": ["LoginDeviceController"], "summary": "获取当前登录设备", "operationId": "accountDeviceCurrentGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«LoginDeviceLocationVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/account/device/heartbeat": {"get": {"tags": ["LoginDeviceController"], "summary": "设备发送心跳", "operationId": "accountDeviceHeartbeatGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/account/device/kickoff": {"put": {"tags": ["LoginDeviceController"], "summary": "把对方会话踢下线", "operationId": "accountDeviceKickoffPut", "parameters": [{"name": "sessionId", "in": "query", "description": "sessionId", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "self", "in": "query", "description": "self", "required": false, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/account/device/login-sessions": {"get": {"tags": ["LoginDeviceController"], "summary": "获取相同deviceId的多个登录会话", "description": "仅限app设备", "operationId": "accountDeviceLoginSessionsGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«LoginDeviceSessionVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/account/device/remainLogin": {"get": {"tags": ["LoginDeviceController"], "summary": "查询客户端关闭后是否维持登录状态", "operationId": "accountDeviceRemainLoginGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«boolean»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "put": {"tags": ["LoginDeviceController"], "summary": "更新客户端关闭后是否维持登录状态", "operationId": "accountDeviceRemainLoginPut", "parameters": [{"name": "appRemainLogin", "in": "query", "description": "appRemainLogin", "required": true, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/account/device/transitStatus": {"get": {"tags": ["LoginDeviceController"], "summary": "查询设备到接入点的连接状态", "operationId": "accountDeviceTransitStatusGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«LoginDeviceTransitDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "post": {"tags": ["LoginDeviceController"], "summary": "更新到接入点的连接信息", "operationId": "accountDeviceTransitStatusPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginDeviceTransitRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/account/device/{deviceId}": {"get": {"tags": ["LoginDeviceController"], "summary": "获取设备详情", "operationId": "accountDeviceByDeviceIdGet", "parameters": [{"name": "deviceId", "in": "path", "description": "deviceId", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«LoginDeviceLocationVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/account/device/{deviceId}/logout": {"delete": {"tags": ["LoginDeviceController"], "summary": "在设备上退出登录", "operationId": "accountDeviceByDeviceIdLogoutDelete", "parameters": [{"name": "deviceId", "in": "path", "description": "deviceId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/account/devices": {"get": {"tags": ["LoginDeviceController"], "summary": "获取当前用户登录设备", "operationId": "accountDevicesGet", "parameters": [{"name": "online", "in": "query", "description": "是否在线，不设置返回全部", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "sortField", "in": "query", "description": "sortField", "required": false, "style": "form", "schema": {"type": "string", "enum": ["hostName", "id", "lastActiveTime", "lastLoginTime"]}}, {"name": "sortOrder", "in": "query", "description": "sortOrder", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«UserLoginDeviceVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/member/authDevice": {"put": {"tags": ["TeamMemberController"], "summary": "设置成员设备配置", "operationId": "memberAuthDevicePut", "parameters": [{"name": "userId", "in": "query", "description": "userId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "deviceId", "in": "query", "description": "deviceId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "allowed", "in": "query", "description": "allowed", "required": true, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/member/checkOps": {"get": {"tags": ["TeamMemberController"], "summary": "检查当前登录策略（抛出异常、创建审核）", "operationId": "memberCheckOpsGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/member/device/logout": {"delete": {"tags": ["TeamMemberController"], "summary": "使团队成员在设备上退出登录", "operationId": "memberDeviceLogoutDelete", "parameters": [{"name": "deviceId", "in": "query", "description": "deviceId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/member/deviceConfig": {"get": {"tags": ["TeamMemberController"], "summary": "获取设备的配置", "operationId": "memberDeviceConfigGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TeamDeviceConfig»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "put": {"tags": ["TeamMemberController"], "summary": "设置团队设备配置", "operationId": "memberDeviceConfigPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TeamDeviceConfig"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/member/list": {"get": {"tags": ["TeamMemberController"], "summary": "团队成员", "operationId": "memberListGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«UserActivityDeviceVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/member/page": {"get": {"tags": ["TeamMemberController"], "summary": "团队成员（按最近活动排列）", "operationId": "memberPageGet", "parameters": [{"name": "pageNum", "in": "query", "description": "pageNum", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "sortFiled", "in": "query", "description": "sortFiled", "required": false, "style": "form", "schema": {"type": "string", "enum": ["last_login_time", "online"]}}, {"name": "sortOrder", "in": "query", "description": "sortOrder", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«UserActivityDeviceVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/member/{userId}/devices": {"get": {"tags": ["TeamMemberController"], "summary": "获取成员的登录设备", "operationId": "memberByUserIdDevicesGet", "parameters": [{"name": "userId", "in": "path", "description": "userId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "sortField", "in": "query", "description": "sortField", "required": false, "style": "form", "schema": {"type": "string", "enum": ["hostName", "id", "lastActiveTime", "lastLoginTime"]}}, {"name": "sortOrder", "in": "query", "description": "sortOrder", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«UserLoginDeviceVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/oauth/callback/{oauthType}": {"get": {"tags": ["OAuth2Controller"], "summary": "第三方登录回调入口", "operationId": "oauthCallbackByOauthTypeGet", "parameters": [{"name": "oauthType", "in": "path", "description": "oauthType", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/oauth/cancel/{uuid}": {"get": {"tags": ["OAuth2Controller"], "summary": "取消这次登录", "operationId": "oauthCancelByUuidGet", "parameters": [{"name": "uuid", "in": "path", "description": "uuid", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/oauth/getOAuthUrl": {"get": {"tags": ["OAuth2Controller"], "summary": "从客户端获取一个浏览器登录或绑定链接和查询uuid", "operationId": "oauthGetOAuthUrlGet", "parameters": [{"name": "oauthType", "in": "query", "description": "oauthType", "required": true, "style": "form", "schema": {"type": "string", "enum": ["ALIYUN", "DINGTALK", "FACEBOOK", "GOOGLE", "KAKAO", "QQ", "WEIBO", "WEIXIN", "WORK_WEIXIN", "WX_MINPROGRAM"]}}, {"name": "action", "in": "query", "description": "action", "required": true, "style": "form", "schema": {"type": "string", "enum": ["bind", "login"]}}, {"name": "rememberMe", "in": "query", "description": "rememberMe", "required": false, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«OAuthProcessInfo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/oauth/qrcode/{uuid}": {"get": {"tags": ["OAuth2Controller"], "summary": "根据uuid获取二维码版本的URL", "operationId": "oauthQrcodeByUuidGet", "parameters": [{"name": "uuid", "in": "path", "description": "uuid", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"image/png": {"schema": {"type": "object"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/oauth/r/{oauthType}": {"get": {"tags": ["OAuth2Controller"], "summary": "第三方登录跳转入口", "operationId": "oauthRByOauthTypeGet", "parameters": [{"name": "oauthType", "in": "path", "description": "oauthType", "required": true, "style": "simple", "schema": {"type": "string"}}, {"name": "state", "in": "query", "description": "state", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/oauth/result/{uuid}": {"get": {"tags": ["OAuth2Controller"], "summary": "根据uuid查询浏览器的登录或绑定请求结果", "operationId": "oauthResultByUuidGet", "parameters": [{"name": "uuid", "in": "path", "description": "uuid", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«OAuthProcessInfo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/oauth/{oauthType}": {"get": {"tags": ["OAuth2Controller"], "summary": "第三方登录跳转入口", "operationId": "oauthByOauthTypeGet", "parameters": [{"name": "oauthType", "in": "path", "description": "oauthType", "required": true, "style": "simple", "schema": {"type": "string"}}, {"name": "state", "in": "query", "description": "state", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ops/grantByUser": {"put": {"tags": ["OpsStrategyController"], "summary": "修改用户的策略组", "operationId": "opsGrantByUserPut", "parameters": [{"name": "userId", "in": "query", "description": "userId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "groupId", "in": "query", "description": "groupId", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ops/group": {"post": {"tags": ["OpsStrategyController"], "summary": "创建策略分组", "operationId": "opsGroupPost", "parameters": [{"name": "createTime", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "creator", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "description", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "id", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "name", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "teamId", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«OpsStrategyGroupDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ops/group/{id}": {"get": {"tags": ["OpsStrategyController"], "summary": "获取策略分组详情", "operationId": "opsGroupByIdGet", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«OpsStrategyGroupVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "put": {"tags": ["OpsStrategyController"], "summary": "修改策略分组名称和描述", "operationId": "opsGroupByIdPut", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "name", "in": "query", "description": "name", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "description", "in": "query", "description": "description", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«OpsStrategyGroupDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "delete": {"tags": ["OpsStrategyController"], "summary": "删除策略分组", "operationId": "opsGroupByIdDelete", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ops/group/{id}/grant": {"put": {"tags": ["OpsStrategyController"], "summary": "将目标添加到策略组", "operationId": "opsGroupByIdGrantPut", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GrantOpsStrategyRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ops/group/{id}/item": {"put": {"tags": ["OpsStrategyController"], "summary": "更新策略条目", "operationId": "opsGroupByIdItemPut", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OpsStrategyItemVo"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ops/group/{id}/strategy": {"put": {"tags": ["OpsStrategyController"], "summary": "更新策略", "operationId": "opsGroupByIdStrategyPut", "parameters": [{"name": "id", "in": "path", "description": "id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateOpsStrategyItemRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ops/groupV2": {"post": {"tags": ["OpsStrategyController"], "summary": "创建策略分组V2", "operationId": "opsGroupV2Post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateOpsStrategyGroupRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«OpsStrategyGroupDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/ops/groups": {"get": {"tags": ["OpsStrategyController"], "summary": "分页获取策略分组", "operationId": "opsGroupsGet", "parameters": [{"name": "pageNum", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "sortField", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "sortOrder", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«OpsStrategyGroupVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/webhook/op/stat": {"get": {"tags": ["OpsWebhookController"], "summary": "获取用户和分身数量", "operationId": "webhookOpStatGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«OpStatVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/account/checkEmailOrPhone": {"get": {"tags": ["RegisterController"], "summary": "检查手机号或邮箱是否合法", "description": "如果是手机号，区号连接到account前面", "operationId": "accountCheckEmailOrPhoneGet", "parameters": [{"name": "areaCode", "in": "query", "description": "areaCode", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "account", "in": "query", "description": "account", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/account/checkInviteCode": {"get": {"tags": ["RegisterController"], "summary": "校验邀请码", "operationId": "accountCheckInviteCodeGet", "parameters": [{"name": "inviteCode", "in": "query", "description": "inviteCode", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/account/register": {"post": {"tags": ["RegisterController"], "summary": "注册账号", "operationId": "accountRegisterPost", "parameters": [{"name": "inviteCode", "in": "query", "description": "邀请码，非必需", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "areaCode", "in": "query", "description": "areaCode", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "account", "in": "query", "description": "账号（手机号或邮箱）", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "verifyCode", "in": "query", "description": "验证码", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RegisterAccountVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/account/registerConfig": {"get": {"tags": ["RegisterController"], "summary": "系统注册配置信息", "operationId": "accountRegisterConfigGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RegisterConfig»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/account/verifyCode": {"get": {"tags": ["RegisterController"], "summary": "获取验证码", "operationId": "accountVerifyCodeGet", "parameters": [{"name": "areaCode", "in": "query", "description": "areaCode", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "account", "in": "query", "description": "账号（手机号或邮箱）", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "action", "in": "query", "description": "进行的操作", "required": true, "style": "form", "schema": {"type": "string", "enum": ["bindEmail", "download", "findPassword", "joinTeam", "login", "mobile3e", "partnerApply", "register", "resetPhone", "testing", "twoFa"]}}, {"name": "<PERSON><PERSON>a", "in": "query", "description": "<PERSON><PERSON>a", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/resource/checkAuthorized": {"get": {"tags": ["ResourceController"], "summary": "检查资源是否被当前用户授权", "operationId": "resourceCheckAuthorizedGet", "parameters": [{"name": "resourceType", "in": "query", "description": "resourceType", "required": true, "style": "form", "schema": {"type": "string", "enum": ["AK", "Activity", "Audit", "BlockElements", "Cloud", "CrsOrder", "CrsProduct", "DiskFile", "FingerPrint", "FingerPrintTemplate", "Gateway", "GhCreator", "GhGifter", "GhJobPlan", "GhUser", "GhVideoCreator", "GiftCardPack", "InsTeamUser", "InsUser", "Invoice", "Ip", "IpPool", "IppIp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "KakaoFriend", "KolCreator", "MobileAccount", "None", "Orders", "PluginTeamPack", "Record", "RpaFlow", "RpaTask", "RpaTaskItem", "RpaVoucher", "Shop", "ShopSession", "Tag", "TeamDiskRoot", "TeamMobile", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TkCreator", "TkTeamPack", "Tkshop", "<PERSON>ks<PERSON><PERSON>uyer", "TkshopCreator", "TrafficPack", "TunnelVps", "Users", "View", "Voucher", "XhsAccount"]}}, {"name": "resourceId", "in": "query", "description": "resourceId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/resource/grant": {"post": {"tags": ["ResourceController"], "summary": "授权给用户和部门", "operationId": "resourceGrantPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResourceGrantRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/resource/grant/{resourceType}/{resourceId}": {"get": {"tags": ["ResourceController"], "summary": "获取资源的授权信息", "operationId": "resourceGrantByResourceTypeByResourceIdGet", "parameters": [{"name": "resourceType", "in": "path", "description": "resourceType", "required": true, "style": "simple", "schema": {"type": "string", "enum": ["AK", "Activity", "Audit", "BlockElements", "Cloud", "CrsOrder", "CrsProduct", "DiskFile", "FingerPrint", "FingerPrintTemplate", "Gateway", "GhCreator", "GhGifter", "GhJobPlan", "GhUser", "GhVideoCreator", "GiftCardPack", "InsTeamUser", "InsUser", "Invoice", "Ip", "IpPool", "IppIp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "KakaoFriend", "KolCreator", "MobileAccount", "None", "Orders", "PluginTeamPack", "Record", "RpaFlow", "RpaTask", "RpaTaskItem", "RpaVoucher", "Shop", "ShopSession", "Tag", "TeamDiskRoot", "TeamMobile", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TkCreator", "TkTeamPack", "Tkshop", "<PERSON>ks<PERSON><PERSON>uyer", "TkshopCreator", "TrafficPack", "TunnelVps", "Users", "View", "Voucher", "XhsAccount"]}}, {"name": "resourceId", "in": "path", "description": "resourceId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«GrantInfo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/support/request": {"post": {"tags": ["SupportController"], "summary": "请求远程支持", "description": "返回支持码", "operationId": "supportRequestPost", "parameters": [{"name": "timezone", "in": "query", "description": "timezone", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "lang", "in": "query", "description": "lang", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "clientTimestamp", "in": "query", "description": "clientTimestamp", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/": {"post": {"tags": ["TeamController"], "summary": "创建团队", "operationId": "teamPost", "parameters": [{"name": "teamName", "in": "query", "description": "团队名称", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "teamType", "in": "query", "description": "teamType", "required": false, "style": "form", "schema": {"type": "string", "enum": ["crs", "gh", "krShop", "normal", "partner", "plugin", "tk", "tkshop"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TeamDto»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/audit/{auditId}": {"get": {"tags": ["TeamController"], "summary": "获取审核详情", "operationId": "teamAuditByAuditIdGet", "parameters": [{"name": "auditId", "in": "path", "description": "auditId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«AuditDetailVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/audit/{auditId}/pass": {"put": {"tags": ["TeamController"], "summary": "通过审批", "operationId": "teamAuditByAuditIdPassPut", "parameters": [{"name": "auditId", "in": "path", "description": "auditId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "teamNickname", "in": "query", "description": "团队昵称", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "roleCode", "in": "query", "description": "默认角色code", "required": false, "style": "form", "schema": {"type": "string", "enum": ["boss", "manager", "staff", "superadmin"]}}, {"name": "departmentIds", "in": "query", "description": "部门ID，不设置时放在根部门（用逗号连接）", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/audit/{auditId}/reject": {"put": {"tags": ["TeamController"], "summary": "拒绝审批", "operationId": "teamAuditByAuditIdRejectPut", "parameters": [{"name": "auditId", "in": "path", "description": "auditId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "reason", "in": "query", "description": "reason", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/audits": {"get": {"tags": ["TeamController"], "summary": "获取成员审核列表", "operationId": "teamAuditsGet", "parameters": [{"name": "teamId", "in": "query", "description": "teamId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "auditType", "in": "query", "description": "auditType", "required": false, "style": "form", "schema": {"type": "string", "enum": ["JOIN_TEAM", "NEW_DEVICE", "RECEIVE_SHOP", "RECEIVE_SHOPS", "SHARED_MOBILES", "SHARED_SHOP", "SHARED_SHOPS", "TRANSFER_SHOP"]}}, {"name": "auditStatus", "in": "query", "description": "auditStatus", "required": false, "style": "form", "schema": {"type": "string", "enum": ["APPROVED", "CANCEL", "NEW", "NOT_PASS"]}}, {"name": "done", "in": "query", "description": "是否已完成", "required": false, "style": "form", "schema": {"type": "boolean"}}, {"name": "word", "in": "query", "description": "昵称或手机号搜索关键字", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "pageNum", "in": "query", "description": "pageNum", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«AuditResultVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/avatar/{file}": {"get": {"tags": ["TeamController"], "summary": "查看团队头像", "operationId": "teamAvatarByFileGet", "parameters": [{"name": "file", "in": "path", "description": "file", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/free-quotas": {"get": {"tags": ["TeamController"], "summary": "获取官方免费配额", "operationId": "teamFreeQuotasGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«TeamQuotaVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/function-configs": {"get": {"tags": ["TeamController"], "summary": "获取指定团队功能配置详情", "operationId": "teamFunctionConfigsGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«TeamFunctionConfigVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/inviteInfo/{inviteCode}": {"get": {"tags": ["TeamController"], "summary": "根据邀请码获取邀请详情", "operationId": "teamInviteInfoByInviteCodeGet", "parameters": [{"name": "inviteCode", "in": "path", "description": "inviteCode", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«InviteInfoVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/inviteLink": {"get": {"tags": ["TeamController"], "summary": "获取邀请加入团队链接", "operationId": "teamInviteLinkGet", "parameters": [{"name": "teamId", "in": "query", "description": "团队ID", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«InviteLinkVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": true, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/join": {"post": {"tags": ["TeamController"], "summary": "加入团队", "operationId": "teamJoinPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/JoinTeamParamVo"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«JoinTeamResultVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/joinList": {"get": {"tags": ["TeamController"], "summary": "获取已经加入的团队列表", "operationId": "teamJoinListGet", "parameters": [{"name": "status", "in": "query", "description": "status", "required": false, "style": "form", "explode": true, "schema": {"type": "string", "enum": ["Blocked", "Deleted", "Pending", "Ready"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«TeamWithRoleVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/loginTeam/{teamId}": {"put": {"tags": ["TeamController"], "summary": "队页面点进入团队按钮之后需要调用该接口", "operationId": "teamLoginTeamByTeamIdPut", "parameters": [{"name": "teamId", "in": "path", "description": "teamId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/quotaDetail": {"get": {"tags": ["TeamController"], "summary": "获取团队特定配额详情", "operationId": "teamQuotaDetailGet", "parameters": [{"name": "quotaName", "in": "query", "description": "quotaName", "required": true, "style": "form", "schema": {"type": "string", "enum": ["ShopQuota", "ShopSecurityPolicy", "StorageQuota", "TeamMemberQuota", "Team<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TeamQuotaDetailVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/quotas": {"get": {"tags": ["TeamController"], "summary": "获取指定团队配额详情", "operationId": "teamQuotasGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«TeamQuotaVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/remainQuota": {"get": {"tags": ["TeamController"], "summary": "获取指定项目的剩余配额", "operationId": "teamRemainQuotaGet", "parameters": [{"name": "quotaName", "in": "query", "description": "quotaName", "required": true, "style": "form", "schema": {"type": "string", "enum": ["ShopQuota", "ShopSecurityPolicy", "StorageQuota", "TeamMemberQuota", "Team<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«int»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/roleConfig": {"get": {"tags": ["TeamController"], "summary": "角色配置信息", "operationId": "teamRoleConfigGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TeamRoleConfig»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/roleFunction": {"put": {"tags": ["TeamController"], "summary": "修订角色默认权限", "operationId": "teamRoleFunctionPut", "parameters": [{"name": "code", "in": "query", "description": "code", "required": true, "style": "form", "schema": {"type": "string", "enum": ["boss", "manager", "staff", "superadmin"]}}, {"name": "customGranted", "in": "query", "description": "customGranted", "required": true, "style": "form", "schema": {"type": "boolean"}}, {"name": "function", "in": "query", "description": "function", "required": true, "style": "form", "schema": {"type": "string", "enum": ["DISK_SPACE_MANAGER", "DISK_TEAM_MANAGER", "DISK_USER_MANAGER", "EXPENSE_MANAGE", "EXPENSE_VIEW", "EXTENSION_LIST", "EXTENSION_MANAGER", "FINGERPRINT_CONFIG", "FINGERPRINT_LIST", "FINGERPRINT_MANAGER", "IP_CONFIG", "IP_LIST", "IP_MANAGE", "KOL_LIST", "KOL_MANAGE", "MOBILE_CONFIG", "MOBILE_IMPORT_DELETE", "OPERATE_LOG_GET_IP", "OPERATE_LOG_GET_LOGIN", "OPERATE_LOG_GET_SHOP", "OPERATE_LOG_GET_TEAM_MANAGE", "OPERATE_LOG_MANAGE_SHOP", "RPA_CARD_MANAGER", "RPA_CREATE_DELETE", "RPA_LIST", "RPA_OPEN_API", "RPA_PLAN", "RPA_RUN", "SHOP_AUTHORIZE", "SHOP_BIND_IP_MANAGE", "SHOP_CONFIG", "SHOP_FINGERPRINT_MANAGE", "SHOP_IMPORT_DELETE", "TEAM_AUDIT", "TEAM_CRITICAL_MANAGE", "TEAM_MANAGE", "TEAM_RESOURCE_MANAGE", "TEAM_VIEW_MEMBER", "TKSHOP_BUYER_MANAGER", "TKSHOP_CREATOR_MANAGER", "TKSHOP_GLOBAL_CREATOR_MANAGER", "TKSHOP_MANAGE", "TK_PACK_MANAGE", "TK_流程计划编排", "TK_达人管理", "TK_达人运营", "TK_达人邀约", "TK_运营辅助"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/roleFunctionBatch": {"put": {"tags": ["TeamController"], "summary": "批量修订角色默认权限", "operationId": "teamRoleFunctionBatchPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SetRoleFunctionRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/rpaDevices": {"get": {"tags": ["TeamController"], "summary": "查询团队的RPA限定设备", "operationId": "teamRpaDevicesGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«LoginDeviceDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/systemConfig": {"get": {"tags": ["TeamController"], "summary": "获取团队系统配置", "operationId": "teamSystemConfigGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TeamSystemConfig»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/user/{userId}/shopTransfer": {"put": {"tags": ["TeamController"], "summary": "交接账户", "operationId": "teamUserByUserIdShopTransferPut", "parameters": [{"name": "userId", "in": "path", "description": "userId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserShopTransferParamVo"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/user/{userId}/status": {"put": {"tags": ["TeamController"], "summary": "更新成员状态", "operationId": "teamUserByUserIdStatusPut", "parameters": [{"name": "userId", "in": "path", "description": "userId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "status", "in": "query", "description": "status", "required": true, "style": "form", "schema": {"type": "string", "enum": ["ACTIVE", "BLOCK", "DELETED", "INACTIVE"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/{teamId}": {"get": {"tags": ["TeamController"], "summary": "获取指定团队详情", "operationId": "teamByTeamIdGet", "parameters": [{"name": "teamId", "in": "path", "description": "团队ID", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TeamDetailVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "put": {"tags": ["TeamController"], "summary": "修改团队信息", "operationId": "teamByTeamIdPut", "parameters": [{"name": "teamId", "in": "path", "description": "teamId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "teamName", "in": "query", "description": "teamName", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/{teamId}/avatar": {"get": {"tags": ["TeamController"], "summary": "获取团队头像URL", "operationId": "teamByTeamIdAvatarGet", "parameters": [{"name": "teamId", "in": "path", "description": "teamId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "post": {"tags": ["TeamController"], "summary": "更新团队头像", "operationId": "teamByTeamIdAvatarPost", "parameters": [{"name": "teamId", "in": "path", "description": "teamId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/{teamId}/config": {"put": {"tags": ["TeamController"], "summary": "修改团队设置", "operationId": "teamByTeamIdConfigPut", "parameters": [{"name": "teamId", "in": "path", "description": "teamId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "inviteEnable", "in": "query", "description": "inviteEnable", "required": true, "style": "form", "schema": {"type": "boolean"}}, {"name": "inviteAuditEnable", "in": "query", "description": "inviteAuditEnable", "required": true, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/{teamId}/contactByPhone/{phone}": {"get": {"tags": ["TeamController"], "summary": "查看其他团队的一个联系人信息", "operationId": "teamByTeamIdContactByPhoneByPhoneGet", "parameters": [{"name": "teamId", "in": "path", "description": "teamId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "areaCode", "in": "query", "description": "areaCode", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "phone", "in": "path", "description": "phone", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/{teamId}/dissolve": {"put": {"tags": ["TeamController"], "summary": "解散团队", "operationId": "teamByTeamIdDissolvePut", "parameters": [{"name": "teamId", "in": "path", "description": "teamId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/{teamId}/favorite": {"put": {"tags": ["TeamController"], "summary": "（取消）收藏团队", "operationId": "teamByTeamIdFavoritePut", "parameters": [{"name": "teamId", "in": "path", "description": "teamId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "favorite", "in": "query", "description": "favorite", "required": true, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/{teamId}/inviteInfo": {"get": {"tags": ["TeamController"], "summary": "获取邀请信息", "operationId": "teamByTeamIdInviteInfoGet", "parameters": [{"name": "teamId", "in": "path", "description": "teamId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TeamInviteVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/{teamId}/refreshInviteCode": {"get": {"tags": ["TeamController"], "summary": "刷新我的邀请链接", "operationId": "teamByTeamIdRefreshInviteCodeGet", "parameters": [{"name": "teamId", "in": "path", "description": "teamId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/{teamId}/stat": {"get": {"tags": ["TeamController"], "summary": "获取指定团队统计信息", "operationId": "teamByTeamIdStatGet", "parameters": [{"name": "teamId", "in": "path", "description": "teamId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TeamStatInfo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/{teamId}/transfer": {"put": {"tags": ["TeamController"], "summary": "转移团队BOSS", "operationId": "teamByTeamIdTransferPut", "parameters": [{"name": "teamId", "in": "path", "description": "teamId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "toUserId", "in": "query", "description": "toUserId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/{teamId}/verifiedInfo": {"get": {"tags": ["TeamController"], "summary": "实名认证信息", "operationId": "teamByTeamIdVerifiedInfoGet", "parameters": [{"name": "teamId", "in": "path", "description": "团队ID", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TeamVerifyDto»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/favorites": {"post": {"tags": ["TeamFavoriteController"], "summary": "添加收藏", "operationId": "teamFavoritesPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddFavorateRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«int»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/favorites/byType/{resourceType}": {"get": {"tags": ["TeamFavoriteController"], "summary": "按类型查询收藏列表", "operationId": "teamFavoritesByTypeByResourceTypeGet", "parameters": [{"name": "resourceType", "in": "path", "description": "resourceType", "required": true, "style": "simple", "schema": {"type": "string", "enum": ["AK", "Activity", "Audit", "BlockElements", "Cloud", "CrsOrder", "CrsProduct", "DiskFile", "FingerPrint", "FingerPrintTemplate", "Gateway", "GhCreator", "GhGifter", "GhJobPlan", "GhUser", "GhVideoCreator", "GiftCardPack", "InsTeamUser", "InsUser", "Invoice", "Ip", "IpPool", "IppIp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "KakaoFriend", "KolCreator", "MobileAccount", "None", "Orders", "PluginTeamPack", "Record", "RpaFlow", "RpaTask", "RpaTaskItem", "RpaVoucher", "Shop", "ShopSession", "Tag", "TeamDiskRoot", "TeamMobile", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TkCreator", "TkTeamPack", "Tkshop", "<PERSON>ks<PERSON><PERSON>uyer", "TkshopCreator", "TrafficPack", "TunnelVps", "Users", "View", "Voucher", "XhsAccount"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«TeamFavoriteDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": true, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/favorites/checkByType/{resourceType}": {"post": {"tags": ["TeamFavoriteController"], "summary": "按类型查询是否收藏", "operationId": "teamFavoritesCheckByTypeByResourceTypePost", "parameters": [{"name": "resourceType", "in": "path", "description": "resourceType", "required": true, "style": "simple", "schema": {"type": "string", "enum": ["AK", "Activity", "Audit", "BlockElements", "Cloud", "CrsOrder", "CrsProduct", "DiskFile", "FingerPrint", "FingerPrintTemplate", "Gateway", "GhCreator", "GhGifter", "GhJobPlan", "GhUser", "GhVideoCreator", "GiftCardPack", "InsTeamUser", "InsUser", "Invoice", "Ip", "IpPool", "IppIp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "KakaoFriend", "KolCreator", "MobileAccount", "None", "Orders", "PluginTeamPack", "Record", "RpaFlow", "RpaTask", "RpaTaskItem", "RpaVoucher", "Shop", "ShopSession", "Tag", "TeamDiskRoot", "TeamMobile", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TkCreator", "TkTeamPack", "Tkshop", "<PERSON>ks<PERSON><PERSON>uyer", "TkshopCreator", "TrafficPack", "TunnelVps", "Users", "View", "Voucher", "XhsAccount"]}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonIdsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«Map«long,boolean»»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/favorites/delete": {"post": {"tags": ["TeamFavoriteController"], "summary": "取消收藏", "operationId": "teamFavoritesDeletePost", "parameters": [{"name": "resourceType", "in": "query", "description": "resourceType", "required": true, "style": "form", "schema": {"type": "string", "enum": ["AK", "Activity", "Audit", "BlockElements", "Cloud", "CrsOrder", "CrsProduct", "DiskFile", "FingerPrint", "FingerPrintTemplate", "Gateway", "GhCreator", "GhGifter", "GhJobPlan", "GhUser", "GhVideoCreator", "GiftCardPack", "InsTeamUser", "InsUser", "Invoice", "Ip", "IpPool", "IppIp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "KakaoFriend", "KolCreator", "MobileAccount", "None", "Orders", "PluginTeamPack", "Record", "RpaFlow", "RpaTask", "RpaTaskItem", "RpaVoucher", "Shop", "ShopSession", "Tag", "TeamDiskRoot", "TeamMobile", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TkCreator", "TkTeamPack", "Tkshop", "<PERSON>ks<PERSON><PERSON>uyer", "TkshopCreator", "TrafficPack", "TunnelVps", "Users", "View", "Voucher", "XhsAccount"]}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonIdsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«int»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/favorites/detailByType/{resourceType}": {"get": {"tags": ["TeamFavoriteController"], "summary": "按类型查询收藏列表", "operationId": "teamFavoritesDetailByTypeByResourceTypeGet", "parameters": [{"name": "resourceType", "in": "path", "description": "resourceType", "required": true, "style": "simple", "schema": {"type": "string", "enum": ["AK", "Activity", "Audit", "BlockElements", "Cloud", "CrsOrder", "CrsProduct", "DiskFile", "FingerPrint", "FingerPrintTemplate", "Gateway", "GhCreator", "GhGifter", "GhJobPlan", "GhUser", "GhVideoCreator", "GiftCardPack", "InsTeamUser", "InsUser", "Invoice", "Ip", "IpPool", "IppIp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "KakaoFriend", "KolCreator", "MobileAccount", "None", "Orders", "PluginTeamPack", "Record", "RpaFlow", "RpaTask", "RpaTaskItem", "RpaVoucher", "Shop", "ShopSession", "Tag", "TeamDiskRoot", "TeamMobile", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TkCreator", "TkTeamPack", "Tkshop", "<PERSON>ks<PERSON><PERSON>uyer", "TkshopCreator", "TrafficPack", "TunnelVps", "Users", "View", "Voucher", "XhsAccount"]}}, {"name": "pageNum", "in": "query", "description": "pageNum", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«TeamFavoriteDetailVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/favorites/page": {"get": {"tags": ["TeamFavoriteController"], "summary": "分页查询收藏列表", "operationId": "teamFavoritesPageGet", "parameters": [{"name": "resourceType", "in": "query", "description": "resourceType", "required": false, "style": "form", "schema": {"type": "string", "enum": ["AK", "Activity", "Audit", "BlockElements", "Cloud", "CrsOrder", "CrsProduct", "DiskFile", "FingerPrint", "FingerPrintTemplate", "Gateway", "GhCreator", "GhGifter", "GhJobPlan", "GhUser", "GhVideoCreator", "GiftCardPack", "InsTeamUser", "InsUser", "Invoice", "Ip", "IpPool", "IppIp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "KakaoFriend", "KolCreator", "MobileAccount", "None", "Orders", "PluginTeamPack", "Record", "RpaFlow", "RpaTask", "RpaTaskItem", "RpaVoucher", "Shop", "ShopSession", "Tag", "TeamDiskRoot", "TeamMobile", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TkCreator", "TkTeamPack", "Tkshop", "<PERSON>ks<PERSON><PERSON>uyer", "TkshopCreator", "TrafficPack", "TunnelVps", "Users", "View", "Voucher", "XhsAccount"]}}, {"name": "collectTimeFrom", "in": "query", "description": "collectTimeFrom", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "collectTimeTo", "in": "query", "description": "collectTimeTo", "required": false, "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "query", "in": "query", "description": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "sortField", "in": "query", "description": "sortField", "required": false, "style": "form", "schema": {"type": "string", "enum": ["collectTime", "id", "name", "size"]}}, {"name": "sortOrder", "in": "query", "description": "sortOrder", "required": false, "style": "form", "schema": {"type": "string", "enum": ["asc", "desc"]}}, {"name": "pageNum", "in": "query", "description": "pageNum", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«TeamFavoriteDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team-member/config": {"get": {"tags": ["TeamMemberController"], "summary": "查询成员配置", "operationId": "teamMemberConfigGet", "parameters": [{"name": "key", "in": "query", "description": "key", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "put": {"tags": ["TeamMemberController"], "summary": "查询成员配置", "operationId": "teamMemberConfigPut", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateMemberConfigRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/settings/browserDebug": {"get": {"tags": ["TeamSettingsController"], "summary": "获取团队浏览器开启browserDebug配置: enabled | disabled ; 空表示由团队成员自行决定", "operationId": "teamSettingsBrowserDebugGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "put": {"tags": ["TeamSettingsController"], "summary": "团队浏览器开启browserDebug配置: enabled | disabled ; 空表示由团队成员自行决定 ", "operationId": "teamSettingsBrowserDebugPut", "parameters": [{"name": "browserDebug", "in": "query", "description": "browserDebug", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/settings/fingerUpgrade": {"get": {"tags": ["TeamSettingsController"], "summary": "获取团队指纹自动升级配置: notify | autoUpgrade ;", "operationId": "teamSettingsFingerUpgradeGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "put": {"tags": ["TeamSettingsController"], "summary": "设置团队指纹自动升级配置: notify | autoUpgrade", "operationId": "teamSettingsFingerUpgradePut", "parameters": [{"name": "fingerUpgrade", "in": "query", "description": "fingerUpgrade", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/settings/language": {"get": {"tags": ["TeamSettingsController"], "summary": "获取团队浏览器语言配置: en-US, zh-CN, zh-TW； null或未配置表示由团队成员自行决定", "operationId": "teamSettingsLanguageGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "put": {"tags": ["TeamSettingsController"], "summary": "设置团队浏览器语言配置: en-US, zh-CN, zh-TW；； 未配置表示由团队成员自行决定", "operationId": "teamSettingsLanguagePut", "parameters": [{"name": "language", "in": "query", "description": "language", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/settings/sessionRequireIp": {"get": {"tags": ["TeamSettingsController"], "summary": "打开会话要求绑定IP", "operationId": "teamSettingsSessionRequireIpGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«boolean»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "put": {"tags": ["TeamSettingsController"], "summary": "打开会话要求绑定IP", "operationId": "teamSettingsSessionRequireIpPut", "parameters": [{"name": "sessionRequireIp", "in": "query", "description": "sessionRequireIp", "required": true, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/settings/shopCodeStatus": {"get": {"tags": ["TeamSettingsController"], "summary": "获取团队浏览器开启shopCodeStatus配置: enabled | disabled ", "operationId": "teamSettingsShopCodeStatusGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "put": {"tags": ["TeamSettingsController"], "summary": "更新获取团队浏览器开启shopCodeStatus配置: enabled | disabled ", "operationId": "teamSettingsShopCodeStatusPut", "parameters": [{"name": "codeStatus", "in": "query", "description": "codeStatus", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/settings/teamConfig": {"get": {"tags": ["TeamSettingsController"], "summary": "查询teamConfig", "operationId": "teamSettingsTeamConfigGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«TeamConfigDto»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/settings/teamConfig/deleteShopFp/{deleteShopFp}": {"put": {"tags": ["TeamSettingsController"], "summary": "设置删除分身时，是否删除指纹", "operationId": "teamSettingsTeamConfigDeleteShopFpByDeleteShopFpPut", "parameters": [{"name": "deleteShopFp", "in": "path", "description": "deleteShopFp", "required": true, "style": "simple", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/verify/company4Element": {"post": {"tags": ["TeamVerifyController"], "summary": "企业四要素认证", "operationId": "teamVerifyCompany4ElementPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Company4ElementVo"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/verify/invalidate": {"get": {"tags": ["TeamVerifyController"], "summary": "解除实名认证", "operationId": "teamVerifyInvalidateGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/team/verify/mobile3Element": {"post": {"tags": ["TeamVerifyController"], "summary": "手机三要素认证", "operationId": "teamVerifyMobile3ElementPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Mobile3ElementVo"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«Mobile3ElementResult»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/user/byTeam/{teamId}/users": {"get": {"tags": ["UserController"], "summary": "获取指定团队的成员列表", "operationId": "userByTeamByTeamIdUsersGet", "parameters": [{"name": "teamId", "in": "path", "description": "teamId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "pageNum", "in": "query", "description": "pageNum", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "departmentId", "in": "query", "description": "departmentId", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "searchWord", "in": "query", "description": "searchWord", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "excludeMyself", "in": "query", "description": "是否排除当前用户", "required": false, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«UserDetailVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/user/data/roleFunction/repair": {"get": {"tags": ["UserController"], "summary": "修复角色权限数据", "operationId": "userDataRoleFunctionRepairGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/user/exitTeam": {"put": {"tags": ["UserController"], "summary": "用户主动退出团队", "operationId": "userExitTeamPut", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/user/grantShops": {"post": {"tags": ["UserController"], "summary": "给用户授权账户", "operationId": "userGrantShopsPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GrantShopsParamVo"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/user/memberGrantList": {"get": {"tags": ["UserController"], "summary": "获取团队成员列表（含授权信息）", "operationId": "userMemberGrantListGet", "parameters": [{"name": "pageNum", "in": "query", "description": "pageNum", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": true, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "departmentId", "in": "query", "description": "departmentId", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "searchWord", "in": "query", "description": "searchWord", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "resourceType", "in": "query", "description": "resourceType", "required": false, "style": "form", "schema": {"type": "string", "enum": ["AK", "Activity", "Audit", "BlockElements", "Cloud", "CrsOrder", "CrsProduct", "DiskFile", "FingerPrint", "FingerPrintTemplate", "Gateway", "GhCreator", "GhGifter", "GhJobPlan", "GhUser", "GhVideoCreator", "GiftCardPack", "InsTeamUser", "InsUser", "Invoice", "Ip", "IpPool", "IppIp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "KakaoFriend", "KolCreator", "MobileAccount", "None", "Orders", "PluginTeamPack", "Record", "RpaFlow", "RpaTask", "RpaTaskItem", "RpaVoucher", "Shop", "ShopSession", "Tag", "TeamDiskRoot", "TeamMobile", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TkCreator", "TkTeamPack", "Tkshop", "<PERSON>ks<PERSON><PERSON>uyer", "TkshopCreator", "TrafficPack", "TunnelVps", "Users", "View", "Voucher", "XhsAccount"]}}, {"name": "resourceId", "in": "query", "description": "resourceId", "required": false, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "excludeMyself", "in": "query", "description": "是否排除当前用户", "required": false, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«UserDetailVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/user/teamNickname": {"put": {"tags": ["UserController"], "summary": "修改当前用户的团队昵称", "operationId": "userTeamNicknamePut", "parameters": [{"name": "teamNickname", "in": "query", "description": "teamNickname", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/user/{userId}": {"get": {"tags": ["UserController"], "summary": "获取指定用户详情", "operationId": "userByUserIdGet", "parameters": [{"name": "userId", "in": "path", "description": "userId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«UserTeamVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/user/{userId}/functions": {"get": {"tags": ["UserController"], "summary": "获取用户对所有权限的元信息", "operationId": "userByUserIdFunctionsGet", "parameters": [{"name": "userId", "in": "path", "description": "userId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«UserFunctionMetaVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "put": {"tags": ["UserController"], "summary": "更新用户权限", "operationId": "userByUserIdFunctionsPut", "parameters": [{"name": "userId", "in": "path", "description": "userId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "functions", "in": "query", "description": "权限code列表，以逗号分隔", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/user/{userId}/functionsV2": {"put": {"tags": ["UserController"], "summary": "更新用户权限V2", "operationId": "userByUserIdFunctionsV2Put", "parameters": [{"name": "userId", "in": "path", "description": "userId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "grantedFunctions", "in": "query", "description": "权限code列表，以逗号分隔", "required": true, "style": "form", "schema": {"type": "string"}}, {"name": "revokedFunctions", "in": "query", "description": "权限code列表，以逗号分隔", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/user/{userId}/grant": {"put": {"tags": ["UserController"], "summary": "强制授权或剥夺权限或取消", "operationId": "userByUserIdGrantPut", "parameters": [{"name": "userId", "in": "path", "description": "userId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "function", "in": "query", "description": "function", "required": true, "style": "form", "schema": {"type": "string", "enum": ["DISK_SPACE_MANAGER", "DISK_TEAM_MANAGER", "DISK_USER_MANAGER", "EXPENSE_MANAGE", "EXPENSE_VIEW", "EXTENSION_LIST", "EXTENSION_MANAGER", "FINGERPRINT_CONFIG", "FINGERPRINT_LIST", "FINGERPRINT_MANAGER", "IP_CONFIG", "IP_LIST", "IP_MANAGE", "KOL_LIST", "KOL_MANAGE", "MOBILE_CONFIG", "MOBILE_IMPORT_DELETE", "OPERATE_LOG_GET_IP", "OPERATE_LOG_GET_LOGIN", "OPERATE_LOG_GET_SHOP", "OPERATE_LOG_GET_TEAM_MANAGE", "OPERATE_LOG_MANAGE_SHOP", "RPA_CARD_MANAGER", "RPA_CREATE_DELETE", "RPA_LIST", "RPA_OPEN_API", "RPA_PLAN", "RPA_RUN", "SHOP_AUTHORIZE", "SHOP_BIND_IP_MANAGE", "SHOP_CONFIG", "SHOP_FINGERPRINT_MANAGE", "SHOP_IMPORT_DELETE", "TEAM_AUDIT", "TEAM_CRITICAL_MANAGE", "TEAM_MANAGE", "TEAM_RESOURCE_MANAGE", "TEAM_VIEW_MEMBER", "TKSHOP_BUYER_MANAGER", "TKSHOP_CREATOR_MANAGER", "TKSHOP_GLOBAL_CREATOR_MANAGER", "TKSHOP_MANAGE", "TK_PACK_MANAGE", "TK_流程计划编排", "TK_达人管理", "TK_达人运营", "TK_达人邀约", "TK_运营辅助"]}}, {"name": "granted", "in": "query", "description": "granted", "required": false, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/user/{userId}/grantList": {"get": {"tags": ["UserController"], "summary": "获取用户强制授权或剥夺列表", "operationId": "userByUserIdGrantListGet", "parameters": [{"name": "userId", "in": "path", "description": "userId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«UserFunctionDto»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/user/{userId}/grantedFunctions": {"get": {"tags": ["UserController"], "summary": "获取用户已经授权的权限", "operationId": "userByUserIdGrantedFunctionsGet", "parameters": [{"name": "userId", "in": "path", "description": "userId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«List«FunctionVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/user/{userId}/kickOut": {"put": {"tags": ["UserController"], "summary": "踢出团队成员", "operationId": "userByUserIdKickOutPut", "parameters": [{"name": "userId", "in": "path", "description": "userId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/user/{userId}/role": {"get": {"tags": ["UserController"], "summary": "获取用户角色", "operationId": "userByUserIdRoleGet", "parameters": [{"name": "userId", "in": "path", "description": "userId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«RoleVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "put": {"tags": ["UserController"], "summary": "设置用户角色", "operationId": "userByUserIdRolePut", "parameters": [{"name": "userId", "in": "path", "description": "userId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "role", "in": "query", "description": "role", "required": true, "style": "form", "schema": {"type": "string", "enum": ["boss", "manager", "staff", "superadmin"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/user/{userId}/teamNickname": {"get": {"tags": ["UserController"], "summary": "获取当前用户的团队昵称", "operationId": "userByUserIdTeamNicknameGet", "parameters": [{"name": "userId", "in": "path", "description": "userId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "put": {"tags": ["UserController"], "summary": "修改指定用户的团队昵称", "operationId": "userByUserIdTeamNicknamePut", "parameters": [{"name": "userId", "in": "path", "description": "userId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "nickname", "in": "query", "description": "nickname", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/p/{code}": {"get": {"tags": ["UserPromotionController"], "summary": "记录推广码", "operationId": "pByCodeGet", "parameters": [{"name": "code", "in": "path", "description": "code", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/promotion/gainReward": {"post": {"tags": ["UserPromotionController"], "summary": "领取奖励", "operationId": "promotionGainRewardPost", "parameters": [{"name": "id", "in": "query", "description": "id", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«GiftCardPackDetailVo»"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/promotion/getRewardObject": {"get": {"tags": ["UserPromotionController"], "summary": "查看奖励内容", "operationId": "promotionGetRewardObjectGet", "parameters": [{"name": "id", "in": "query", "description": "id", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«GiftCardPackDetailVo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/promotion/myPromoUrl": {"get": {"tags": ["UserPromotionController"], "summary": "获取我的个人邀请码信息", "operationId": "promotionMyPromoUrlGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«UserPromoInfo»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/promotion/promotedUsers": {"get": {"tags": ["UserPromotionController"], "summary": "查询用户发展的用户列表", "operationId": "promotionPromotedUsersGet", "parameters": [{"name": "pageNum", "in": "query", "description": "pageNum", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«PageResult«UserPromotedRecordVo»»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}, "/api/user/settings/language": {"get": {"tags": ["UserSettingsController"], "summary": "获取用户浏览器语言配置: en-US, zh-CN, zh-TW； null或未配置表示由客户端电脑环境决定", "operationId": "userSettingsLanguageGet", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult«string»"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}, "put": {"tags": ["UserSettingsController"], "summary": "设置用户浏览器语言配置: en-US, zh-CN, zh-TW； 未配置表示由客户端电脑环境决定", "operationId": "userSettingsLanguagePut", "parameters": [{"name": "language", "in": "query", "description": "language", "required": true, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebResult"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "security": [{"Authorization": ["global"]}, {"x-dm-team-id": ["global"]}]}}}, "components": {"schemas": {"AddFavorateRequest": {"title": "AddFavorateRequest", "type": "object", "properties": {"resourceItems": {"type": "array", "items": {"$ref": "#/components/schemas/FavoriteItem"}}, "resourceType": {"type": "string", "enum": ["AK", "Activity", "Audit", "BlockElements", "Cloud", "CrsOrder", "CrsProduct", "DiskFile", "FingerPrint", "FingerPrintTemplate", "Gateway", "GhCreator", "GhGifter", "GhJobPlan", "GhUser", "GhVideoCreator", "GiftCardPack", "InsTeamUser", "InsUser", "Invoice", "Ip", "IpPool", "IppIp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "KakaoFriend", "KolCreator", "MobileAccount", "None", "Orders", "PluginTeamPack", "Record", "RpaFlow", "RpaTask", "RpaTaskItem", "RpaVoucher", "Shop", "ShopSession", "Tag", "TeamDiskRoot", "TeamMobile", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TkCreator", "TkTeamPack", "Tkshop", "<PERSON>ks<PERSON><PERSON>uyer", "TkshopCreator", "TrafficPack", "TunnelVps", "Users", "View", "Voucher", "XhsAccount"]}}}, "AlipayPreconsultRequest": {"title": "AlipayPreconsultRequest", "type": "object", "properties": {"certName": {"type": "string", "description": "姓名"}, "certNo": {"type": "string", "description": "身份证号"}, "loginId": {"type": "string", "description": "支付宝登录账号，可选"}, "mobile": {"type": "string", "description": "支付宝绑定手机号，可选"}}}, "AlipayPreconsultResult": {"title": "AlipayPreconsultResult", "type": "object", "properties": {"url": {"type": "string"}, "verifyId": {"type": "string"}}}, "AlipayVerifyResultVo": {"title": "AlipayVerifyResultVo", "type": "object", "properties": {"done": {"type": "boolean"}, "message": {"type": "string"}, "responseObj": {"type": "object"}, "success": {"type": "boolean"}}}, "AuditDetailVo": {"title": "AuditDetailVo", "type": "object", "properties": {"applyUser": {"description": "申请人", "$ref": "#/components/schemas/UserVo"}, "applyUserId": {"type": "integer", "format": "int64"}, "applyUserNickname": {"type": "string"}, "auditTime": {"type": "string", "format": "date-time"}, "bizId": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "handleUser": {"description": "处理人", "$ref": "#/components/schemas/UserVo"}, "handleUserId": {"type": "integer", "format": "int64"}, "handleUserNickname": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "paramContent": {"type": "string"}, "remark": {"type": "string"}, "status": {"type": "string", "enum": ["APPROVED", "CANCEL", "NEW", "NOT_PASS"]}, "teamId": {"type": "integer", "format": "int64"}, "teamName": {"type": "string"}, "type": {"type": "string", "enum": ["JOIN_TEAM", "NEW_DEVICE", "RECEIVE_SHOP", "RECEIVE_SHOPS", "SHARED_MOBILES", "SHARED_SHOP", "SHARED_SHOPS", "TRANSFER_SHOP"]}}}, "AuditResultVo": {"title": "AuditResultVo", "type": "object", "properties": {"done": {"type": "integer", "description": "已完成总数", "format": "int32"}, "page": {"$ref": "#/components/schemas/PageResult«AuditDetailVo»"}, "pending": {"type": "integer", "description": "待审批总数", "format": "int32"}}}, "BizLinkDto": {"title": "BizLinkDto", "type": "object", "properties": {"bizData": {"type": "string"}, "bizType": {"type": "string", "enum": ["InviteJoinTeam"]}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "creatorName": {"type": "string"}, "expireSeconds": {"type": "integer", "format": "int32"}, "id": {"type": "integer", "format": "int64"}, "lastAccessTime": {"type": "string", "format": "date-time"}, "linkStatus": {"type": "string", "enum": ["Expired", "InValid", "Init", "Used", "<PERSON><PERSON>"]}, "oneoff": {"type": "boolean"}, "targetEmail": {"type": "string"}, "targetUser": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}, "teamName": {"type": "string"}, "token": {"type": "string"}, "url": {"type": "string"}}}, "CheckAndJoinDemoTeamResult": {"title": "CheckAndJoinDemoTeamResult", "type": "object", "properties": {"demoTeam": {"$ref": "#/components/schemas/TeamDto"}, "joinStatus": {"type": "string", "enum": ["ALREADY_IN_AUDIT", "ALREADY_IN_TEAM", "JOIN_SUCCESS", "WAIT_AUDIT"]}}}, "CommonIdsRequest": {"title": "CommonIdsRequest", "type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "Company4ElementVo": {"title": "Company4ElementVo", "type": "object", "properties": {"entname": {"type": "string", "description": "企业名称"}, "idcard": {"type": "string", "description": "企业法人身份证号"}, "name": {"type": "string", "description": "企业法人姓名"}, "regno": {"type": "string", "description": "企业工商注册号/统一信用代码"}}}, "ConfirmInviteJoinTeamResult": {"title": "ConfirmInviteJoinTeamResult", "type": "object", "properties": {"link": {"$ref": "#/components/schemas/BizLinkDto"}, "loginResult": {"$ref": "#/components/schemas/LoginResultVo"}}}, "CreateOpsStrategyGroupRequest": {"title": "CreateOpsStrategyGroupRequest", "type": "object", "properties": {"description": {"type": "string"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/OpsStrategyItemVo"}}, "name": {"type": "string"}}}, "DepartmentDto": {"title": "DepartmentDto", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "hidden": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "invitingAuditEnabled": {"type": "boolean"}, "invitingEnabled": {"type": "boolean"}, "name": {"type": "string"}, "parentId": {"type": "integer", "format": "int64"}, "sortNumber": {"type": "integer", "format": "int32"}, "teamId": {"type": "integer", "format": "int64"}}}, "DepartmentMemberMoveParamVo": {"title": "DepartmentMemberMoveParamVo", "type": "object", "properties": {"departmentId": {"type": "integer", "format": "int64"}, "userIdList": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "DepartmentMemberUpdateParamVo": {"title": "DepartmentMemberUpdateParamVo", "type": "object", "properties": {"departmentIdList": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "nickname": {"type": "string"}, "roleCode": {"type": "string", "enum": ["boss", "manager", "staff", "superadmin"]}}}, "DepartmentParamVo": {"title": "DepartmentParamVo", "type": "object", "properties": {"name": {"type": "string"}, "parentId": {"type": "integer", "format": "int64"}}}, "DepartmentTreeNode": {"title": "DepartmentTreeNode", "type": "object", "properties": {"childList": {"type": "array", "description": "下级部门", "items": {}}, "id": {"type": "integer", "description": "部门ID", "format": "int64"}, "memberCount": {"type": "integer", "description": "成员数", "format": "int32"}, "members": {"type": "array", "description": "部门成员", "items": {"$ref": "#/components/schemas/MemberVo"}}, "name": {"type": "string", "description": "部门名称"}, "parentId": {"type": "integer", "description": "上级部门ID", "format": "int64"}, "sortNumber": {"type": "integer", "description": "排序数", "format": "int32"}}}, "DevicePlatformVo": {"title": "DevicePlatformVo", "type": "object", "properties": {"enabled": {"type": "boolean", "description": "是否运行这个平台访问", "example": false}, "minVersion": {"type": "string", "description": "不为空时，表示允许的最低版本"}, "platform": {"type": "string", "description": "平台", "enum": ["android", "linux", "macos", "unknown", "web", "windows", "windows7"]}}}, "FavoriteDetailVo": {"title": "FavoriteDetailVo", "type": "object", "properties": {"collectTime": {"type": "string", "format": "date-time"}, "collector": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "resourceId": {"type": "integer", "format": "int64"}, "resourceType": {"type": "string", "enum": ["AK", "Activity", "Audit", "BlockElements", "Cloud", "CrsOrder", "CrsProduct", "DiskFile", "FingerPrint", "FingerPrintTemplate", "Gateway", "GhCreator", "GhGifter", "GhJobPlan", "GhUser", "GhVideoCreator", "GiftCardPack", "InsTeamUser", "InsUser", "Invoice", "Ip", "IpPool", "IppIp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "KakaoFriend", "KolCreator", "MobileAccount", "None", "Orders", "PluginTeamPack", "Record", "RpaFlow", "RpaTask", "RpaTaskItem", "RpaVoucher", "Shop", "ShopSession", "Tag", "TeamDiskRoot", "TeamMobile", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TkCreator", "TkTeamPack", "Tkshop", "<PERSON>ks<PERSON><PERSON>uyer", "TkshopCreator", "TrafficPack", "TunnelVps", "Users", "View", "Voucher", "XhsAccount"]}, "size": {"type": "integer", "format": "int64"}, "target": {"type": "object"}, "teamId": {"type": "integer", "format": "int64"}, "url": {"type": "string"}}}, "FavoriteDto": {"title": "FavoriteDto", "type": "object", "properties": {"collectTime": {"type": "string", "format": "date-time"}, "collector": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "resourceId": {"type": "integer", "format": "int64"}, "resourceType": {"type": "string", "enum": ["AK", "Activity", "Audit", "BlockElements", "Cloud", "CrsOrder", "CrsProduct", "DiskFile", "FingerPrint", "FingerPrintTemplate", "Gateway", "GhCreator", "GhGifter", "GhJobPlan", "GhUser", "GhVideoCreator", "GiftCardPack", "InsTeamUser", "InsUser", "Invoice", "Ip", "IpPool", "IppIp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "KakaoFriend", "KolCreator", "MobileAccount", "None", "Orders", "PluginTeamPack", "Record", "RpaFlow", "RpaTask", "RpaTaskItem", "RpaVoucher", "Shop", "ShopSession", "Tag", "TeamDiskRoot", "TeamMobile", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TkCreator", "TkTeamPack", "Tkshop", "<PERSON>ks<PERSON><PERSON>uyer", "TkshopCreator", "TrafficPack", "TunnelVps", "Users", "View", "Voucher", "XhsAccount"]}, "size": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}, "url": {"type": "string"}}}, "FavoriteItem": {"title": "FavoriteItem", "type": "object", "properties": {"resourceId": {"type": "integer", "format": "int64"}, "resourceName": {"type": "string"}, "size": {"type": "integer", "format": "int64"}}}, "FunctionConfig": {"title": "FunctionConfig", "type": "object", "properties": {"granted": {"type": "array", "items": {"type": "string"}}, "revoked": {"type": "array", "items": {"type": "string"}}}}, "FunctionMetaVo": {"title": "FunctionMetaVo", "type": "object", "properties": {"function": {"description": "权限信息", "$ref": "#/components/schemas/FunctionVo"}, "roleConfigList": {"type": "array", "description": "每个角色的配置信息", "items": {"$ref": "#/components/schemas/FunctionRoleConfig"}}}}, "FunctionRoleConfig": {"title": "FunctionRoleConfig", "type": "object", "properties": {"customGrantEnabled": {"type": "boolean", "description": "是否允许自定义", "example": false}, "customGranted": {"type": "boolean", "description": "当前是否授权", "example": false}, "defaultGranted": {"type": "boolean", "description": "出厂默认值", "example": false}, "role": {"type": "string", "description": "角色信息", "enum": ["boss", "manager", "staff", "superadmin"]}}}, "FunctionVo": {"title": "FunctionVo", "type": "object", "properties": {"description": {"type": "string", "description": "描述"}, "descriptionEn": {"type": "string"}, "id": {"type": "string", "description": "ID", "enum": ["DISK_SPACE_MANAGER", "DISK_TEAM_MANAGER", "DISK_USER_MANAGER", "EXPENSE_MANAGE", "EXPENSE_VIEW", "EXTENSION_LIST", "EXTENSION_MANAGER", "FINGERPRINT_CONFIG", "FINGERPRINT_LIST", "FINGERPRINT_MANAGER", "IP_CONFIG", "IP_LIST", "IP_MANAGE", "KOL_LIST", "KOL_MANAGE", "MOBILE_CONFIG", "MOBILE_IMPORT_DELETE", "OPERATE_LOG_GET_IP", "OPERATE_LOG_GET_LOGIN", "OPERATE_LOG_GET_SHOP", "OPERATE_LOG_GET_TEAM_MANAGE", "OPERATE_LOG_MANAGE_SHOP", "RPA_CARD_MANAGER", "RPA_CREATE_DELETE", "RPA_LIST", "RPA_OPEN_API", "RPA_PLAN", "RPA_RUN", "SHOP_AUTHORIZE", "SHOP_BIND_IP_MANAGE", "SHOP_CONFIG", "SHOP_FINGERPRINT_MANAGE", "SHOP_IMPORT_DELETE", "TEAM_AUDIT", "TEAM_CRITICAL_MANAGE", "TEAM_MANAGE", "TEAM_RESOURCE_MANAGE", "TEAM_VIEW_MEMBER", "TKSHOP_BUYER_MANAGER", "TKSHOP_CREATOR_MANAGER", "TKSHOP_GLOBAL_CREATOR_MANAGER", "TKSHOP_MANAGE", "TK_PACK_MANAGE", "TK_流程计划编排", "TK_达人管理", "TK_达人运营", "TK_达人邀约", "TK_运营辅助"]}, "module": {"type": "string", "description": "模块名称"}, "name": {"type": "string", "description": "权限项名称"}, "nameEn": {"type": "string"}, "sortNumber": {"type": "integer", "description": "排序号", "format": "int32"}}}, "GbsConfig": {"title": "GbsConfig", "type": "object", "properties": {"password": {"type": "string"}, "tenantId": {"type": "string"}, "userId": {"type": "string"}}}, "GiftCardPackDetailVo": {"title": "GiftCardPackDetailVo", "type": "object", "properties": {"cardCount": {"type": "integer", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}, "giftCardType": {"type": "string", "enum": ["Credit", "RpaVoucher"]}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "packItems": {"type": "array", "items": {"$ref": "#/components/schemas/GiftCardPackItemDetailVo"}}, "partnerId": {"type": "integer", "format": "int64"}, "remainCount": {"type": "integer", "description": "剩余可用礼品卡数量", "format": "int32"}, "remarks": {"type": "string"}, "serialNumber": {"type": "string"}, "valid": {"type": "boolean", "description": "是否在有效期内（根据createTime & validDays计算出来的）", "example": false}, "validDays": {"type": "integer", "format": "int32"}}}, "GiftCardPackItemDetailVo": {"title": "GiftCardPackItemDetailVo", "type": "object", "properties": {"activatedTeamId": {"type": "integer", "format": "int64"}, "activatedTeamName": {"type": "string"}, "activeTime": {"type": "string", "format": "date-time"}, "amount": {"type": "number", "format": "bigdecimal"}, "cardNumber": {"type": "string"}, "cardPackId": {"type": "integer", "format": "int64"}, "cardPassword": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "disabled": {"type": "boolean"}, "duration": {"type": "integer", "description": "当为rpa礼品卡时，表示该卡有几个 periodUnit 月/周", "format": "int32"}, "expireDate": {"type": "string", "format": "date-time"}, "giftCardType": {"type": "string", "enum": ["Credit", "RpaVoucher"]}, "goodsId": {"type": "integer", "format": "int64"}, "goodsType": {"type": "string", "enum": ["Credit", "CreditPack", "ExclusiveIp", "FingerprintQuota", "IosDeveloperApprove", "Ip", "IpGo", "IpProxy", "MarketFlow", "None", "PluginPack", "PriceDifference", "ProxyTraffic", "RpaCaptcha", "RpaExecuteQuota", "RpaMobile", "RpaOpenAi", "RpaSendEmail", "RpaSendSms", "RpaSendWeChat", "Rpa_Voucher_Base", "Rpa_Voucher_Performance", "SharingIp", "ShopQuota", "ShopSecurityPolicy", "StorageQuota", "TeamMemberQuota", "Team<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TkPack", "TkPackTrail", "Tkshop", "TkshopEnterprise", "TkshopStandard", "Traffic", "TransitTraffic", "TransitTrafficV2", "UserExclusiveIp", "Voucher"]}, "id": {"type": "integer", "format": "int64"}, "orderItemId": {"type": "integer", "format": "int64"}, "periodUnit": {"type": "string", "enum": ["Buyout", "Byte", "GB", "GB天", "个", "个天", "分钟", "周", "天", "年", "张", "无", "月", "次"]}, "remarks": {"type": "string"}, "valid": {"type": "boolean", "description": "是否在有效期内（根据createTime & validDays计算出来的）", "example": false}}}, "GrantDepartmentResourcesRequest": {"title": "GrantDepartmentResourcesRequest", "type": "object", "properties": {"resourceIds": {"type": "array", "description": "资源ID，不设置是清空授权", "items": {"type": "integer", "format": "int64"}}, "resourceType": {"type": "string", "description": "资源类型，必填", "enum": ["AK", "Activity", "Audit", "BlockElements", "Cloud", "CrsOrder", "CrsProduct", "DiskFile", "FingerPrint", "FingerPrintTemplate", "Gateway", "GhCreator", "GhGifter", "GhJobPlan", "GhUser", "GhVideoCreator", "GiftCardPack", "InsTeamUser", "InsUser", "Invoice", "Ip", "IpPool", "IppIp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "KakaoFriend", "KolCreator", "MobileAccount", "None", "Orders", "PluginTeamPack", "Record", "RpaFlow", "RpaTask", "RpaTaskItem", "RpaVoucher", "Shop", "ShopSession", "Tag", "TeamDiskRoot", "TeamMobile", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TkCreator", "TkTeamPack", "Tkshop", "<PERSON>ks<PERSON><PERSON>uyer", "TkshopCreator", "TrafficPack", "TunnelVps", "Users", "View", "Voucher", "XhsAccount"]}}}, "GrantInfo": {"title": "GrantInfo", "type": "object", "properties": {"departments": {"type": "array", "description": "授权的部门", "items": {"$ref": "#/components/schemas/DepartmentDto"}}, "users": {"type": "array", "description": "授权给的用户", "items": {"$ref": "#/components/schemas/MemberVo"}}}}, "GrantOpsStrategyItem": {"title": "GrantOpsStrategyItem", "type": "object", "properties": {"mappingType": {"type": "string", "enum": ["department", "role", "user"]}, "targetId": {"type": "integer", "format": "int64"}}}, "GrantOpsStrategyRequest": {"title": "GrantOpsStrategyRequest", "type": "object", "properties": {"cleanFirst": {"type": "boolean"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/GrantOpsStrategyItem"}}}}, "GrantShopsParamVo": {"title": "GrantShopsParamVo", "type": "object", "properties": {"shopIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "teamId": {"type": "integer", "format": "int64"}, "userId": {"type": "integer", "format": "int64"}}}, "HyRuntimeDto": {"title": "HyRuntimeDto", "type": "object", "properties": {"cpu": {"type": "integer", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}, "deviceId": {"type": "string"}, "hostname": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "identifier": {"type": "string"}, "lastActiveTime": {"type": "string", "format": "date-time"}, "lastRemoteIp": {"type": "string"}, "mem": {"type": "integer", "format": "int64"}, "osName": {"type": "string"}, "remark": {"type": "string"}, "remoteIp": {"type": "string"}, "secretKey": {"type": "string"}, "status": {"type": "string", "enum": ["DELETED", "READY", "STOPPED"]}, "version": {"type": "string"}}}, "InviteInfoVo": {"title": "InviteInfoVo", "type": "object", "properties": {"alreadyInTeam": {"type": "boolean", "description": "是否已经在团队中", "example": false}, "auditStatus": {"type": "string", "description": "审核状态", "enum": ["APPROVED", "CANCEL", "NEW", "NOT_PASS"]}, "creator": {"description": "团队创建者信息", "$ref": "#/components/schemas/UserVo"}, "departmentName": {"type": "string", "description": "部门名称"}, "teamId": {"type": "integer", "description": "团队ID", "format": "int64"}, "teamName": {"type": "string", "description": "团队名称"}}}, "InviteJoinTeamCheckResult": {"title": "InviteJoinTeamCheckResult", "type": "object", "properties": {"link": {"$ref": "#/components/schemas/BizLinkDto"}, "status": {"type": "string", "enum": ["Account<PERSON><PERSON><PERSON>", "AccountNotExists", "Account<PERSON>ot<PERSON><PERSON><PERSON>", "AccountNotMatch"]}}}, "InviteLinkVo": {"title": "InviteLinkVo", "type": "object", "properties": {"createTime": {"type": "string", "description": "创建时间", "format": "date-time"}, "expireTime": {"type": "string", "description": "过期时间", "format": "date-time"}, "id": {"type": "integer", "description": "id", "format": "int64"}, "linkStatus": {"type": "string", "description": "链接状态", "format": "byte"}, "token": {"type": "string", "description": "token"}}}, "IpDataDto": {"title": "IpDataDto", "type": "object", "properties": {"city": {"type": "string"}, "country": {"type": "string"}, "countryCode": {"type": "string"}, "district": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "ip": {"type": "string"}, "isp": {"type": "string"}, "locationId": {"type": "integer", "format": "int64"}, "province": {"type": "string"}, "revisedLocationId": {"type": "integer", "format": "int64"}, "revisedProvider": {"type": "string"}, "tag": {"type": "string"}, "updateTime": {"type": "string", "format": "date-time"}, "zip": {"type": "string"}, "zone": {"type": "string"}}}, "JoinTeamParamVo": {"title": "JoinTeamParamVo", "type": "object", "properties": {"account": {"type": "string"}, "areaCode": {"type": "string"}, "inviteCode": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}, "verifyCode": {"type": "string"}}}, "JoinTeamResultVo": {"title": "JoinTeamResultVo", "type": "object", "properties": {"joinTeamStatus": {"type": "string", "enum": ["ALREADY_IN_AUDIT", "ALREADY_IN_TEAM", "JOIN_SUCCESS", "WAIT_AUDIT"]}, "jwt": {"type": "string"}, "jwtExpireDate": {"type": "string", "format": "date-time"}}}, "JwtResultVo": {"title": "JwtResultVo", "type": "object", "properties": {"jwt": {"type": "string"}, "jwtExpireTime": {"type": "string", "format": "date-time"}, "jwtId": {"type": "string"}}}, "LadderPriceRange": {"title": "LadderPriceRange", "type": "object", "properties": {"amount": {"type": "integer", "description": "阶梯折扣百分比（=原价*amount/100）", "format": "int32"}, "threshold": {"type": "integer", "description": "超过特定数量", "format": "int32"}}}, "LoginDeviceDto": {"title": "LoginDeviceDto", "type": "object", "properties": {"appId": {"type": "string"}, "appVersion": {"type": "string"}, "clientIp": {"type": "string"}, "clientLocation": {"type": "integer", "format": "int64"}, "cpus": {"type": "integer", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}, "deviceId": {"type": "string"}, "deviceType": {"type": "string", "enum": ["App", "Browser", "HYRuntime", "RpaExecutor"]}, "domestic": {"type": "boolean"}, "hostName": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "ipDataId": {"type": "integer", "format": "int64"}, "lastActiveTime": {"type": "string", "format": "date-time"}, "lastCity": {"type": "string"}, "lastLogTime": {"type": "string", "format": "date-time"}, "lastRemoteIp": {"type": "string"}, "lastUserId": {"type": "integer", "format": "int64"}, "logUrl": {"type": "string"}, "mem": {"type": "integer", "format": "int64"}, "online": {"type": "boolean"}, "osName": {"type": "string"}, "userAgent": {"type": "string"}, "version": {"type": "string"}}}, "LoginDeviceLocationVo": {"title": "LoginDeviceLocationVo", "type": "object", "properties": {"appId": {"type": "string"}, "appVersion": {"type": "string"}, "clientIp": {"type": "string"}, "clientLocation": {"type": "integer", "format": "int64"}, "cpus": {"type": "integer", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}, "deviceId": {"type": "string"}, "deviceType": {"type": "string", "enum": ["App", "Browser", "HYRuntime", "RpaExecutor"]}, "domestic": {"type": "boolean"}, "hostName": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "ipData": {"$ref": "#/components/schemas/IpDataDto"}, "ipDataId": {"type": "integer", "format": "int64"}, "lastActiveTime": {"type": "string", "format": "date-time"}, "lastCity": {"type": "string"}, "lastLogTime": {"type": "string", "format": "date-time"}, "lastRemoteIp": {"type": "string"}, "lastUserId": {"type": "integer", "format": "int64"}, "logUrl": {"type": "string"}, "mem": {"type": "integer", "format": "int64"}, "online": {"type": "boolean"}, "osName": {"type": "string"}, "userAgent": {"type": "string"}, "version": {"type": "string"}}}, "LoginDeviceSessionVo": {"title": "LoginDeviceSessionVo", "type": "object", "properties": {"current": {"type": "boolean"}, "deviceId": {"type": "string"}, "remoteIp": {"type": "string"}, "sessionId": {"type": "string"}}}, "LoginDeviceTransitDto": {"title": "LoginDeviceTransitDto", "type": "object", "properties": {"deviceId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "lastProbeTime": {"type": "string", "format": "date-time"}, "probeCode": {"type": "integer", "format": "int32"}, "probeError": {"type": "string"}, "probeOut": {"type": "string"}, "remoteIp": {"type": "string"}, "status": {"type": "string", "enum": ["Available", "Pending", "Unavailable"]}, "testingTime": {"type": "integer", "format": "int32"}, "transitId": {"type": "integer", "format": "int64"}}}, "LoginDeviceTransitItem": {"title": "LoginDeviceTransitItem", "type": "object", "properties": {"probeCode": {"type": "integer", "description": "探测结果代码", "format": "int32"}, "probeError": {"type": "string", "description": "探测时的错误信息"}, "probeOut": {"type": "string", "description": "输出信息"}, "remoteIp": {"type": "string", "description": "探测到的remoteIp"}, "status": {"type": "string", "description": "状态", "enum": ["Available", "Pending", "Unavailable"]}, "testingTime": {"type": "integer", "description": "探测总耗时", "format": "int32"}, "transitId": {"type": "integer", "description": "接入点ID", "format": "int64"}}}, "LoginDeviceTransitRequest": {"title": "LoginDeviceTransitRequest", "type": "object", "properties": {"transitItems": {"type": "array", "items": {"$ref": "#/components/schemas/LoginDeviceTransitItem"}}}}, "LoginResultVo": {"title": "LoginResultVo", "type": "object", "properties": {"account": {"type": "string"}, "jwt": {"type": "string"}, "jwtExpireTime": {"type": "string", "format": "date-time"}, "jwtId": {"type": "string"}, "needCaptcha": {"type": "boolean", "description": "是否需要验证码", "example": false}, "nickname": {"type": "string"}, "userId": {"type": "integer", "format": "int64"}, "userType": {"type": "string", "enum": ["NORMAL", "PARTNER", "SHADOW"]}}}, "MemberVo": {"title": "MemberVo", "type": "object", "properties": {"account": {"type": "string", "description": "账号"}, "avatar": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "email": {"type": "string", "description": "邮箱"}, "gender": {"type": "string", "enum": ["FEMALE", "MALE", "UNSPECIFIC"]}, "id": {"type": "integer", "format": "int64"}, "nickname": {"type": "string"}, "partnerId": {"type": "integer", "format": "int64"}, "phone": {"type": "string", "description": "手机"}, "residentCity": {"type": "string"}, "roleCode": {"type": "string", "enum": ["boss", "manager", "staff", "superadmin"]}, "signature": {"type": "string"}, "status": {"type": "string", "enum": ["ACTIVE", "BLOCK", "DELETED", "INACTIVE"]}, "teamNickname": {"type": "string"}, "tenant": {"type": "integer", "format": "int64"}, "userType": {"type": "string", "enum": ["NORMAL", "PARTNER", "SHADOW"]}, "weixin": {"type": "string"}}}, "Mobile3ElementResult": {"title": "Mobile3ElementResult", "type": "object", "properties": {"charged": {"type": "boolean"}, "matched": {"type": "boolean"}, "message": {"type": "string"}, "rawResponse": {"type": "object"}, "success": {"type": "boolean"}}}, "Mobile3ElementVo": {"title": "Mobile3ElementVo", "type": "object", "properties": {"areaCode": {"type": "string"}, "certName": {"type": "string"}, "certNo": {"type": "string"}, "mobile": {"type": "string"}, "verifyCode": {"type": "string", "description": "手机验证码"}}}, "OAuthProcessInfo": {"title": "OAuthProcessInfo", "type": "object", "properties": {"action": {"type": "string", "enum": ["bind", "login"]}, "done": {"type": "boolean"}, "error": {"type": "string"}, "jwt": {"type": "string"}, "oauthType": {"type": "string", "enum": ["ALIYUN", "DINGTALK", "FACEBOOK", "GOOGLE", "KAKAO", "QQ", "WEIBO", "WEIXIN", "WORK_WEIXIN", "WX_MINPROGRAM"]}, "sessionId": {"type": "string"}, "success": {"type": "boolean"}, "url": {"type": "string"}, "userId": {"type": "integer", "format": "int64"}, "uuid": {"type": "string"}}}, "OpStatVo": {"title": "OpStatVo", "type": "object", "properties": {"accountCount": {"type": "integer", "description": "分身数量", "format": "int32"}, "userCount": {"type": "integer", "description": "用户数", "format": "int32"}}}, "OpenAccountDto": {"title": "OpenAccountDto", "type": "object", "properties": {"appId": {"type": "string"}, "avatar": {"type": "string"}, "city": {"type": "string"}, "corpId": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "email": {"type": "string"}, "extra": {"type": "string"}, "gender": {"type": "string", "enum": ["FEMALE", "MALE", "UNSPECIFIC"]}, "home": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "locale": {"type": "string"}, "location": {"type": "string"}, "nickname": {"type": "string"}, "oauthType": {"type": "string", "enum": ["ALIYUN", "DINGTALK", "FACEBOOK", "GOOGLE", "KAKAO", "QQ", "WEIBO", "WEIXIN", "WORK_WEIXIN", "WX_MINPROGRAM"]}, "openId": {"type": "string"}, "phone": {"type": "string"}, "province": {"type": "string"}, "subscribe": {"type": "integer", "format": "int32"}, "thirdAppUserId": {"type": "string"}, "unionId": {"type": "string"}, "userId": {"type": "integer", "format": "int64"}, "valid": {"type": "boolean"}}}, "OpsStrategyGroupDto": {"title": "OpsStrategyGroupDto", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "creator": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}}}, "OpsStrategyGroupVo": {"title": "OpsStrategyGroupVo", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "creator": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "grantedUserIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "id": {"type": "integer", "format": "int64"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/OpsStrategyItemVo"}}, "name": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}}}, "OpsStrategyItemVo": {"title": "OpsStrategyItemVo", "type": "object", "properties": {"config": {"type": "object"}, "opsStrategy": {"type": "string", "enum": ["device_auth_type", "login_time"]}}}, "PageResult«AuditDetailVo»": {"title": "PageResult«AuditDetailVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/AuditDetailVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«FavoriteDetailVo»": {"title": "PageResult«FavoriteDetailVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/FavoriteDetailVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«FavoriteDto»": {"title": "PageResult«FavoriteDto»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/FavoriteDto"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«OpsStrategyGroupVo»": {"title": "PageResult«OpsStrategyGroupVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/OpsStrategyGroupVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«TeamFavoriteDetailVo»": {"title": "PageResult«TeamFavoriteDetailVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/TeamFavoriteDetailVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«TeamFavoriteDto»": {"title": "PageResult«TeamFavoriteDto»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/TeamFavoriteDto"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«UserActivityDeviceVo»": {"title": "PageResult«UserActivityDeviceVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/UserActivityDeviceVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«UserDetailVo»": {"title": "PageResult«UserDetailVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/UserDetailVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PageResult«UserPromotedRecordVo»": {"title": "PageResult«UserPromotedRecordVo»", "type": "object", "properties": {"current": {"type": "integer", "format": "int32"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/UserPromotedRecordVo"}}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int64"}}}, "PartnerDto": {"title": "PartnerDto", "type": "object", "properties": {"bankAccount": {"type": "string"}, "bankName": {"type": "string"}, "bankNo": {"type": "string"}, "contactName": {"type": "string"}, "contactPhone": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "fullName": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "managerId": {"type": "integer", "format": "int64"}, "oemSupport": {"type": "boolean"}, "openapiSupport": {"type": "boolean"}, "organizedTeamAccountQuota": {"type": "integer", "format": "int32"}, "organizedTeamUserQuota": {"type": "integer", "format": "int32"}, "password": {"type": "string"}, "role": {"type": "string", "enum": ["Broker", "Organizer"]}, "shortName": {"type": "string"}, "status": {"type": "string", "enum": ["ACTIVE", "BLOCK", "DELETED", "INACTIVE"]}, "teamId": {"type": "integer", "format": "int64"}, "userId": {"type": "integer", "format": "int64"}}}, "PasswordPolicyVo": {"title": "PasswordPolicyVo", "type": "object", "properties": {"autoReset": {"type": "boolean", "description": "是否自动更新密码并通知（设置有效期时）", "example": false}, "containsAny": {"type": "boolean", "description": "包含任意字符", "example": false}, "maxSize": {"type": "integer", "description": "最大长度", "format": "int32"}, "minSize": {"type": "integer", "description": "最小长度", "format": "int32"}, "mustContainsLetter": {"type": "boolean", "description": "必须包含字母", "example": false}, "mustContainsNumber": {"type": "boolean", "description": "必须包含数字", "example": false}, "mustContainsSpecial": {"type": "boolean", "description": "必须包含特殊字符", "example": false}, "nonRepeatCount": {"type": "integer", "description": "不能与前若干次重复", "format": "int32"}, "validSeconds": {"type": "integer", "description": "密码有效期(-1为永久有效）", "format": "int32"}}}, "PreLoginVo": {"title": "PreLoginVo", "type": "object", "properties": {"needCaptcha": {"type": "boolean", "description": "是否需要验证码", "example": false}}}, "ProductConfigVo": {"title": "ProductConfigVo", "type": "object", "properties": {"publicKeyPEM": {"type": "string", "description": "公钥test"}}}, "RegisterAccountVo": {"title": "RegisterAccountVo", "type": "object", "properties": {"loginResultVo": {"$ref": "#/components/schemas/LoginResultVo"}, "updateToken": {"type": "string"}, "user": {"$ref": "#/components/schemas/UserVo"}}}, "RegisterConfig": {"title": "RegisterConfig", "type": "object", "properties": {"needInvite": {"type": "boolean"}}}, "RemoteLocationVo": {"title": "RemoteLocationVo", "type": "object", "properties": {"city": {"type": "string"}, "location": {"type": "string"}}}, "ResourceGrantRequest": {"title": "ResourceGrantRequest", "type": "object", "properties": {"cleanFirst": {"type": "boolean"}, "departmentIdList": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "resourceIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "resourceType": {"type": "string", "enum": ["AK", "Activity", "Audit", "BlockElements", "Cloud", "CrsOrder", "CrsProduct", "DiskFile", "FingerPrint", "FingerPrintTemplate", "Gateway", "GhCreator", "GhGifter", "GhJobPlan", "GhUser", "GhVideoCreator", "GiftCardPack", "InsTeamUser", "InsUser", "Invoice", "Ip", "IpPool", "IppIp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "KakaoFriend", "KolCreator", "MobileAccount", "None", "Orders", "PluginTeamPack", "Record", "RpaFlow", "RpaTask", "RpaTaskItem", "RpaVoucher", "Shop", "ShopSession", "Tag", "TeamDiskRoot", "TeamMobile", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TkCreator", "TkTeamPack", "Tkshop", "<PERSON>ks<PERSON><PERSON>uyer", "TkshopCreator", "TrafficPack", "TunnelVps", "Users", "View", "Voucher", "XhsAccount"]}, "teamId": {"type": "integer", "format": "int64"}, "userIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "RoleVo": {"title": "RoleVo", "type": "object", "properties": {"code": {"type": "string", "enum": ["boss", "manager", "staff", "superadmin"]}, "description": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}, "type": {"type": "string", "enum": ["build_in", "custom"]}}}, "SetRoleFunctionRequest": {"title": "SetRoleFunctionRequest", "type": "object", "properties": {"codes": {"type": "array", "items": {"type": "string", "enum": ["boss", "manager", "staff", "superadmin"]}}, "grantedFunctions": {"type": "array", "items": {"type": "string", "enum": ["DISK_SPACE_MANAGER", "DISK_TEAM_MANAGER", "DISK_USER_MANAGER", "EXPENSE_MANAGE", "EXPENSE_VIEW", "EXTENSION_LIST", "EXTENSION_MANAGER", "FINGERPRINT_CONFIG", "FINGERPRINT_LIST", "FINGERPRINT_MANAGER", "IP_CONFIG", "IP_LIST", "IP_MANAGE", "KOL_LIST", "KOL_MANAGE", "MOBILE_CONFIG", "MOBILE_IMPORT_DELETE", "OPERATE_LOG_GET_IP", "OPERATE_LOG_GET_LOGIN", "OPERATE_LOG_GET_SHOP", "OPERATE_LOG_GET_TEAM_MANAGE", "OPERATE_LOG_MANAGE_SHOP", "RPA_CARD_MANAGER", "RPA_CREATE_DELETE", "RPA_LIST", "RPA_OPEN_API", "RPA_PLAN", "RPA_RUN", "SHOP_AUTHORIZE", "SHOP_BIND_IP_MANAGE", "SHOP_CONFIG", "SHOP_FINGERPRINT_MANAGE", "SHOP_IMPORT_DELETE", "TEAM_AUDIT", "TEAM_CRITICAL_MANAGE", "TEAM_MANAGE", "TEAM_RESOURCE_MANAGE", "TEAM_VIEW_MEMBER", "TKSHOP_BUYER_MANAGER", "TKSHOP_CREATOR_MANAGER", "TKSHOP_GLOBAL_CREATOR_MANAGER", "TKSHOP_MANAGE", "TK_PACK_MANAGE", "TK_流程计划编排", "TK_达人管理", "TK_达人运营", "TK_达人邀约", "TK_运营辅助"]}}, "revokedFunctions": {"type": "array", "items": {"type": "string", "enum": ["DISK_SPACE_MANAGER", "DISK_TEAM_MANAGER", "DISK_USER_MANAGER", "EXPENSE_MANAGE", "EXPENSE_VIEW", "EXTENSION_LIST", "EXTENSION_MANAGER", "FINGERPRINT_CONFIG", "FINGERPRINT_LIST", "FINGERPRINT_MANAGER", "IP_CONFIG", "IP_LIST", "IP_MANAGE", "KOL_LIST", "KOL_MANAGE", "MOBILE_CONFIG", "MOBILE_IMPORT_DELETE", "OPERATE_LOG_GET_IP", "OPERATE_LOG_GET_LOGIN", "OPERATE_LOG_GET_SHOP", "OPERATE_LOG_GET_TEAM_MANAGE", "OPERATE_LOG_MANAGE_SHOP", "RPA_CARD_MANAGER", "RPA_CREATE_DELETE", "RPA_LIST", "RPA_OPEN_API", "RPA_PLAN", "RPA_RUN", "SHOP_AUTHORIZE", "SHOP_BIND_IP_MANAGE", "SHOP_CONFIG", "SHOP_FINGERPRINT_MANAGE", "SHOP_IMPORT_DELETE", "TEAM_AUDIT", "TEAM_CRITICAL_MANAGE", "TEAM_MANAGE", "TEAM_RESOURCE_MANAGE", "TEAM_VIEW_MEMBER", "TKSHOP_BUYER_MANAGER", "TKSHOP_CREATOR_MANAGER", "TKSHOP_GLOBAL_CREATOR_MANAGER", "TKSHOP_MANAGE", "TK_PACK_MANAGE", "TK_流程计划编排", "TK_达人管理", "TK_达人运营", "TK_达人邀约", "TK_运营辅助"]}}}}, "ShopDto": {"title": "ShopDto", "type": "object", "properties": {"account": {"type": "string", "description": "账户账号"}, "allowExtension": {"type": "boolean", "description": "是否允许用户自行安装插件", "example": false}, "allowMonitor": {"type": "boolean", "description": "会话是否允许监视", "example": false}, "allowSkip": {"type": "boolean", "description": "是否允许跳过敏感操作", "example": false}, "autoFill": {"type": "boolean", "description": "是否自动代填", "example": false}, "bookmarkBar": {"type": "string"}, "coordinateId": {"type": "string"}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time"}, "creatorId": {"type": "integer", "description": "创建者", "format": "int64"}, "deleteTime": {"type": "string", "format": "date-time"}, "deleting": {"type": "boolean"}, "description": {"type": "string", "description": "描述"}, "domainPolicy": {"type": "string", "enum": ["Blacklist", "None", "Whitelist"]}, "downTraffic": {"type": "integer", "description": "下行流量", "format": "int64"}, "exclusive": {"type": "boolean", "description": "是否独占访问", "example": false}, "extraProp": {"type": "string"}, "fingerprintId": {"type": "integer", "description": "绑定的指纹Id", "format": "int64"}, "fingerprintTemplateId": {"type": "integer", "description": "绑定的指纹模板Id（只有无状态账号才允许绑定指纹模板）", "format": "int64"}, "frontUrl": {"type": "string"}, "googleTranslateSpeed": {"type": "boolean"}, "homePage": {"type": "string"}, "homePageSites": {"type": "string"}, "id": {"type": "integer", "description": "id", "format": "int64"}, "imageForbiddenSize": {"type": "integer", "format": "int64"}, "intranetEnabled": {"type": "boolean"}, "ipSwitchCheckInterval": {"type": "integer", "format": "int32"}, "ipSwitchStrategy": {"type": "string", "enum": ["Abort", "<PERSON><PERSON>", "Off"]}, "lastAccessTime": {"type": "string", "format": "date-time"}, "lastAccessUser": {"type": "integer", "format": "int64"}, "lastSyncTime": {"type": "string", "format": "date-time"}, "loginStatus": {"type": "string", "enum": ["Offline", "Online", "Unknown"]}, "markCode": {"type": "string", "description": "任务栏分身标记文字"}, "markCodeBg": {"type": "integer", "description": "任务栏分身标记背景颜色", "format": "int32"}, "monitorPerception": {"type": "boolean"}, "name": {"type": "string", "description": "账户名称"}, "nameBookmarkEnabled": {"type": "boolean"}, "operateStatus": {"type": "string", "enum": ["shared", "sharing", "sole", "transferring"]}, "operatingCategory": {"type": "string", "description": "经营品类", "enum": ["医药保健", "图书文具", "宠物用品", "家具建材", "家电电器", "工业用品", "户外运动", "手机数码", "手表眼镜", "护肤美妆", "母婴玩具", "汽车配件", "生活家居", "电商其他", "电脑平板", "艺术珠宝", "花园聚会", "计生情趣", "软件程序", "鞋服箱包", "音乐影视", "食品生鲜", "鲜花绿植"]}, "parentShopId": {"type": "integer", "format": "int64"}, "password": {"type": "string", "description": "密码"}, "platformId": {"type": "integer", "description": "平台ID", "format": "int64"}, "privateAddress": {"type": "string"}, "privateTitle": {"type": "string"}, "recordPerception": {"type": "boolean"}, "recordPolicy": {"type": "string", "enum": ["<PERSON><PERSON>", "Disabled", "Forced"]}, "requireIp": {"type": "boolean"}, "resourcePolicy": {"type": "integer", "format": "int32"}, "securityPolicyEnabled": {"type": "boolean"}, "securityPolicyUpdateTime": {"type": "string", "format": "date-time"}, "sharePolicyId": {"type": "string"}, "shopDataSize": {"type": "integer", "format": "int64"}, "stateless": {"type": "boolean"}, "statelessChangeFp": {"type": "boolean"}, "statelessSyncPolicy": {"type": "integer", "format": "int32"}, "syncPolicy": {"type": "integer", "format": "int32"}, "teamId": {"type": "integer", "description": "团队ID", "format": "int64"}, "trafficAlertStrategy": {"type": "string", "enum": ["Abort", "Off"]}, "trafficAlertThreshold": {"type": "integer", "format": "int32"}, "trafficSaving": {"type": "boolean"}, "type": {"type": "string", "enum": ["Global", "Local", "None"]}, "upTraffic": {"type": "integer", "description": "上行流量", "format": "int64"}, "webSecurity": {"type": "boolean"}}}, "SortedDepartmentVo": {"title": "SortedDepartmentVo", "type": "object", "properties": {"departmentIds": {"type": "array", "description": "当前同级部门ID（按顺序）", "items": {"type": "integer", "format": "int64"}}, "parentId": {"type": "integer", "description": "上级部门ID", "format": "int64"}}}, "TeamConfigDto": {"title": "TeamConfigDto", "type": "object", "properties": {"alreadyInTeam": {"type": "boolean"}, "deleteShopFp": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "inviteAuditEnable": {"type": "boolean"}, "inviteCode": {"type": "string"}, "inviteEnable": {"type": "boolean"}, "recordKeepDays": {"type": "integer", "format": "int32"}, "rpaKeepDays": {"type": "integer", "format": "int32"}, "storageAlertCredit": {"type": "integer", "format": "int32"}, "teamId": {"type": "integer", "format": "int64"}}}, "TeamDetailVo": {"title": "TeamDetailVo", "type": "object", "properties": {"avatar": {"type": "string"}, "consoleDesc": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "creatorName": {"type": "string"}, "deleteTime": {"type": "string", "format": "date-time"}, "domesticCloudEnabled": {"type": "boolean"}, "fingerprintCount": {"type": "integer", "format": "int32"}, "id": {"type": "integer", "format": "int64"}, "invalidTime": {"type": "string", "format": "date-time"}, "inviteCode": {"type": "integer", "format": "int64"}, "ipCount": {"type": "integer", "format": "int32"}, "memberCount": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "orderCount": {"type": "integer", "description": "订单数量", "format": "int32"}, "overseaCloudEnabled": {"type": "boolean"}, "paid": {"type": "boolean"}, "partner": {"$ref": "#/components/schemas/PartnerDto"}, "partnerDesc": {"type": "string", "description": "伙伴管理者编辑的描述"}, "partnerId": {"type": "integer", "format": "int64"}, "payTime": {"type": "string", "format": "date-time"}, "repurchaseTime": {"type": "string", "format": "date-time"}, "repurchased": {"type": "boolean"}, "rpaTaskCount": {"type": "integer", "format": "int32"}, "shopCount": {"type": "integer", "format": "int32"}, "status": {"type": "string", "enum": ["Blocked", "Deleted", "Pending", "Ready"]}, "teamBalance": {"type": "number", "format": "bigdecimal"}, "teamCredit": {"type": "number", "description": "剩余花瓣", "format": "bigdecimal"}, "teamType": {"type": "string", "enum": ["crs", "gh", "krShop", "normal", "partner", "plugin", "tk", "tkshop"]}, "tenantId": {"type": "integer", "format": "int64"}, "testing": {"type": "boolean"}, "tkTeamRelation": {"$ref": "#/components/schemas/TkTeamRelationDto"}, "validateTime": {"type": "string", "format": "date-time"}, "validated": {"type": "boolean"}, "verified": {"type": "boolean"}}}, "TeamDeviceConfig": {"title": "TeamDeviceConfig", "type": "object", "properties": {"versionControls": {"type": "array", "description": "版本限制", "items": {"$ref": "#/components/schemas/DevicePlatformVo"}}}}, "TeamDto": {"title": "TeamDto", "type": "object", "properties": {"avatar": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "deleteTime": {"type": "string", "format": "date-time"}, "domesticCloudEnabled": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "invalidTime": {"type": "string", "format": "date-time"}, "inviteCode": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "overseaCloudEnabled": {"type": "boolean"}, "paid": {"type": "boolean"}, "partnerId": {"type": "integer", "format": "int64"}, "payTime": {"type": "string", "format": "date-time"}, "repurchaseTime": {"type": "string", "format": "date-time"}, "repurchased": {"type": "boolean"}, "status": {"type": "string", "enum": ["Blocked", "Deleted", "Pending", "Ready"]}, "teamType": {"type": "string", "enum": ["crs", "gh", "krShop", "normal", "partner", "plugin", "tk", "tkshop"]}, "tenantId": {"type": "integer", "format": "int64"}, "testing": {"type": "boolean"}, "validateTime": {"type": "string", "format": "date-time"}, "validated": {"type": "boolean"}, "verified": {"type": "boolean"}}}, "TeamFavoriteDetailVo": {"title": "TeamFavoriteDetailVo", "type": "object", "properties": {"collectTime": {"type": "string", "format": "date-time"}, "collector": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "resourceId": {"type": "integer", "format": "int64"}, "resourceType": {"type": "string", "enum": ["AK", "Activity", "Audit", "BlockElements", "Cloud", "CrsOrder", "CrsProduct", "DiskFile", "FingerPrint", "FingerPrintTemplate", "Gateway", "GhCreator", "GhGifter", "GhJobPlan", "GhUser", "GhVideoCreator", "GiftCardPack", "InsTeamUser", "InsUser", "Invoice", "Ip", "IpPool", "IppIp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "KakaoFriend", "KolCreator", "MobileAccount", "None", "Orders", "PluginTeamPack", "Record", "RpaFlow", "RpaTask", "RpaTaskItem", "RpaVoucher", "Shop", "ShopSession", "Tag", "TeamDiskRoot", "TeamMobile", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TkCreator", "TkTeamPack", "Tkshop", "<PERSON>ks<PERSON><PERSON>uyer", "TkshopCreator", "TrafficPack", "TunnelVps", "Users", "View", "Voucher", "XhsAccount"]}, "size": {"type": "integer", "format": "int64"}, "target": {"type": "object"}, "teamId": {"type": "integer", "format": "int64"}, "url": {"type": "string"}}}, "TeamFavoriteDto": {"title": "TeamFavoriteDto", "type": "object", "properties": {"collectTime": {"type": "string", "format": "date-time"}, "collector": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "resourceId": {"type": "integer", "format": "int64"}, "resourceType": {"type": "string", "enum": ["AK", "Activity", "Audit", "BlockElements", "Cloud", "CrsOrder", "CrsProduct", "DiskFile", "FingerPrint", "FingerPrintTemplate", "Gateway", "GhCreator", "GhGifter", "GhJobPlan", "GhUser", "GhVideoCreator", "GiftCardPack", "InsTeamUser", "InsUser", "Invoice", "Ip", "IpPool", "IppIp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "KakaoFriend", "KolCreator", "MobileAccount", "None", "Orders", "PluginTeamPack", "Record", "RpaFlow", "RpaTask", "RpaTaskItem", "RpaVoucher", "Shop", "ShopSession", "Tag", "TeamDiskRoot", "TeamMobile", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TkCreator", "TkTeamPack", "Tkshop", "<PERSON>ks<PERSON><PERSON>uyer", "TkshopCreator", "TrafficPack", "TunnelVps", "Users", "View", "Voucher", "XhsAccount"]}, "size": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}, "url": {"type": "string"}}}, "TeamFunctionConfigVo": {"title": "TeamFunctionConfigVo", "type": "object", "properties": {"deducting": {"type": "boolean"}, "enabled": {"type": "boolean"}, "functionDesc": {"type": "string"}, "functionName": {"type": "string", "enum": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "RpaCaptcha", "RpaExecuteCloud", "RpaExecuteLocal", "RpaMobile", "RpaOpenAi", "RpaSendSms"]}, "officialPrice": {"type": "number", "format": "double"}, "price": {"type": "number", "format": "double"}, "subKey": {"type": "string"}, "subKeyInfo": {"type": "object"}, "subKeyLabel": {"type": "string"}}}, "TeamInviteVo": {"title": "TeamInviteVo", "type": "object", "properties": {"id": {"type": "integer", "description": "ID", "format": "int64"}, "inviteAuditEnable": {"type": "boolean", "description": "是否开启团队邀请审核", "example": false}, "inviteCode": {"type": "string", "description": "个人邀请码"}, "inviteEnable": {"type": "boolean", "description": "是否开启团队邀请", "example": false}, "teamId": {"type": "integer", "description": "团队ID", "format": "int64"}}}, "TeamQuotaDetailVo": {"title": "TeamQuotaDetailVo", "type": "object", "properties": {"freeQuota": {"type": "number", "description": "团队免费配额，null表示使用官方默认，-1表示无限", "format": "double"}, "ladderPrices": {"type": "array", "description": "阶梯价格", "items": {"$ref": "#/components/schemas/LadderPriceRange"}}, "needPay": {"type": "boolean", "description": "需要付费", "example": false}, "price": {"type": "number", "description": "官方单价（花瓣/天），0表示免费使用", "format": "double"}, "quota": {"type": "number", "description": "（最大）配额(-1表示不限制）", "format": "double"}, "quotaDesc": {"type": "string", "description": "配额描述"}, "quotaName": {"type": "string", "description": "配额名称", "enum": ["ShopQuota", "ShopSecurityPolicy", "StorageQuota", "TeamMemberQuota", "Team<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "used": {"type": "number", "description": "已用配额", "format": "double"}}}, "TeamQuotaVo": {"title": "TeamQuotaVo", "type": "object", "properties": {"freeQuota": {"type": "number", "description": "团队免费配额，null表示使用官方默认，-1表示无限", "format": "double"}, "ladderPrices": {"type": "array", "description": "阶梯价格", "items": {"$ref": "#/components/schemas/LadderPriceRange"}}, "price": {"type": "number", "description": "官方单价（花瓣/天），0表示免费使用", "format": "double"}, "quota": {"type": "number", "description": "（最大）配额(-1表示不限制）", "format": "double"}, "quotaDesc": {"type": "string", "description": "配额描述"}, "quotaName": {"type": "string", "description": "配额名称", "enum": ["ShopQuota", "ShopSecurityPolicy", "StorageQuota", "TeamMemberQuota", "Team<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}}}, "TeamRoleConfig": {"title": "TeamRoleConfig", "type": "object", "properties": {"roleFunctionMap": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/FunctionConfig"}}}}, "TeamStatInfo": {"title": "TeamStatInfo", "type": "object", "properties": {"creatorName": {"type": "string"}, "ipCount": {"type": "integer", "format": "int32"}, "memberCount": {"type": "integer", "format": "int32"}, "orderCount": {"type": "integer", "description": "订单数量", "format": "int32"}, "rpaTaskCount": {"type": "integer", "format": "int32"}, "shopCount": {"type": "integer", "format": "int32"}, "teamBalance": {"type": "number", "format": "bigdecimal"}, "teamCredit": {"type": "number", "description": "剩余花瓣", "format": "bigdecimal"}}}, "TeamSystemConfig": {"title": "TeamSystemConfig", "type": "object", "properties": {"enableTransit": {"type": "boolean", "description": "自有IP允许使用接入点", "example": false}, "greenScan": {"type": "boolean", "description": "发送邮件等时是否启用内容健康扫描", "example": false}, "ipCreateFromEip": {"type": "boolean", "description": "尽量使用EIP的方式来创建IP", "example": false}, "ipNameSpecDynamic": {"type": "string", "description": "导入动态IP命名规范"}, "ipNameSpecPlatform": {"type": "string", "description": "导入平台IP命名规范"}, "ipNameSpecStatic": {"type": "string", "description": "导入静态IP命名规范"}, "ipPurenessCheck": {"type": "boolean", "description": "IP纯净度检查", "example": false}, "ipUniqueCreateRetry": {"type": "integer", "description": "创建IP时，遇到重复IP，重试次数", "format": "int32"}, "ipUniqueMonth": {"type": "integer", "description": "平台IP唯一性检查月数，=0不检查", "format": "int32"}, "sessionProxyHost": {"type": "string", "description": "本地打开会话监听的地址"}, "tkShopHeadless": {"type": "boolean", "description": "TKshop隐式运行", "example": false}}}, "TeamVerifyDto": {"title": "TeamVerifyDto", "type": "object", "properties": {"certName": {"type": "string"}, "certNo": {"type": "string"}, "certType": {"type": "string", "enum": ["BusinessLicense", "IdCard"]}, "charged": {"type": "boolean"}, "enterpriseName": {"type": "string"}, "enterpriseNo": {"type": "string"}, "extraInfo": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "invalidTime": {"type": "string", "format": "date-time"}, "legalIdCard": {"type": "string"}, "legalName": {"type": "string"}, "mobile": {"type": "string"}, "operator": {"type": "integer", "format": "int64"}, "pass": {"type": "boolean"}, "remark": {"type": "string"}, "teamId": {"type": "integer", "format": "int64"}, "valid": {"type": "boolean"}, "verifyProvider": {"type": "string"}, "verifyTime": {"type": "string", "format": "date-time"}}}, "TeamWithRoleVo": {"title": "TeamWithRoleVo", "type": "object", "properties": {"avatar": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "integer", "format": "int64"}, "deleteTime": {"type": "string", "format": "date-time"}, "domesticCloudEnabled": {"type": "boolean"}, "favorite": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "invalidTime": {"type": "string", "format": "date-time"}, "inviteCode": {"type": "integer", "format": "int64"}, "joinTime": {"type": "string", "format": "date-time"}, "name": {"type": "string"}, "overseaCloudEnabled": {"type": "boolean"}, "paid": {"type": "boolean"}, "partnerId": {"type": "integer", "format": "int64"}, "payTime": {"type": "string", "format": "date-time"}, "repurchaseTime": {"type": "string", "format": "date-time"}, "repurchased": {"type": "boolean"}, "roleCode": {"type": "string"}, "status": {"type": "string", "enum": ["Blocked", "Deleted", "Pending", "Ready"]}, "teamType": {"type": "string", "enum": ["crs", "gh", "krShop", "normal", "partner", "plugin", "tk", "tkshop"]}, "teamUserStatus": {"type": "string", "description": "用户在团队内的状态", "enum": ["ACTIVE", "BLOCK", "DELETED", "INACTIVE"]}, "tenantId": {"type": "integer", "format": "int64"}, "testing": {"type": "boolean"}, "validateTime": {"type": "string", "format": "date-time"}, "validated": {"type": "boolean"}, "verified": {"type": "boolean"}}}, "TkTeamRelationDto": {"title": "TkTeamRelationDto", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "dataTeamId": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "operationTeamId": {"type": "integer", "format": "int64"}, "tkRole": {"type": "string", "enum": ["TkClient", "TkData", "TkOperation"]}}}, "UpdateDepartmentOrderParamVo": {"title": "UpdateDepartmentOrderParamVo", "type": "object", "properties": {"sortedDepartments": {"type": "array", "description": "被移动顺序的上级部门列表", "items": {"$ref": "#/components/schemas/SortedDepartmentVo"}}}}, "UpdateMemberConfigRequest": {"title": "UpdateMemberConfigRequest", "type": "object", "properties": {"config": {"type": "object", "description": "配置信息，为null时，删除配置"}, "key": {"type": "string", "description": "配置Key"}}}, "UpdateOpsStrategyItemRequest": {"title": "UpdateOpsStrategyItemRequest", "type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/OpsStrategyItemVo"}}}}, "UserActivityDeviceVo": {"title": "UserActivityDeviceVo", "type": "object", "properties": {"appRemainLogin": {"type": "boolean"}, "authorizedDeviceIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "avatar": {"type": "string"}, "continuousLogin": {"type": "integer", "format": "int32"}, "devices": {"type": "array", "items": {"$ref": "#/components/schemas/LoginDeviceLocationVo"}}, "domestic": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "lastCity": {"type": "string"}, "lastLoginTime": {"type": "string", "format": "date-time"}, "lastLogoutTime": {"type": "string", "format": "date-time"}, "lastRemoteIps": {"type": "string"}, "loginCount": {"type": "integer", "format": "int32"}, "nickname": {"type": "string"}, "online": {"type": "boolean"}, "onlineTotalTime": {"type": "integer", "format": "int32"}, "roleCode": {"type": "string", "enum": ["boss", "manager", "staff", "superadmin"]}, "strategyGroup": {"$ref": "#/components/schemas/OpsStrategyGroupVo"}, "vitality": {"type": "integer", "format": "int32"}}}, "UserActivityDto": {"title": "UserActivityDto", "type": "object", "properties": {"appRemainLogin": {"type": "boolean"}, "continuousLogin": {"type": "integer", "format": "int32"}, "domestic": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "lastCity": {"type": "string"}, "lastLoginTime": {"type": "string", "format": "date-time"}, "lastLogoutTime": {"type": "string", "format": "date-time"}, "lastRemoteIps": {"type": "string"}, "loginCount": {"type": "integer", "format": "int32"}, "online": {"type": "boolean"}, "onlineTotalTime": {"type": "integer", "format": "int32"}, "vitality": {"type": "integer", "format": "int32"}}}, "UserDepartmentVo": {"title": "UserDepartmentVo", "type": "object", "properties": {"grantShopList": {"type": "array", "items": {"$ref": "#/components/schemas/ShopDto"}}, "grantShopNumber": {"type": "integer", "format": "int32"}, "id": {"type": "integer", "format": "int64"}, "nickname": {"type": "string"}, "role": {"type": "string"}}}, "UserDetailVo": {"title": "UserDetailVo", "type": "object", "properties": {"account": {"type": "string", "description": "账号"}, "avatar": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "email": {"type": "string", "description": "邮箱"}, "gender": {"type": "string", "enum": ["FEMALE", "MALE", "UNSPECIFIC"]}, "id": {"type": "integer", "format": "int64"}, "nickname": {"type": "string"}, "partnerId": {"type": "integer", "format": "int64"}, "phone": {"type": "string", "description": "手机"}, "residentCity": {"type": "string"}, "resourceGranted": {"type": "boolean"}, "role": {"description": "所属角色", "$ref": "#/components/schemas/RoleVo"}, "shopCount": {"type": "integer", "description": "授权的账户数量", "format": "int32"}, "signature": {"type": "string"}, "status": {"type": "string", "enum": ["ACTIVE", "BLOCK", "DELETED", "INACTIVE"]}, "teamUserStatus": {"type": "string", "description": "用户在团队的状态", "enum": ["ACTIVE", "BLOCK", "DELETED", "INACTIVE"]}, "tenant": {"type": "integer", "format": "int64"}, "userType": {"type": "string", "enum": ["NORMAL", "PARTNER", "SHADOW"]}, "viewDetail": {"type": "boolean", "description": "是否可以查看详情", "example": false}, "weixin": {"type": "string"}}}, "UserFunctionDto": {"title": "UserFunctionDto", "type": "object", "properties": {"functionCode": {"type": "string", "enum": ["DISK_SPACE_MANAGER", "DISK_TEAM_MANAGER", "DISK_USER_MANAGER", "EXPENSE_MANAGE", "EXPENSE_VIEW", "EXTENSION_LIST", "EXTENSION_MANAGER", "FINGERPRINT_CONFIG", "FINGERPRINT_LIST", "FINGERPRINT_MANAGER", "IP_CONFIG", "IP_LIST", "IP_MANAGE", "KOL_LIST", "KOL_MANAGE", "MOBILE_CONFIG", "MOBILE_IMPORT_DELETE", "OPERATE_LOG_GET_IP", "OPERATE_LOG_GET_LOGIN", "OPERATE_LOG_GET_SHOP", "OPERATE_LOG_GET_TEAM_MANAGE", "OPERATE_LOG_MANAGE_SHOP", "RPA_CARD_MANAGER", "RPA_CREATE_DELETE", "RPA_LIST", "RPA_OPEN_API", "RPA_PLAN", "RPA_RUN", "SHOP_AUTHORIZE", "SHOP_BIND_IP_MANAGE", "SHOP_CONFIG", "SHOP_FINGERPRINT_MANAGE", "SHOP_IMPORT_DELETE", "TEAM_AUDIT", "TEAM_CRITICAL_MANAGE", "TEAM_MANAGE", "TEAM_RESOURCE_MANAGE", "TEAM_VIEW_MEMBER", "TKSHOP_BUYER_MANAGER", "TKSHOP_CREATOR_MANAGER", "TKSHOP_GLOBAL_CREATOR_MANAGER", "TKSHOP_MANAGE", "TK_PACK_MANAGE", "TK_流程计划编排", "TK_达人管理", "TK_达人运营", "TK_达人邀约", "TK_运营辅助"]}, "granted": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "teamId": {"type": "integer", "format": "int64"}, "userId": {"type": "integer", "format": "int64"}}}, "UserFunctionMetaVo": {"title": "UserFunctionMetaVo", "type": "object", "properties": {"function": {"description": "权限信息", "$ref": "#/components/schemas/FunctionVo"}, "granted": {"type": "boolean", "description": "当前是否已经授权", "example": false}, "roleConfigList": {"type": "array", "description": "每个角色的配置信息", "items": {"$ref": "#/components/schemas/FunctionRoleConfig"}}}}, "UserLoginDeviceVo": {"title": "UserLoginDeviceVo", "type": "object", "properties": {"appId": {"type": "string"}, "appVersion": {"type": "string"}, "clientIp": {"type": "string"}, "clientLocation": {"type": "integer", "format": "int64"}, "cpus": {"type": "integer", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}, "currentDevice": {"type": "boolean"}, "deviceId": {"type": "string"}, "deviceType": {"type": "string", "enum": ["App", "Browser", "HYRuntime", "RpaExecutor"]}, "domestic": {"type": "boolean"}, "hostName": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "ipData": {"$ref": "#/components/schemas/IpDataDto"}, "ipDataId": {"type": "integer", "format": "int64"}, "lastActiveTime": {"type": "string", "format": "date-time"}, "lastCity": {"type": "string"}, "lastLogTime": {"type": "string", "format": "date-time"}, "lastLoginTime": {"type": "string", "format": "date-time"}, "lastRemoteIp": {"type": "string"}, "lastUserId": {"type": "integer", "format": "int64"}, "logUrl": {"type": "string"}, "mem": {"type": "integer", "format": "int64"}, "online": {"type": "boolean"}, "osName": {"type": "string"}, "scope": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "Ipp", "Openapi", "Partner", "Portal"]}, "userAgent": {"type": "string"}, "userId": {"type": "integer", "format": "int64"}, "version": {"type": "string"}}}, "UserPromoInfo": {"title": "UserPromoInfo", "type": "object", "properties": {"code": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "phoneRewardId": {"type": "integer", "format": "int64"}, "promoUserId": {"type": "integer", "format": "int64"}, "totalRewardAmount": {"type": "integer", "description": "总奖励数量", "format": "int32"}, "url": {"type": "string"}, "wechatRewardId": {"type": "integer", "format": "int64"}}}, "UserPromotedRecordVo": {"title": "UserPromotedRecordVo", "type": "object", "properties": {"createTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int64"}, "nickname": {"type": "string"}, "promoUserId": {"type": "integer", "format": "int64"}, "promotedUserId": {"type": "integer", "format": "int64"}, "rewardAmount": {"type": "integer", "format": "int32"}, "rewardId": {"type": "integer", "format": "int64"}, "rewardTime": {"type": "string", "format": "date-time"}}}, "UserShopTransferParamVo": {"title": "UserShopTransferParamVo", "type": "object", "properties": {"copyGrant": {"type": "boolean", "description": "是否复制权限授权记录", "example": false}, "shopIdList": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "toUserId": {"type": "integer", "format": "int64"}}}, "UserTeamVo": {"title": "UserTeamVo", "type": "object", "properties": {"account": {"type": "string", "description": "账号"}, "avatar": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "departmentList": {"type": "array", "description": "部门", "items": {"$ref": "#/components/schemas/DepartmentDto"}}, "email": {"type": "string", "description": "邮箱"}, "gender": {"type": "string", "enum": ["FEMALE", "MALE", "UNSPECIFIC"]}, "id": {"type": "integer", "format": "int64"}, "joinTime": {"type": "string", "description": "加入时间", "format": "date-time"}, "nickname": {"type": "string"}, "openAccounts": {"type": "array", "description": "绑定的开放账户", "items": {"$ref": "#/components/schemas/OpenAccountDto"}}, "partnerId": {"type": "integer", "format": "int64"}, "phone": {"type": "string", "description": "手机"}, "residentCity": {"type": "string"}, "role": {"type": "string", "description": "角色", "enum": ["boss", "manager", "staff", "superadmin"]}, "signature": {"type": "string"}, "status": {"type": "string", "enum": ["ACTIVE", "BLOCK", "DELETED", "INACTIVE"]}, "tenant": {"type": "integer", "format": "int64"}, "userType": {"type": "string", "enum": ["NORMAL", "PARTNER", "SHADOW"]}, "weixin": {"type": "string"}}}, "UserVo": {"title": "UserVo", "type": "object", "properties": {"account": {"type": "string", "description": "账号"}, "avatar": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "email": {"type": "string", "description": "邮箱"}, "gender": {"type": "string", "enum": ["FEMALE", "MALE", "UNSPECIFIC"]}, "id": {"type": "integer", "format": "int64"}, "nickname": {"type": "string"}, "partnerId": {"type": "integer", "format": "int64"}, "phone": {"type": "string", "description": "手机"}, "residentCity": {"type": "string"}, "signature": {"type": "string"}, "status": {"type": "string", "enum": ["ACTIVE", "BLOCK", "DELETED", "INACTIVE"]}, "tenant": {"type": "integer", "format": "int64"}, "userType": {"type": "string", "enum": ["NORMAL", "PARTNER", "SHADOW"]}, "weixin": {"type": "string"}}}, "WebResult": {"title": "WebResult", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«AlipayPreconsultResult»": {"title": "WebResult«AlipayPreconsultResult»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/AlipayPreconsultResult"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«AlipayVerifyResultVo»": {"title": "WebResult«AlipayVerifyResultVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/AlipayVerifyResultVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«AuditDetailVo»": {"title": "WebResult«AuditDetailVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/AuditDetailVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«AuditResultVo»": {"title": "WebResult«AuditResultVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/AuditResultVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«CheckAndJoinDemoTeamResult»": {"title": "WebResult«CheckAndJoinDemoTeamResult»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/CheckAndJoinDemoTeamResult"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«ConfirmInviteJoinTeamResult»": {"title": "WebResult«ConfirmInviteJoinTeamResult»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/ConfirmInviteJoinTeamResult"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«DepartmentDto»": {"title": "WebResult«DepartmentDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/DepartmentDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«GbsConfig»": {"title": "WebResult«GbsConfig»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/GbsConfig"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«GiftCardPackDetailVo»": {"title": "WebResult«GiftCardPackDetailVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/GiftCardPackDetailVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«GrantInfo»": {"title": "WebResult«GrantInfo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/GrantInfo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«HyRuntimeDto»": {"title": "WebResult«HyRuntimeDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/HyRuntimeDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«InviteInfoVo»": {"title": "WebResult«InviteInfoVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/InviteInfoVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«InviteJoinTeamCheckResult»": {"title": "WebResult«InviteJoinTeamCheckResult»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/InviteJoinTeamCheckResult"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«InviteLinkVo»": {"title": "WebResult«InviteLinkVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/InviteLinkVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«JoinTeamResultVo»": {"title": "WebResult«JoinTeamResultVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/JoinTeamResultVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«JwtResultVo»": {"title": "WebResult«JwtResultVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/JwtResultVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«DepartmentTreeNode»»": {"title": "WebResult«List«DepartmentTreeNode»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/DepartmentTreeNode"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«FavoriteDto»»": {"title": "WebResult«List«FavoriteDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/FavoriteDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«FunctionMetaVo»»": {"title": "WebResult«List«FunctionMetaVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/FunctionMetaVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«FunctionVo»»": {"title": "WebResult«List«FunctionVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/FunctionVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«LoginDeviceDto»»": {"title": "WebResult«List«LoginDeviceDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/LoginDeviceDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«LoginDeviceSessionVo»»": {"title": "WebResult«List«LoginDeviceSessionVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/LoginDeviceSessionVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«LoginDeviceTransitDto»»": {"title": "WebResult«List«LoginDeviceTransitDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/LoginDeviceTransitDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«TeamFavoriteDto»»": {"title": "WebResult«List«TeamFavoriteDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TeamFavoriteDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«TeamFunctionConfigVo»»": {"title": "WebResult«List«TeamFunctionConfigVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TeamFunctionConfigVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«TeamQuotaVo»»": {"title": "WebResult«List«TeamQuotaVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TeamQuotaVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«TeamWithRoleVo»»": {"title": "WebResult«List«TeamWithRoleVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TeamWithRoleVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«UserActivityDeviceVo»»": {"title": "WebResult«List«UserActivityDeviceVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/UserActivityDeviceVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«UserFunctionDto»»": {"title": "WebResult«List«UserFunctionDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/UserFunctionDto"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«UserFunctionMetaVo»»": {"title": "WebResult«List«UserFunctionMetaVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/UserFunctionMetaVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«UserLoginDeviceVo»»": {"title": "WebResult«List«UserLoginDeviceVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/UserLoginDeviceVo"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«List«long»»": {"title": "WebResult«List«long»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«LoginDeviceLocationVo»": {"title": "WebResult«LoginDeviceLocationVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/LoginDeviceLocationVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«LoginResultVo»": {"title": "WebResult«LoginResultVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/LoginResultVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«Map«long,boolean»»": {"title": "WebResult«Map«long,boolean»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object", "additionalProperties": {"type": "boolean"}}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«Mobile3ElementResult»": {"title": "WebResult«Mobile3ElementResult»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/Mobile3ElementResult"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«OAuthProcessInfo»": {"title": "WebResult«OAuthProcessInfo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/OAuthProcessInfo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«OpStatVo»": {"title": "WebResult«OpStatVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/OpStatVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«OpsStrategyGroupDto»": {"title": "WebResult«OpsStrategyGroupDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/OpsStrategyGroupDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«OpsStrategyGroupVo»": {"title": "WebResult«OpsStrategyGroupVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/OpsStrategyGroupVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«FavoriteDetailVo»»": {"title": "WebResult«PageResult«FavoriteDetailVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«FavoriteDetailVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«FavoriteDto»»": {"title": "WebResult«PageResult«FavoriteDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«FavoriteDto»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«OpsStrategyGroupVo»»": {"title": "WebResult«PageResult«OpsStrategyGroupVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«OpsStrategyGroupVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«TeamFavoriteDetailVo»»": {"title": "WebResult«PageResult«TeamFavoriteDetailVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«TeamFavoriteDetailVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«TeamFavoriteDto»»": {"title": "WebResult«PageResult«TeamFavoriteDto»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«TeamFavoriteDto»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«UserActivityDeviceVo»»": {"title": "WebResult«PageResult«UserActivityDeviceVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«UserActivityDeviceVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«UserDetailVo»»": {"title": "WebResult«PageResult«UserDetailVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«UserDetailVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«PageResult«UserPromotedRecordVo»»": {"title": "WebResult«PageResult«UserPromotedRecordVo»»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/PageResult«UserPromotedRecordVo»"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«RegisterAccountVo»": {"title": "WebResult«RegisterAccountVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/RegisterAccountVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«RegisterConfig»": {"title": "WebResult«RegisterConfig»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/RegisterConfig"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«RoleVo»": {"title": "WebResult«RoleVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/RoleVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TeamConfigDto»": {"title": "WebResult«TeamConfigDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TeamConfigDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TeamDetailVo»": {"title": "WebResult«TeamDetailVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TeamDetailVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TeamDeviceConfig»": {"title": "WebResult«TeamDeviceConfig»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TeamDeviceConfig"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TeamDto»": {"title": "WebResult«TeamDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TeamDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TeamInviteVo»": {"title": "WebResult«TeamInviteVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TeamInviteVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TeamQuotaDetailVo»": {"title": "WebResult«TeamQuotaDetailVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TeamQuotaDetailVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TeamRoleConfig»": {"title": "WebResult«TeamRoleConfig»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TeamRoleConfig"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TeamStatInfo»": {"title": "WebResult«TeamStatInfo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TeamStatInfo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TeamSystemConfig»": {"title": "WebResult«TeamSystemConfig»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TeamSystemConfig"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«TeamVerifyDto»": {"title": "WebResult«TeamVerifyDto»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/TeamVerifyDto"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«UserDepartmentVo»": {"title": "WebResult«UserDepartmentVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/UserDepartmentVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«UserDetailVo»": {"title": "WebResult«UserDetailVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/UserDetailVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«UserPromoInfo»": {"title": "WebResult«UserPromoInfo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/UserPromoInfo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«UserTeamVo»": {"title": "WebResult«UserTeamVo»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/components/schemas/UserTeamVo"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«boolean»": {"title": "WebResult«boolean»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "boolean"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«int»": {"title": "WebResult«int»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}, "WebResult«string»": {"title": "WebResult«string»", "type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "string"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "success": {"type": "boolean"}}}}, "securitySchemes": {"Authorization": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}, "x-dm-team-id": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-dm-team-id", "in": "header"}}}}
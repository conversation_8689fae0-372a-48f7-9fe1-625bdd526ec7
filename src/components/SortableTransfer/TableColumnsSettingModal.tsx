import I18N from '@/i18n';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import _ from 'lodash';
import { arrayMove } from '@dnd-kit/sortable';
import { <PERSON><PERSON>, Card, Col, Row, Space } from 'antd';
import classNames from 'classnames';
import DMModal from '@/components/Common/Modal/DMModal';
import styles from '@/pages/TikTok/components/style.less';
import SortableList from '@/components/SortableTransfer/SortableList';
import IconFontIcon from '@/components/Common/IconFontIcon';

export function getColumnByDataIndex(key: string, meta: any[]) {
  return meta.find((i) => i.dataIndex === key);
}

const TableColumnsSettingModal = (props: {
  columns: string[];
  meta: {
    dataIndex: string;
    title: string;
    disabled?: boolean;
    [key: string]: any;
  }[];
  onSubmit: (keys: string[]) => void;
}) => {
  const { onSubmit, columns, meta } = props;
  const [visible, changeVisible] = useState(true);
  const [selected, changeSelected] = useState<string>();
  const [direction, changeDirection] = useState<'left' | 'right'>();
  const getColumn = useCallback(
    (key: string) => {
      return getColumnByDataIndex(key, meta);
    },
    [meta],
  );
  const [activeColumns, setActiveColumns] = useState<string[]>([]);
  useEffect(() => {
    setActiveColumns(columns);
  }, [columns]);
  const init = useRef(false);
  useEffect(() => {
    if (!selected && columns?.length && !init.current) {
      changeSelected(_.find(columns, (key) => !getColumn(key)?.disabled));
      init.current = true;
    }
  }, [selected, columns]);
  const activeFields = useMemo(() => {
    const _active: { title: string; dataIndex: string; disabled?: boolean }[] = [];
    activeColumns?.forEach((key) => {
      const item = meta.find((i) => {
        return key === i.dataIndex;
      });
      if (item) {
        _active.push(item);
      }
    });
    return _.uniqBy(_active, 'dataIndex');
  }, [activeColumns, meta]);
  const resetFields = useMemo(() => {
    const _resets: { title: string; dataIndex: string }[] = [];
    meta?.forEach((item) => {
      const index = activeColumns.findIndex((i) => {
        return i === item.dataIndex;
      });
      if (index === -1) {
        _resets.push(item);
      }
    });
    return _resets;
  }, [activeColumns, meta]);
  const resetColumns = useMemo(() => {
    return resetFields?.map((i) => {
      return i.dataIndex;
    });
  }, [resetFields]);
  useEffect(() => {
    if (selected) {
      if (_.findIndex(activeColumns, (i) => i === selected) !== -1) {
        changeDirection('left');
      } else if (_.findIndex(resetColumns, (i) => i === selected) !== -1) {
        changeDirection('right');
      }
    }
  }, [activeColumns, selected, resetFields]);
  const autoSelectNext = useCallback(
    (arr, i) => {
      const item = getColumn(arr[i]);
      if (!item) {
        changeSelected(undefined);
        return;
      }
      if (arr.length === 1) {
        if (item?.disabled) {
          changeSelected(undefined);
        } else {
          changeSelected(arr[0]);
        }
      } else if (i > arr.length - 1) {
        if (item?.disabled) {
          autoSelectNext(arr, 0);
        } else {
          changeSelected(arr[i]);
        }
      } else if (item?.disabled) {
        autoSelectNext(arr, i + 1);
      } else {
        changeSelected(arr[i]);
      }
    },
    [getColumn],
  );
  // 取消选择
  const onDeselect = useCallback(
    async (index: number) => {
      setActiveColumns((prev) => {
        const _columns = [...prev];
        _columns.splice(index, 1);
        autoSelectNext(_columns, index);
        return _columns;
      });
    },
    [autoSelectNext],
  );
  // 选择
  const onSelect = useCallback(
    (key = selected) => {
      setActiveColumns((prevState) => {
        const index = _.findIndex(resetColumns, (i) => i === key);
        const _columns = [...prevState];
        const findLastKey = _.findLastIndex(_columns, (_key) => {
          return _.findIndex(meta, (i) => i.dataIndex === _key && i.disabled) !== -1;
        });
        _columns.splice(findLastKey, 0, key!);
        autoSelectNext(
          resetColumns.filter((i) => i !== key),
          index,
        );
        return _columns;
      });
    },
    [autoSelectNext, resetColumns, selected, meta],
  );
  const onSort = useCallback(async (oldId, newId) => {
    setActiveColumns((prev) => {
      const _columns = [...prev];
      const oldIndex = _.findIndex(_columns, (i) => i === oldId);
      const newIndex = _.findIndex(_columns, (i) => i === newId);
      return arrayMove(_columns, oldIndex, newIndex);
    });
  }, []);
  const remainNodes = useMemo(() => {
    return resetFields?.map(({ title, dataIndex }) => {
      return (
        <Row
          onClick={() => {
            changeSelected(dataIndex);
          }}
          onDoubleClick={() => {
            onSelect(dataIndex);
          }}
          wrap={false}
          key={dataIndex}
          gutter={[8, 8]}
          className={classNames({ active: selected === dataIndex })}
        >
          <Col flex={1}>{title}</Col>
        </Row>
      );
    });
  }, [selected, onSelect, resetFields]);
  const leftDisabled = useMemo(() => {
    return !selected || direction !== 'right';
  }, [selected, direction]);
  const rightDisabled = useMemo(() => {
    return !selected || direction !== 'left';
  }, [selected, direction]);

  return (
    <DMModal
      className={styles.columnPlanModal}
      width={800}
      onOk={() => {
        onSubmit(activeColumns);
        changeVisible(false);
      }}
      title={I18N.t('表格字段配置')}
      open={visible}
      onCancel={(e) => {
        changeVisible(false);
      }}
    >
      <Row align={'stretch'} gutter={[12, 12]} style={{ height: 405 }} wrap={false}>
        <Col span={11}>
          <Card title={I18N.t('当前显示字段')}>
            <SortableList
              onDragEnd={(e) => {
                const { active, over } = e;
                if (over && active) {
                  if (active.id !== over.id) {
                    onSort(active.id, over.id);
                  }
                }
              }}
              onRow={(item, index) => {
                return {
                  onClick() {
                    if (!item.disabled) {
                      changeSelected(item.id);
                    }
                  },
                  onDoubleClick() {
                    if (!item.disabled!) {
                      onDeselect(index);
                    }
                  },
                };
              }}
              activeKey={selected}
              items={
                [
                  ...(activeFields?.map(({ title, dataIndex, disabled }) => {
                    return {
                      id: dataIndex,
                      title,
                      disabled,
                    };
                  }) || []),
                ] || []
              }
            />
          </Card>
        </Col>
        <Col
          span={2}
          style={{
            display: 'flex',
            alignItems: 'center',
            flexDirection: 'column',
            justifyContent: 'center',
          }}
        >
          <Space direction={'vertical'} size={'large'}>
            <Button
              size={'small'}
              type={'primary'}
              disabled={rightDisabled}
              onClick={() => {
                // 取消激活
                const index = _.findIndex(activeColumns, (i) => {
                  return i === selected;
                });
                if (index !== -1) {
                  onDeselect(index);
                }
              }}
            >
              <IconFontIcon iconName="angle-right_24" />
            </Button>
            <Button
              size={'small'}
              type={'primary'}
              disabled={leftDisabled}
              onClick={() => {
                onSelect();
              }}
            >
              <IconFontIcon iconName="angle-left_24" />
            </Button>
          </Space>
        </Col>
        <Col span={11}>
          <Card title={I18N.t('待显示字段')} className={styles.columnPlanList}>
            {remainNodes}
          </Card>
        </Col>
      </Row>
    </DMModal>
  );
};
export default TableColumnsSettingModal;

//@i18n-ignore

import I18N from '@/i18n';
import { useRequest } from '@@/plugin-request/request';
import {
  ghSettingsCountriesPut,
  ghSettingsCreatorTypesPut,
  ghSettingsKolConfigGet,
  ghSettingsLiveHoursPut,
  ghSettingsMinRevenuePut,
} from '@/services/api-TKGHAPI/GhSettingsController';
import { useMemo, useState } from 'react';
import { Alert, Card, Checkbox, Form, InputNumber, message, Select, Space, Typography } from 'antd';
import IconFontIcon from '@/components/Common/IconFontIcon';
import styles from '@/pages/Setting/style.less';
import DMFormItem, { DMFormItemContext } from '@/components/Common/DMFormItem';
import { getCountryNameByAlpha2Code } from '@/components/Common/Selector/CreatorRegionFormItem';
import DMModal from '@/components/Common/Modal/DMModal';
import { GhostModalCaller } from '@/mixins/modal';
import DMConfirm from '@/components/Common/DMConfirm';
import InfoTip from '@/components/Tips/InfoTip';
import _ from 'lodash';

// https://wiki.thinkoncloud.cn/pages/viewpage.action?pageId=222101505
const META_DATA = [
  {
    name: '美国',
    code: 'US',
    countries: [
      { name: '美国', code: 'US' },
      { name: '加拿大', code: 'CA' },
    ],
  },
  {
    name: '巴西',
    code: 'BR',
    countries: [{ name: '巴西', code: 'BR' }],
  },
  {
    name: '拉丁美洲',
    code: 'LATAM',
    countries: [
      { name: '阿根廷', code: 'AR' },
      { name: '智利', code: 'CL' },
      { name: '墨西哥', code: 'MX' },
      { name: '哥伦比亚', code: 'CO' },
      { name: '秘鲁', code: 'PE' },
      { name: '乌拉圭', code: 'UY' },
      { name: '巴拉圭', code: 'PY' },
      { name: '委内瑞拉', code: 'VE' },
      { name: '厄瓜多尔', code: 'EC' },
      { name: '多米尼加共和国', code: 'DO' },
      { name: '洪都拉斯', code: 'HN' },
      { name: '尼加拉瓜', code: 'NI' },
      { name: '萨尔瓦多', code: 'SV' },
      { name: '哥斯达黎加', code: 'CR' },
      { name: '巴拿马', code: 'PA' },
      { name: '玻利维亚', code: 'BO' },
      { name: '危地马拉', code: 'GT' },
      { name: '波多黎各', code: 'PR' },
      { name: '几内亚', code: 'GN' },
    ],
  },
  {
    name: '澳大利亚+',
    code: 'AU+',
    countries: [
      { name: '澳大利亚', code: 'AU' },
      { name: '新西兰', code: 'NZ' },
      { name: '斐济', code: 'FJ' },
      { name: '巴布亚新几内亚', code: 'PG' },
      { name: '汤加', code: 'TO' },
      { name: '萨摩亚', code: 'WS' },
      { name: '瓦努阿图', code: 'VU' },
      { name: '库克群岛', code: 'CK' },
      { name: '密克罗尼西亚联邦', code: 'FM' },
      { name: '帕劳', code: 'PW' },
      { name: '马绍尔群岛', code: 'MH' },
      { name: '基里巴斯', code: 'KI' },
      { name: '图瓦卢', code: 'TV' },
      { name: '纽埃', code: 'NU' },
      { name: '所罗门群岛', code: 'SB' },
      { name: '圣诞岛', code: 'CX' },
      { name: '诺福克岛', code: 'NF' },
      { name: '库克群岛', code: 'CC' },
      { name: '瑙鲁', code: 'NR' },
      { name: '皮特凯恩群岛', code: 'PN' },
      { name: '东图瓦卢', code: 'TK' },
      { name: '瓦利斯和富图纳', code: 'WF' },
    ],
  },
  {
    name: '日本',
    code: 'JP',
    countries: [{ name: '日本', code: 'JP' }],
  },
  {
    name: '韩国',
    code: 'KR',
    countries: [{ name: '韩国', code: 'KR' }],
  },
  {
    name: '印度尼西亚',
    code: 'ID',
    countries: [{ name: '印度尼西亚', code: 'ID' }],
  },
  {
    name: '越南',
    code: 'VN',
    countries: [{ name: '越南', code: 'VN' }],
  },
  {
    name: '泰国',
    code: 'TH',
    countries: [{ name: '泰国', code: 'TH' }],
  },
  {
    name: '新加坡-马来西亚',
    code: 'SG-MY',
    countries: [
      { name: '马来西亚', code: 'MY' },
      { name: '新加坡', code: 'SG' },
    ],
  },
  {
    name: '台湾',
    code: 'TW',
    countries: [{ name: '台湾', code: 'TW' }],
  },
  {
    name: '菲律宾',
    code: 'PH',
    countries: [
      {
        name: '菲律宾',
        code: 'PH',
      },
    ],
  },
  {
    name: '中东和北非地区',
    code: 'MENA',
    countries: [
      { name: '埃及', code: 'EG' },
      { name: '摩洛哥', code: 'MA' },
      { name: '沙特阿拉伯', code: 'SA' },
      { name: '阿曼', code: 'OM' },
      { name: '阿联酋', code: 'AE' },
      { name: '突尼斯', code: 'TN' },
      { name: '巴林', code: 'BH' },
      { name: '约旦', code: 'JO' },
      { name: '阿尔及利亚', code: 'DZ' },
      { name: '卡塔尔', code: 'QA' },
      { name: '科威特', code: 'KW' },
      { name: '黎巴嫩', code: 'LB' },
      { name: '吉布提', code: 'DJ' },
      { name: '伊拉克', code: 'IQ' },
      { name: '科摩罗', code: 'KM' },
      { name: '利比亚', code: 'LY' },
      { name: '毛里塔尼亚', code: 'MR' },
      { name: '巴勒斯坦', code: 'PS' },
      { name: '苏丹', code: 'SD' },
      { name: '索马里', code: 'SO' },
      { name: '也门', code: 'YE' },
    ],
  },
  {
    name: '土耳其',
    code: 'TR',
    countries: [{ name: '土耳其', code: 'TR' }],
  },
  {
    name: '中亚',
    countries: [
      { name: '白俄罗斯', code: 'BY' },
      { name: '阿塞拜疆', code: 'AZ' },
      { name: '哈萨克斯坦', code: 'KZ' },
      { name: '吉尔吉斯斯坦', code: 'KG' },
      { name: '塔吉克斯坦', code: 'TJ' },
      { name: '土库曼斯坦', code: 'TM' },
      { name: '乌兹别克斯坦', code: 'UZ' },
      { name: '亚美尼亚', code: 'AM' },
      { name: '摩尔多瓦', code: 'MD' },
      { name: '格鲁吉亚', code: 'GE' },
    ],
    code: 'RU+',
  },
  {
    name: '英国+',
    code: 'UK+',
    countries: [
      { name: '英国', code: 'GB' },
      { name: '曼岛', code: 'IM' },
      { name: '根西', code: 'GG' },
      { name: '泽西', code: 'JE' },
      { name: '希腊', code: 'GR' },
      { name: '阿尔巴尼亚', code: 'AL' },
      { name: '爱尔兰', code: 'IE' },
      { name: '立陶宛', code: 'LT' },
      { name: '塞尔维亚', code: 'RS' },
      { name: '拉脱维亚', code: 'LV' },
      { name: '爱沙尼亚', code: 'EE' },
      { name: '北马其顿', code: 'MK' },
      { name: '波斯尼亚和黑塞哥维那', code: 'BA' },
      { name: '马耳他', code: 'MT' },
      { name: '黑山', code: 'ME' },
      { name: '法罗群岛', code: 'FO' },
      { name: '直布罗陀', code: 'GI' },
      { name: '刚果民主共和国', code: 'CD' },
      { name: '圣马力诺', code: 'SM' },
      { name: '斯瓦尔巴和扬马延', code: 'SJ' },
    ],
  },
  {
    name: '法国+',
    code: 'FR+',
    countries: [
      { name: '法国', code: 'FR' },
      { name: '留尼汪', code: 'RE' },
      { name: '法属圭亚那', code: 'GF' },
      { name: '比利时', code: 'BE' },
      { name: '摩纳哥', code: 'MC' },
      { name: '法属波利尼西亚', code: 'PF' },
      { name: '法属南部和南极领地', code: 'TF' },
    ],
  },
  {
    name: '意大利',
    code: 'IT',
    countries: [
      {
        name: '意大利',
        code: 'IT',
      },
    ],
  },
  {
    name: '西班牙+',
    code: 'ES+',
    countries: [
      { name: '西班牙', code: 'ES' },
      { name: '安道尔', code: 'AD' },
    ],
  },
  {
    name: '德国+',
    code: 'DE+',
    countries: [
      { name: '德国', code: 'DE' },
      { name: '奥地利', code: 'AT' },
      { name: '瑞士', code: 'CH' },
      { name: '列支敦士登', code: 'LI' },
      { name: '卢森堡', code: 'LU' },
    ],
  },
  {
    name: '罗马尼亚',
    code: 'RO',
    countries: [{ name: '罗马尼亚', code: 'RO' }],
  },
  {
    name: '瑞典+',
    code: 'SE+',
    countries: [
      { name: '瑞典', code: 'SE' },
      { name: '芬兰', code: 'FI' },
      { name: '挪威', code: 'NO' },
      { name: '丹麦', code: 'DK' },
      { name: '冰岛', code: 'IS' },
      { name: '格林兰', code: 'GL' },
    ],
  },
  {
    name: '波兰',
    code: 'PL',
    countries: [{ name: '波兰', code: 'PL' }],
  },
  {
    name: '荷兰',
    code: 'NL',
    countries: [{ name: '荷兰', code: 'NL' }],
  },
  {
    name: '葡萄牙',
    code: 'PT',
    countries: [
      { name: '葡萄牙', code: 'PT' },
      { name: '安哥拉', code: 'AO' },
      { name: '佛得角', code: 'CV' },
      { name: '赤道几内亚', code: 'GQ' },
      { name: '几内亚比绍', code: 'GW' },
      { name: '莫桑比克', code: 'MZ' },
      { name: '圣多美和普林西比', code: 'ST' },
    ],
  },
  {
    name: '匈牙利',
    code: 'HU',
    countries: [{ name: '匈牙利', code: 'HU' }],
  },
  {
    name: '巴尔干半岛',
    code: 'BK',
    countries: [
      { name: '希腊', code: 'GR' },
      { name: '保加利亚', code: 'BG' },
      { name: '波黑', code: 'BA' },
      { name: '塞尔维亚', code: 'RS' },
      { name: '克罗地亚', code: 'HR' },
      { name: '阿尔巴尼亚', code: 'AL' },
      { name: '黑山', code: 'ME' },
      { name: '北马其顿', code: 'MK' },
      { name: '斯洛文尼亚', code: 'SI' },
    ],
  },
];

const RevenueConfigModal = (props: { value: number; onUpdate: () => void }) => {
  const { value, onUpdate } = props;
  const [open, setOpen] = useState(true);
  const [form] = Form.useForm();
  const { run: submit, loading } = useRequest(
    async () => {
      const values = await form.validateFields();
      await ghSettingsMinRevenuePut(values);
      message.success(I18N.t('修改成功'));
      setOpen(false);
      onUpdate();
    },
    {
      manual: true,
    },
  );
  return (
    <DMModal
      title={I18N.t('最低流水配置')}
      width={520}
      onOk={submit}
      confirmLoading={loading}
      open={open}
      onCancel={() => {
        setOpen(false);
      }}
    >
      <Form requiredMark={false} form={form}>
        <DMFormItem
          disableLabelMuted
          label={I18N.t('最低流水要求')}
          name={'minRevenue'}
          rules={[
            {
              required: true,
              message: I18N.t('请设置最低流水要求'),
              type: 'number',
            },
          ]}
          initialValue={value}
        >
          <InputNumber min={0} precision={0} max={200} />
        </DMFormItem>
        <InfoTip
          type={'info'}
          style={{ alignItems: 'flex-start' }}
          message={
            <div style={{ marginTop: -4 }}>
              <Typography.Text style={{ whiteSpace: 'wrap' }}>
                {I18N.t(
                  '历史最高流水大于上述数值的主播才允许抓取到您的团队，最小值为0，最大值为200，请注意，最低流水要求越高，每日抓取的符合要求的主播数量越少',
                )}
              </Typography.Text>
            </div>
          }
        />
      </Form>
    </DMModal>
  );
};
const LiveTimeConfigModal = (props: { value: number; onUpdate: () => void }) => {
  const { value, onUpdate } = props;
  const [open, setOpen] = useState(true);
  const [form] = Form.useForm();
  const { run: submit, loading } = useRequest(
    async () => {
      const values = await form.validateFields();
      await ghSettingsLiveHoursPut({ liveHours: values.liveHours * 24 });
      message.success(I18N.t('修改成功'));
      setOpen(false);
      onUpdate();
    },
    {
      manual: true,
    },
  );
  return (
    <DMModal
      title={I18N.t('最近开播时间')}
      width={520}
      onOk={submit}
      confirmLoading={loading}
      open={open}
      onCancel={() => {
        setOpen(false);
      }}
    >
      <Form requiredMark={false} form={form}>
        <DMFormItem
          disableLabelMuted
          label={I18N.t('最近开播时间')}
          name={'liveHours'}
          rules={[
            {
              required: true,
              message: I18N.t('请设置最近开播时间'),
              type: 'number',
            },
          ]}
          initialValue={value ? value / 24 : 7}
        >
          <InputNumber min={1} precision={0} addonAfter={I18N.t('天')} max={30} />
        </DMFormItem>
        <InfoTip
          type={'info'}
          style={{ alignItems: 'flex-start' }}
          message={
            <div style={{ marginTop: -4 }}>
              <Typography.Text style={{ whiteSpace: 'wrap' }}>
                {I18N.t(
                  '最近开播时间晚于上述时间的主播才允许抓取到您的团队，最小值为1天，最大值为30天，请注意，最近天数要求的越小，抓取的主播数量越少',
                )}
              </Typography.Text>
            </div>
          }
        />
      </Form>
    </DMModal>
  );
};

const RegionConfigModal = (props: { initialValues: any; onUpdate: () => void }) => {
  const { initialValues, onUpdate } = props;
  const [open, setOpen] = useState(true);
  const [form] = Form.useForm();
  const [options, setOptions] = useState(() => {
    return META_DATA.find((item) => {
      return item.name === initialValues.area;
    })?.countries;
  });
  const [countries, setCountries] = useState<string[]>(initialValues.countries || []);
  const { run: submit, loading } = useRequest(
    async () => {
      const { area } = await form.validateFields();
      DMConfirm({
        title: I18N.t('确定要更改TikTok所属区域吗？'),
        content: (
          <div style={{ fontSize: 14, display: 'flex', flexDirection: 'column', gap: 12 }}>
            <div>1. {I18N.t('请同步更换抓取主播ID流程所使用的浏览器分身的节点IP地址')}</div>
            <div>2. {I18N.t('请同步更换用来筛选主播达人是否能够被邀约的经纪人账号')}</div>
            <div>3. {I18N.t('请同步更换用来抓取主播ID流程所使用的TikTok账号')}</div>
            <div>
              4.{' '}
              {I18N.t('请注意，一个月只能更改{{count}}次', {
                count: initialValues.regionSwitchQuota,
              })}
            </div>
          </div>
        ),

        width: 540,
        onOk() {
          ghSettingsCountriesPut({ countries: countries.join(','), area }).then(() => {
            message.success(I18N.t('修改成功'));
            setOpen(false);
            onUpdate();
          });
        },
      });
    },
    {
      manual: true,
    },
  );

  return (
    <DMModal
      title={I18N.t('TikTok公会所属区域')}
      open={open}
      width={640}
      bodyStyle={{ paddingBottom: 0 }}
      confirmLoading={loading}
      onOk={submit}
      onCancel={() => {
        setOpen(false);
      }}
    >
      <Alert
        showIcon
        type={'info'}
        message={`${I18N.t('更改公会所属区域，请注意，一个月只能更改')}${
          initialValues.regionSwitchQuota || 0
        }${I18N.t('次')}`}
      />

      <Form form={form} style={{ paddingTop: 16 }}>
        <DMFormItemContext.Provider value={{ disableLabelMuted: true }}>
          <DMFormItem name={'area'} label={I18N.t('所属区域')} initialValue={initialValues.area}>
            <Select
              onChange={(val) => {
                const _list =
                  _.find(META_DATA, (item) => {
                    return item.name === val;
                  })?.countries || [];
                setOptions(_list);
                setCountries(
                  _list.map((item) => {
                    return String(item?.code).toLowerCase();
                  }),
                );
              }}
              options={META_DATA.map((item) => {
                return {
                  label: I18N.isCn() ? item.name : item.code,
                  value: item.name,
                };
              })}
            />
          </DMFormItem>
          <DMFormItem
            label={I18N.t('国家列表')}
            name={'_countries'}
            rules={[
              {
                validator() {
                  if (countries?.length === 0) {
                    return Promise.reject(I18N.t('至少选择一个国家'));
                  }
                  return Promise.resolve();
                },
              },
            ]}
          >
            <div
              style={{
                marginTop: 6,
                height: 200,
                overflow: 'auto',
                border: '1px solid #ddd',
                padding: '4px 8px',
              }}
            >
              <Form.Item shouldUpdate noStyle>
                <Checkbox.Group
                  value={countries}
                  onChange={(val) => {
                    setCountries(val);
                  }}
                >
                  <Space direction={'vertical'}>
                    {options?.map((i) => {
                      return (
                        <Checkbox key={i.code} value={i.code.toLowerCase()}>
                          {I18N.isCn() ? i.name : i.code}
                        </Checkbox>
                      );
                    })}
                  </Space>
                </Checkbox.Group>
              </Form.Item>
            </div>
          </DMFormItem>
        </DMFormItemContext.Provider>
      </Form>
    </DMModal>
  );
};
const CreatorTypeModal = (props: { onUpdate: () => void; value?: string[] }) => {
  const { onUpdate, value } = props;
  const [visible, setVisible] = useState(true);
  const [form] = Form.useForm();
  const { run: submit, loading } = useRequest(
    async () => {
      const values = await form.validateFields();
      await ghSettingsCreatorTypesPut({
        types: values.types.join(','),
      });
      message.success(I18N.t('修改成功'));
      setVisible(false);
      onUpdate();
    },
    {
      manual: true,
    },
  );
  return (
    <DMModal
      title={I18N.t('主播类型')}
      open={visible}
      onOk={submit}
      bodyStyle={{
        paddingBottom: 0,
      }}
      confirmLoading={loading}
      onCancel={() => {
        setVisible(false);
      }}
    >
      <Form requiredMark={false} form={form}>
        <Form.Item
          name={'types'}
          initialValue={value || []}
          label={I18N.t('主播类型')}
          rules={[
            {
              required: true,
              message: I18N.t('至少要选择一种类型的主播'),
              type: 'array',
            },
          ]}
        >
          <Checkbox.Group>
            <Space size={16} direction={'vertical'} style={{ paddingTop: 6 }}>
              <Checkbox value={'游戏主播'}>{I18N.t('抓取游戏主播')}</Checkbox>
              <Checkbox value={'带货主播'}>{I18N.t('抓取带货主播')}</Checkbox>
              <Checkbox value={'其他类型主播'}>{I18N.t('抓取其他类型的主播')}</Checkbox>
            </Space>
          </Checkbox.Group>
        </Form.Item>
      </Form>
    </DMModal>
  );
};
const RegionConfiguration = () => {
  const {
    data: config,
    run,
    loading,
  } = useRequest(() => {
    return ghSettingsKolConfigGet();
  });
  const { run: openRevenueConfigModal } = useRequest(
    async () => {
      if (!config) {
        return;
      }
      GhostModalCaller(
        <RevenueConfigModal value={config?.minRevenue} onUpdate={run} />,
        'RevenueConfigModal',
      );
    },
    {
      manual: true,
    },
  );
  const { run: openRegionConfigModal } = useRequest(
    async () => {
      if (config?.regionSwitchQuota && config?.regionSwitchQuota >= 0) {
        GhostModalCaller(
          <RegionConfigModal
            initialValues={{
              regionSwitchQuota: config?.regionSwitchQuota,
              area: config?.area,
              countries: config?.countries,
            }}
            onUpdate={run}
          />,

          'RegionConfigModal',
        );
      } else {
        DMConfirm({
          type: 'info',
          title: I18N.t('当前不允许切换区域'),
        });
      }
    },
    {
      manual: true,
    },
  );
  const revenueConfig = useMemo(() => {
    if (!config) {
      return false;
    }
    if (config?.minRevenue >= 0) {
      const text = config?.minRevenue?.toLocaleString();
      return (
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: 32 }}>
          <Typography.Text>{text}</Typography.Text>
          <Typography.Link onClick={openRevenueConfigModal}>
            <IconFontIcon iconName={'edit_24'} />
          </Typography.Link>
        </div>
      );
    }
    return <Typography.Link onClick={openRevenueConfigModal}>{I18N.t('未设置')}</Typography.Link>;
  }, [config, openRevenueConfigModal]);
  const { run: openCreatorTypeModal } = useRequest(
    async () => {
      if (!config) {
        return;
      }
      GhostModalCaller(
        <CreatorTypeModal value={config?.creatorTypes} onUpdate={run} />,
        'CreatorTypeModal',
      );
    },
    {
      manual: true,
    },
  );
  const regionConfig = useMemo(() => {
    if (!config) {
      return false;
    }
    if (config?.area) {
      const _target = _.find(META_DATA, (item) => {
        return item.name === config?.area;
      });
      return (
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: 32 }}>
          <Typography.Text>{I18N.isCn() ? _target?.name : _target?.code}</Typography.Text>
          <Typography.Link onClick={openRegionConfigModal}>
            <IconFontIcon iconName={'edit_24'} />
          </Typography.Link>
        </div>
      );
    }
    return <Typography.Link onClick={openRegionConfigModal}>{I18N.t('未设置')}</Typography.Link>;
  }, [config, openRegionConfigModal]);
  const creatorTypeConfig = useMemo(() => {
    if (!config) {
      return false;
    }
    if (config?.creatorTypes?.length && config?.creatorTypes.length < 3) {
      return (
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: 32 }}>
          <Typography.Text>{config?.creatorTypes?.join('，')}</Typography.Text>
          <Typography.Link onClick={openCreatorTypeModal}>
            <IconFontIcon iconName={'edit_24'} />
          </Typography.Link>
        </div>
      );
    }
    return <Typography.Link onClick={openCreatorTypeModal}>{I18N.t('不限')}</Typography.Link>;
  }, [config, openCreatorTypeModal]);
  const countryConfig = useMemo(() => {
    if (config === undefined) {
      return false;
    }
    let node: any = I18N.t('未设置');
    if (config?.countries) {
      node = (
        <Space direction={'vertical'}>
          {config?.countries?.map((item) => {
            return <span key={item}>{getCountryNameByAlpha2Code(item)}</span>;
          })}
        </Space>
      );
    }
    return (
      <div
        style={{
          overflow: 'auto',
          border: '1px solid #ddd',
          padding: '4px 8px',
          marginTop: 6,
          height: 200,
        }}
      >
        {node}
      </div>
    );
  }, [config]);
  return (
    <Card hoverable className={styles.settingCard} loading={loading}>
      <div className="title">{I18N.t('TikTok公会配置')}</div>
      <div style={{ flex: 1, overflow: 'hidden', padding: 24 }}>
        <DMFormItemContext.Provider value={{ labelWidth: 185, disableLabelMuted: true }}>
          <DMFormItem label={I18N.t('最低流水要求')}>
            {!loading ? revenueConfig : <IconFontIcon spin iconName={'loading_24'} />}
          </DMFormItem>
          <DMFormItem label={I18N.t('所属区域')}>
            {!loading ? regionConfig : <IconFontIcon spin iconName={'loading_24'} />}
          </DMFormItem>
          <DMFormItem label={I18N.t('主播类型')}>
            {!loading ? creatorTypeConfig : <IconFontIcon spin iconName={'loading_24'} />}
          </DMFormItem>
          <DMFormItem label={I18N.t('国家列表')} style={{ marginBottom: 0 }}>
            {!loading ? countryConfig : <IconFontIcon spin iconName={'loading_24'} />}
          </DMFormItem>
        </DMFormItemContext.Provider>
      </div>
    </Card>
  );
};
export default RegionConfiguration;

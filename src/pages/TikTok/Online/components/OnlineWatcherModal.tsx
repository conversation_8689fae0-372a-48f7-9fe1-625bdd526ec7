import I18N from '@/i18n';
import { useCallback, useState } from 'react';
import DMModal from '@/components/Common/Modal/DMModal';
import { Checkbox, Form, Input, InputNumber, Radio, Space, Typography } from 'antd';
import HelpTooltip from '@/components/HelpTooltip';
import DMFormItem, { DMFormItemContext } from '@/components/Common/DMFormItem';
import { useRequest } from '@@/plugin-request/request';
import {
  ghSettingsKolConfigGet,
  ghSettingsLoadSettingsGet,
} from '@/services/api-TKGHAPI/GhSettingsController';
import DMConfirm, { DMLoading } from '@/components/Common/DMConfirm';
import { rpaGetRpaFlowByBizCodeGet } from '@/services/api-RPAAPI/RpaController';
import { GhostModalCaller } from '@/mixins/modal';
import SelectRpaDeviceModal from '@/components/Common/SelectRpaDeviceModal';
import { dateFormat, trimValues } from '@/utils/utils';
import {
  rpaTaskCountNotEndByBizCodeGet,
  rpaTaskFindShopsByTagNameGet,
  rpaTaskForceEndByBizCodePut,
  rpaTaskManualTriggerTaskPost,
} from '@/services/api-RPAAPI/RpaTaskController';
import pMinDelay from 'p-min-delay';
import _ from 'lodash';
import IconFontIcon from '@/components/Common/IconFontIcon';
import { ghLiveCreatorMarkAllOfflinePost } from '@/services/api-TKGHAPI/GhLiveCreatorController';
import moment from 'moment';

const CreatorTypes = {
  游戏主播: 'game',
  带货主播: 'commerce',
  其他类型主播: 'other',
};

const InputFormItemWithFetch = (props: {
  prefix: any;
  disabled?: boolean;
  initialValue: string;
}) => {
  const { prefix, disabled, initialValue } = props;
  const { loading, data, run, mutate } = useRequest(
    (tagName: string = initialValue) => {
      if (disabled) {
        return Promise.resolve({
          data: {
            status: '',
            text: '',
          },
        });
      }
      if (!tagName) {
        return Promise.resolve({
          data: {
            status: 'error',
            text: <Typography.Text type={'danger'}>{I18N.t('请输入标签名称')}</Typography.Text>,
          },
        });
      }
      return pMinDelay(
        rpaTaskFindShopsByTagNameGet({
          tagName,
        }),
        500,
      ).then((res) => {
        if (res.data?.length) {
          return {
            data: {
              status: 'success',
              text: (
                <Typography.Text type={'success'}>
                  {I18N.t('共找到')}
                  {res.data?.length.toLocaleString()}
                  {I18N.t('个带有此标签的浏览器分身')}
                </Typography.Text>
              ),
            },
          };
        }
        return {
          data: {
            status: 'error',
            text: (
              <Typography.Text type={'danger'}>
                {I18N.t('未找到任何带有此标签的浏览器分身')}
              </Typography.Text>
            ),
          },
        };
      });
    },
    {
      defaultLoading: false,
      debounceInterval: 300,
    },
  );
  return (
    <DMFormItem
      validateFirst
      rules={[
        {
          validator: async (rule, value) => {
            if (disabled) {
              return Promise.resolve();
            }
            if (!loading && data?.status === 'success') {
              return Promise.resolve();
            }
            if (loading || data?.status === 'loading') {
              return Promise.reject();
            }
            await run(_.trim(value));
            return Promise.reject();
          },
        },
      ]}
      name={[prefix, 'shop_tag_name']}
      validateStatus={loading ? 'validating' : data?.status}
      help={
        loading && !disabled ? (
          <Space>
            <Typography.Link>
              <IconFontIcon iconName={'loading_24'} />
            </Typography.Link>
            <span>{I18N.t('正在查找带有此标签的浏览器分身')}</span>
          </Space>
        ) : (
          data?.text
        )
      }
      label={I18N.t('使用的分身标签')}
      initialValue={initialValue}
      validateTrigger={['onBlur', 'onSubmit']}
    >
      <Input
        onChange={() => {
          mutate(() => {
            return {
              data: {
                status: 'loading',
              },
            };
          });
        }}
        onBlur={(e) => {
          run(_.trim(e.target.value));
        }}
        disabled={disabled}
      />
    </DMFormItem>
  );
};

const OnlineWatcherModal = (props: {
  onSubmit: (data: {
    square: {
      enabled?: boolean;
      duration_hours?: number;
      filter_exists_handle?: boolean;
      [key: string]: any;
    };
    favor: {
      enabled?: boolean;
      duration_hours?: number;
      [key: string]: any;
    };
  }) => Promise<void>;
}) => {
  const { onSubmit } = props;
  const [visible, setVisible] = useState(true);
  const [squareChecked, setSquareChecked] = useState(true);
  const [favorChecked, setFavorChecked] = useState(true);
  const [form] = Form.useForm();
  const { data: creatorTypes } = useRequest(
    () => {
      return ghSettingsKolConfigGet().then((res) => {
        return {
          data: _.orderBy(
            res.data?.creatorTypes?.map((item) => {
              return {
                label: item,
                value: CreatorTypes[item],
              };
            }),
            (i) => {
              return ['game', 'commerce', 'other'].indexOf(i.value);
            },
            'desc',
          ),
        };
      });
    },
    {
      onSuccess(data) {
        form.setFieldValue(
          ['square', 'creator_types'],
          data?.map((item) => {
            return item.value;
          }),
        );
      },
    },
  );
  const { run: submit, loading } = useRequest(
    async () => {
      const { square, favor, duration_hours, tag_name } = trimValues(await form.validateFields());
      console.log({
        square,
        favor,
      });
      const data: any = {
        favor: {
          ...favor,
          enabled: favorChecked,
          duration_hours,
          tag_name,
        },
        square: {
          ...square,
          enabled: squareChecked,
          duration_hours,
          tag_name,
        },
      };
      await pMinDelay(onSubmit(data), 500);
      setVisible(false);
    },
    {
      manual: true,
    },
  );
  return (
    <DMModal
      open={visible}
      onOk={submit}
      width={720}
      bodyStyle={{ paddingBottom: 0 }}
      okButtonProps={{
        disabled: !squareChecked && !favorChecked,
      }}
      confirmLoading={loading}
      onCancel={() => {
        setVisible(false);
      }}
      headless
    >
      <DMFormItemContext.Provider value={{ disableLabelMuted: true }}>
        <Form requiredMark={false} form={form}>
          <div style={{ paddingTop: 12, paddingBottom: 12 }}>
            <Checkbox
              checked={squareChecked}
              onChange={(e) => {
                setSquareChecked(e.target.checked);
              }}
            >
              <Space>
                <span>{I18N.t('运行“直播广场主播在线守候流程“')}</span>
                <Typography.Link>
                  <HelpTooltip title={I18N.t('到直播广场中抓取正在直播的主播')} />
                </Typography.Link>
              </Space>
            </Checkbox>
          </div>
          <div style={{ paddingLeft: 24 }}>
            <DMFormItem label={I18N.t('直播间流水最低')} name={'_hack_one'}>
              <Space>
                <Form.Item name={['square', 'min_revenue']} initialValue={10} noStyle>
                  <InputNumber
                    disabled={!squareChecked}
                    onBlur={(e) => {
                      if (!e.target.value) {
                        form.setFieldValue(['square', 'min_revenue'], 0);
                      }
                    }}
                    precision={0}
                    min={0}
                  />
                </Form.Item>
                <Typography.Text type={'secondary'}>
                  {I18N.t('（低于此值的主播将被忽略）')}
                </Typography.Text>
              </Space>
            </DMFormItem>
            <DMFormItem
              name={['square', 'creator_types']}
              rules={[
                {
                  required: squareChecked,
                  message: I18N.t('请指定主播类型'),
                },
              ]}
              initialValue={[]}
              label={I18N.t('主播类型')}
            >
              <Checkbox.Group disabled={!squareChecked}>
                {creatorTypes?.map((item) => {
                  return (
                    <Checkbox key={item.value} value={item.value}>
                      {item.label}
                    </Checkbox>
                  );
                })}
              </Checkbox.Group>
            </DMFormItem>
            <DMFormItem
              name={['square', 'invite_types']}
              rules={[
                {
                  required: squareChecked,
                  message: I18N.t('请指定邀约类型'),
                },
              ]}
              initialValue={['normal', 'elite']}
              label={I18N.t('邀约类型')}
            >
              <Checkbox.Group disabled={!squareChecked}>
                <Checkbox value="normal">{I18N.t('普通邀约')}</Checkbox>
                <Checkbox value="elite">{I18N.t('金牌邀约')}</Checkbox>
              </Checkbox.Group>
            </DMFormItem>
            <Space align={'start'} wrap={false}>
              <DMFormItem
                initialValue={500}
                name={['square', 'creator_sum']}
                label={I18N.t('守候的主播数量')}
              >
                <InputNumber
                  disabled={!squareChecked}
                  onBlur={(e) => {
                    if (!e.target.value) {
                      form.setFieldValue(['square', 'creator_sum'], 1);
                    }
                  }}
                  precision={0}
                  min={1}
                  max={1000}
                />
              </DMFormItem>
              <span style={{ lineHeight: '32px' }}>{I18N.t('最多1000')}</span>
            </Space>
            <DMFormItem
              name={['square', 'filter_exists_handle']}
              valuePropName="checked"
              initialValue={false}
            >
              <Checkbox>{I18N.t('忽略当前团队已有的主播')}</Checkbox>
            </DMFormItem>

            <InputFormItemWithFetch
              prefix={'square'}
              initialValue={I18N.t('直播广场在线守候')}
              disabled={!squareChecked}
            />
          </div>

          <div style={{ paddingTop: 12, paddingBottom: 12 }}>
            <Checkbox
              checked={favorChecked}
              onChange={(e) => {
                setFavorChecked(e.target.checked);
              }}
            >
              <Space>
                <span>{I18N.t('运行“团队已有主播在线守候流程”')}</span>
                <Typography.Link>
                  <HelpTooltip title={I18N.t('查看当前团队已有主播是否在线开播')} />
                </Typography.Link>
              </Space>
            </Checkbox>
          </div>
          <div style={{ paddingLeft: 24 }}>
            <DMFormItem
              label={I18N.t('主播来源')}
              name={['favor', 'all_creator']}
              initialValue={true}
            >
              <Radio.Group>
                <Radio value={true}>{I18N.t('团队已抓取到的“公海”中的主播达人')}</Radio>
                <Radio value={false}>{I18N.t('团队关注的主播达人')}</Radio>
              </Radio.Group>
            </DMFormItem>
            <DMFormItem label={I18N.t('直播间流水最低')} name={'_hack_two'}>
              <Space>
                <Form.Item name={['favor', 'min_revenue']} initialValue={10} noStyle>
                  <InputNumber
                    disabled={!favorChecked}
                    precision={0}
                    min={0}
                    onBlur={(e) => {
                      if (!e.target.value) {
                        form.setFieldValue(['favor', 'min_revenue'], 0);
                      }
                    }}
                  />
                </Form.Item>
                <Typography.Text type={'secondary'}>
                  {I18N.t('（低于此值的主播将被忽略）')}
                </Typography.Text>
              </Space>
            </DMFormItem>
            <Space wrap={false} align={'start'}>
              <DMFormItem
                initialValue={500}
                name={['favor', 'creator_sum']}
                label={I18N.t('守候的主播数量')}
              >
                <InputNumber
                  disabled={!favorChecked}
                  onBlur={(e) => {
                    if (!e.target.value) {
                      form.setFieldValue(['favor', 'creator_sum'], 1);
                    }
                  }}
                  precision={0}
                  min={1}
                  max={1000}
                />
              </DMFormItem>
              <span style={{ lineHeight: '32px' }}>{I18N.t('最多1000')}</span>
            </Space>
            <InputFormItemWithFetch
              prefix={'favor'}
              initialValue={I18N.t('团队关注在线守候')}
              disabled={!favorChecked}
            />
          </div>
          <Space align={'center'} wrap={false} style={{ marginBottom: 24 }}>
            <DMFormItem
              label={I18N.t('运行时长')}
              name={'duration_hours'}
              initialValue={5}
              style={{ marginBottom: 0 }}
            >
              <InputNumber
                onBlur={(e) => {
                  if (!e.target.value) {
                    form.setFieldValue('duration_hours', 1);
                  }
                }}
                min={1}
                precision={0}
                max={20}
              />
            </DMFormItem>
            <span>{I18N.t('个小时后自动结束此流程')}</span>
            <Typography.Link>
              <HelpTooltip
                title={I18N.t(
                  '为避免账号被封禁，运行一段时间后需要停止流程，如有需要，再次重启即可',
                )}
              />
            </Typography.Link>
          </Space>
          <DMFormItem label={I18N.t('给主播打上标签')}>
            <Space>
              <DMFormItem name="tag_name" noStyle initialValue={`${moment().format('MMDD')}.live`}>
                <Input allowClear />
              </DMFormItem>
              <Typography.Link>
                <HelpTooltip
                  title={I18N.t(
                    '抓取到的正在开播的主播会自动进入到团队的公海达人，给这些主播打上标签方便您事后筛选',
                  )}
                />
              </Typography.Link>
            </Space>
          </DMFormItem>
        </Form>
      </DMFormItemContext.Provider>
    </DMModal>
  );
};
export default OnlineWatcherModal;
export function useWatcherStartRequest(onUpdate: () => void) {
  const getCount = useWatcherCountCallback();
  const getEnabled = useOnlineWatcherEnabledCallback();
  const return_options = useRequest(
    async () => {
      if (return_options.loading) {
        return;
      }
      const loading = DMLoading({
        title: I18N.t('正在获取自动化流程配置...'),
      });
      const enabled = await getEnabled();
      if (!enabled) {
        loading.destroy();
        DMConfirm({
          type: 'info',
          width: 540,
          title: I18N.t('当前团队未开启此功能'),
          content: I18N.t('请和在线客服联系，确认开启了正在开播的主播达人等相关功能'),
        });
        return;
      }
      const { total } = await getCount();
      if (total > 0) {
        loading.destroy();
        DMConfirm({
          type: 'info',
          width: 540,
          title: I18N.t('当前已经开启了主播在线守候流程'),
          content: I18N.t('同时只能运行一份主播在线守候流程'),
        });
        return;
      }
      loading.destroy();
      GhostModalCaller(
        <OnlineWatcherModal
          onSubmit={async (values) => {
            const tasks: { flow: API.RpaFlowVo; params: any }[] = [];
            if (values.square.enabled) {
              await rpaGetRpaFlowByBizCodeGet({
                bizCode: 'tiktok.live.watcher.square',
              }).then((res) => {
                const flow = res.data!;
                if (!flow) {
                  throw new Error(I18N.t('找不到＂直播广场主播在线守候流程＂'));
                }
                tasks.push({ flow, params: values.square });
              });
            }
            if (values.favor.enabled) {
              await rpaGetRpaFlowByBizCodeGet({
                bizCode: 'tiktok.live.watcher.favor',
              }).then((res) => {
                const flow = res.data!;
                if (!flow) {
                  throw new Error(I18N.t('找不到＂团队关注主播在线守候流程＂'));
                }
                tasks.push({ flow, params: values.favor });
              });
            }
            const devices = await ghSettingsLoadSettingsGet({
              ghPlatformType: 'TikTok',
            }).then((res) => {
              return res.data?.devices || [];
            });
            if (!devices.length) {
              DMConfirm({
                type: 'info',
                content: I18N.t('请前往系统设置绑定RPA执行设备'),
                title: I18N.t('团队还没有绑定设备'),
              });
              return;
            }
            const _submit = async (device: API.RpaRunDeviceVo) => {
              const list: any[] = [];
              const concurrent = device.cpus! > 4 ? 5 : 2;
              const deviceId = device.deviceId;
              for (let i = 0; i < tasks.length; i++) {
                const { flow, params } = tasks[i];
                list.push(
                  await rpaTaskManualTriggerTaskPost({
                    rpaFlowId: flow.id!,
                    shopIds: [],
                    name: `${flow.name}_${dateFormat(new Date(), 'YYYYMMDDHHmmss')}`,
                    forceRecord: false,
                    params: {
                      0: params,
                    },
                    snapshot: 'Not',
                    concurrent,
                    deviceId,
                  }),
                );
              }
              DMConfirm({
                type: 'info',
                width: 540,
                title: `${I18N.t('已经在您指定的设备”')}${device.hostName}${I18N.t(
                  '“中开启相关流程',
                )}`,
                content: I18N.t(
                  '请注意，一个团队只能开启一个在线守候流程，当流程结束后，开播的主播将不再实时监管',
                ),
              });
              onUpdate();
            };
            if (devices.length === 1) {
              const device = devices[0];
              await _submit({ ...device, deviceId: device.clientId! });
            } else {
              return new Promise<void>(async (resolve, reject) => {
                GhostModalCaller(
                  <SelectRpaDeviceModal
                    gh
                    onCancel={() => {
                      reject();
                    }}
                    onSubmit={async (data) => {
                      await _submit(data.rows[0]);
                      resolve();
                    }}
                  />,
                );
              });
            }
          }}
        />,
      );
    },
    {
      manual: true,
    },
  );
  return return_options;
}

export function useWatcherStopRequest(onUpdate: () => void) {
  const { run } = useWatcherStartRequest(onUpdate);
  const getCount = useWatcherCountCallback();
  return useRequest(
    async () => {
      const status = await getCount();
      if (status.total) {
        DMConfirm({
          title: I18N.t('确定要停止主播在线守候流程吗？'),
          width: 540,
          content: I18N.t('系统会停止相关流程，关闭相应的浏览器窗口'),
          onOk: () => {
            const list: any[] = [];
            Object.keys(status).forEach((bizCode) => {
              if (bizCode !== 'total') {
                list.push(
                  rpaTaskForceEndByBizCodePut({
                    bizCode,
                    stopPostRun: true,
                  }),
                );
              }
            });
            Promise.all(list).then(() => {
              ghLiveCreatorMarkAllOfflinePost().then(onUpdate);
            });
          },
        });
      } else {
        DMConfirm({
          iconType: 'info',
          title: I18N.t('您还没有开启主播守候任务'),
          width: 540,
          okText: I18N.t('立即开启'),
          onOk: () => {
            run();
          },
        });
      }
    },
    {
      manual: true,
    },
  );
}
export function useWatcherCountCallback() {
  return useCallback(async () => {
    const status = {
      'tiktok.live.watcher.square': 0,
      'tiktok.live.watcher.favor': 0,
      total: 0,
    };
    for (const bizCode in status) {
      if (bizCode !== 'total') {
        await rpaTaskCountNotEndByBizCodeGet({
          bizCode,
        }).then((res) => {
          status[bizCode] = res.data || 0;
          status.total += res.data || 0;
        });
      }
    }

    return status;
  }, []);
}
export function useOnlineWatcherRunning() {
  const getCount = useWatcherCountCallback();
  return useRequest(
    async () => {
      const { total } = await getCount();
      return {
        data: total > 0,
      };
    },
    {
      manual: true,
    },
  );
}
export function useOnlineWatcherEnabledCallback() {
  return useCallback(async () => {
    const status = {
      'tiktok.live.watcher.square': 0,
      'tiktok.live.watcher.favor': 0,
    };
    let enabled = false;
    for (const bizCode in status) {
      enabled = await rpaGetRpaFlowByBizCodeGet({
        bizCode,
      }).then((res) => {
        return !!res.data;
      });
      if (!enabled) {
        break;
      }
    }
    return enabled;
  }, []);
}
export function useOnlineWatcherEnabled() {
  const getEnabled = useOnlineWatcherEnabledCallback();
  return useRequest(async () => {
    return {
      data: await getEnabled(),
    };
  });
}

import I18N from '@/i18n';
import classNames from 'classnames';
import styled from 'styled-components';
import { Button, ConfigProvider, Empty, Layout, Space, Tooltip, Typography } from 'antd';
import type { ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { scrollProTableOptionFn } from '@/mixins/table';
import { useCallback, useMemo, useRef, useState } from 'react';
import IconFontIcon from '@/components/Common/IconFontIcon';
import { useVT } from 'virtualizedtableforantd4';
import { GlobalHeaderAction } from '@/utils/pageUtils';
import {
  ghLiveCreatorIsWatchingLiveRewardsGet,
  ghLiveCreatorPageGet,
  ghLiveCreatorStopWatchLiveRewardsDelete,
} from '@/services/api-TKGHAPI/GhLiveCreatorController';
import useOnlineCreatorColumns from '@/pages/TikTok/Online/components/getColumns';
import {
  useOnlineWatcherRunning,
  useWatcherStartRequest,
  useWatcherStopRequest,
} from '@/pages/TikTok/Online/components/OnlineWatcherModal';
import ColoursIcon from '@/components/Common/ColoursIcon';
import { StyledOverflow } from '@/components/Common/MoreDropdown';
import {
  ghCreatorAllocatePut,
  ghCreatorCancelAllocatePut,
} from '@/services/api-TKGHAPI/GhCreatorController';
import { getSearchFieldsSize } from '@/pages/TikTok/utils/utils';
import ModuleWrapper from '@/components/Common/ModuleWrapper';
import { AllocateButton, StatusModifyBtn } from '@/pages/TikTok/components/common';
import { GhostModalCaller } from '@/mixins/modal';
import LiveRoomDiamonds from '@/pages/TikTok/Online/components/LiveRoomDiamonds';
import DMConfirm from '@/components/Common/DMConfirm';
import { useRequest } from '@@/plugin-request/request';
import FunctionButton from '@/components/Common/FunctionButton';
import Functions from '@/constants/Functions';

const StyledContent = styled(Layout.Content)`
  main {
    flex: 1;
    height: 100%;
    overflow: hidden;
  }
  header {
    form {
      display: flex;
      flex: 1;
      gap: 8px;
      justify-content: flex-end;
    }
  }
`;
let timer;
const OnlineList = () => {
  const actionRef = useRef<ActionType>();
  const [selectedVos, changeSelectedVos] = useState<API.GhLiveCreatorDetailVo[]>([]);
  const { data: running, run: fetch_2 } = useOnlineWatcherRunning();
  const { data: rewarding, run: fetch_3 } = useRequest(() => {
    return ghLiveCreatorIsWatchingLiveRewardsGet();
  });
  const loaded = useRef(false);
  const [vt] = useVT(() => ({ scroll: { y: '100%' } }), []);

  const { run: fetchStatus } = useRequest(async () => {
    fetch_3();
    fetch_2();
  });
  const _onUpdate = useCallback(
    (reset?: boolean) => {
      if (reset) {
        actionRef.current?.reloadAndRest?.();
        fetchStatus();
        changeSelectedVos([]);
      } else {
        actionRef.current?.reload();
        fetchStatus();
      }
    },
    [fetchStatus],
  );
  const { tableProps, header, getSearchParams, getSortParams } = useOnlineCreatorColumns(
    _onUpdate,
    'store',
  );
  const selected = useMemo(() => {
    if (selectedVos?.length) {
      return selectedVos.map((item) => item.id!);
    }
    return [];
  }, [selectedVos]);

  const { run: stopTask } = useWatcherStopRequest(() => {
    _onUpdate();
  });

  const { run: openWatcherModal, loading: opening } = useWatcherStartRequest(() => {
    _onUpdate();
  });

  return (
    <ModuleWrapper code={'util'}>
      <StyledContent>
        <GlobalHeaderAction.Emit>
          <Space size={32}>
            <Button
              ghost
              style={{ background: 'none' }}
              icon={<IconFontIcon iconName={'shuaxin_24'} />}
              onClick={() => {
                _onUpdate();
              }}
            >
              {I18N.t('刷新')}
            </Button>
            <FunctionButton
              code={Functions.KOL_MANAGE}
              ghost
              style={{ background: 'none' }}
              onClick={() => {
                if (running) {
                  stopTask();
                } else {
                  openWatcherModal();
                }
              }}
            >
              <Space>
                {running ? (
                  <Typography.Text type={'danger'}>
                    <IconFontIcon iconName={'qiangzhijieshu_24'} />
                  </Typography.Text>
                ) : (
                  <IconFontIcon iconName={'pangguan_24'} />
                )}

                <span>{running ? I18N.t('停止在线守候流程') : I18N.t('开启在线守候流程')}</span>
              </Space>
            </FunctionButton>
            <FunctionButton
              code={Functions.KOL_MANAGE}
              ghost
              style={{ background: 'none' }}
              onClick={() => {
                if (running) {
                  if (rewarding) {
                    DMConfirm({
                      title: I18N.t('确定停止直播间打赏吗？'),
                      onOk() {
                        ghLiveCreatorStopWatchLiveRewardsDelete().then(fetchStatus);
                      },
                    });
                  } else {
                    GhostModalCaller(<LiveRoomDiamonds onUpdate={fetchStatus} />);
                  }
                } else {
                  DMConfirm({
                    width: 520,
                    type: 'info',
                    title: I18N.t('无法开启直播间打赏流程'),
                    content: I18N.t('您需要先开启直播间在线守候流程，才能够开启直播间打赏流程'),
                  });
                }
              }}
            >
              <Space>
                {rewarding ? (
                  <Typography.Text type={'danger'}>
                    <IconFontIcon iconName={'tuichu_24'} />
                  </Typography.Text>
                ) : (
                  <IconFontIcon iconName={'zuanshi_24'} />
                )}
                <span>{rewarding ? I18N.t('停止直播间打赏') : I18N.t('开启直播间打赏')}</span>
              </Space>
            </FunctionButton>
          </Space>
        </GlobalHeaderAction.Emit>
        <header className={classNames('header')}>
          <span />
          {header}
        </header>
        <main>
          <ConfigProvider
            renderEmpty={() => {
              if (loaded.current) {
                const size = getSearchFieldsSize(getSearchParams(), 'store');
                if (size !== 0) {
                  return <Empty description={I18N.t('当前搜索条件未查询到达人')} />;
                }
                if (running) {
                  return (
                    <div>
                      <div style={{ display: 'inline-flex', gap: 16, width: 640 }}>
                        <span style={{ flex: '0 0 72px' }}>
                          <Typography.Link>
                            <IconFontIcon size={72} spin iconName={'loading_24'} />
                          </Typography.Link>
                        </span>
                        <div
                          style={{
                            display: 'inline-flex',
                            flexDirection: 'column',
                            gap: 16,
                            textAlign: 'left',
                          }}
                        >
                          <Typography.Text>{I18N.t('正在等待流程汇报开播的主播')}</Typography.Text>
                          <Typography.Text>
                            {I18N.t('请稍候，流程正在抓取符合您要求的且正在开播的主播')}{' '}
                          </Typography.Text>
                        </div>
                      </div>
                    </div>
                  );
                }
                return (
                  <div>
                    <div style={{ display: 'inline-flex', gap: 16, width: 640 }}>
                      <span style={{ flex: '0 0 72px' }}>
                        <ColoursIcon size={72} className={'maikefeng_24'} />
                      </span>
                      <div
                        style={{
                          display: 'inline-flex',
                          flexDirection: 'column',
                          gap: 16,
                          textAlign: 'left',
                        }}
                      >
                        <Typography.Text>{I18N.t('正在开播的主播达人')}</Typography.Text>
                        <Typography.Text type={'secondary'}>
                          {I18N.t(
                            '您可以通过开启“主播在线守候”的相关流程，抓取当前正在直播的主播达人，并进入其直播间与其互动。',
                          )}
                        </Typography.Text>
                        <div>
                          <FunctionButton
                            code={Functions.KOL_MANAGE}
                            loading={opening}
                            onClick={() => {
                              openWatcherModal();
                            }}
                            type={'primary'}
                          >
                            {I18N.t('立即开启')}
                          </FunctionButton>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              }
              return false;
            }}
          >
            <ProTable<API.GhLiveCreatorDetailVo>
              components={{
                ...vt,
                header: tableProps.header,
              }}
              actionRef={actionRef}
              rowSelection={{
                selectedRowKeys: selected,
                onChange(keys, creators) {
                  changeSelectedVos(creators);
                },
              }}
              request={async (params) => {
                loaded.current = false;
                const { pageSize, current, ...rest } = params;
                return ghLiveCreatorPageGet({
                  pageSize,
                  pageNum: current,
                  ...rest,
                  ...getSearchParams(),
                  ...getSortParams(),
                }).then((res) => {
                  loaded.current = true;
                  clearTimeout(timer);
                  timer = setTimeout(() => {
                    _onUpdate();
                  }, 60 * 1000);
                  return {
                    data: res.data?.list,
                    total: res.data?.total,
                  };
                });
              }}
              columns={tableProps.columns}
              {...scrollProTableOptionFn({
                alwaysShowFooter: true,
                footer: () => {
                  // 分配
                  const ids = selectedVos?.map((item) => {
                    return item.creator?.id!;
                  });
                  const disabled = ids.length === 0;
                  const nodes = [
                    {
                      key: 'allocate',
                      node: (_props) => {
                        return (
                          <AllocateButton
                            bizScene={'LiveCreator'}
                            ghPlatformType={'TikTok'}
                            disabled={disabled}
                            onSubmit={async (action, { responsibleUserIds, tag }) => {
                              if (action === 'cancel') {
                                await ghCreatorCancelAllocatePut({
                                  ids,
                                });
                              } else {
                                await ghCreatorAllocatePut({
                                  responsibleUserId: responsibleUserIds[0],
                                  ids,
                                  tag,
                                });
                              }
                              _onUpdate();
                            }}
                            {..._props}
                          >
                            {I18N.t('分配用户')}
                          </AllocateButton>
                        );
                      },
                    },
                    {
                      key: 'status',
                      node: (_props) => {
                        return (
                          <StatusModifyBtn
                            ids={ids}
                            disabled={disabled}
                            ghPlatformType={'TikTok'}
                            bizScene={'LiveCreator'}
                            onUpdate={() => {
                              _onUpdate();
                            }}
                            {..._props}
                          />
                        );
                      },
                    },
                  ];

                  return (
                    <Tooltip
                      placement={'topLeft'}
                      title={disabled ? I18N.t('请选择主播') : undefined}
                    >
                      <StyledOverflow data={nodes} disabled={disabled} />
                    </Tooltip>
                  );
                },
                scroll: {
                  x: tableProps.width,
                },
              })}
            />
          </ConfigProvider>
        </main>
      </StyledContent>
    </ModuleWrapper>
  );
};
export default OnlineList;

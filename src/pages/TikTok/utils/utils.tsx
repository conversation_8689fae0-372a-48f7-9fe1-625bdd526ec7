import I18N from '@/i18n';
import { createSlot } from '@/hooks/createSlot';
import IconFontIcon from '@/components/Common/IconFontIcon';
import { useMemo } from 'react';
import navStyles from '@/style/nav.less';
import type { TikTokCreatorRefer } from '@/pages/TikTok/utils/types';
import { dateFormat, getTeamIdFromUrl, trimValues } from '@/utils/utils';
import { history } from 'umi';
import styled from 'styled-components';
import { Tag } from 'antd';
import { GhCreatorOptions } from '@/pages/TikTok/components/GhCreatorStatusFormItem';
import _ from 'lodash';
import { shopMobileGetSimpleInfoGet } from '@/services/api-ShopAPI/TeamMobileGroupController';
import DMConfirm from '@/components/Common/DMConfirm';
import { rpaTaskManualTriggerTaskPost } from '@/services/api-RPAAPI/RpaTaskController';

export const CreatorBreadCrumb = (props: { params: any }) => {
  const { params } = props;
  const refer = params?.refer as TikTokCreatorRefer;
  const id = params?.id as any;
  const title = useMemo(() => {
    return I18N.t('主播达人');
  }, []);

  return (
    <div className={navStyles.navs}>
      <div
        className={'nav-item'}
        onClick={() => {
          history.push(`/team/${getTeamIdFromUrl()}/tiktok/${refer}/live`);
        }}
      >
        {!!id && (
          <div className="link" title={title}>
            <span className={'text'}>{title}</span>
          </div>
        )}
        {!!id && (
          <span className="arrow">
            <IconFontIcon iconName="angle-right_24" size={12} />
          </span>
        )}
      </div>
      {!!id && (
        <div className={'nav-item'}>
          <div className="link" title={I18N.t('详情')}>
            <span className={'text'}>{I18N.t('详情')}</span>
          </div>
        </div>
      )}
    </div>
  );
};
export const CreatorHeaderSlot = createSlot();

export function getCreatorAvatar(data: API.GhCreatorDetailVo) {
  return data?.avatar;
}

const TagStyled = styled(Tag)`
  && {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 22px !important;
    margin: 0 !important;
    font-size: 14px;
    text-align: center;
    border: none;
  }
`;
export function getCreatorEliteStatus(data?: API.GhCreatorDetailVo) {
  if (!data) {
    return <TagStyled color={'default'}>{I18N.t('未知')}</TagStyled>;
  }
  const { elite } = data;
  if (elite === null || elite === undefined) {
    return <TagStyled color={'default'}>{I18N.t('未知')}</TagStyled>;
  }
  return elite ? (
    <TagStyled color={'#FF9900'}>{I18N.t('金牌邀约')}</TagStyled>
  ) : (
    <TagStyled color={'#009E0F'}>{I18N.t('普通邀约')}</TagStyled>
  );
}
export function getCreatorStatus(data?: API.GhCreatorDetailVo) {
  if (!data) {
    return <TagStyled color={'default'}>{I18N.t('未知')}</TagStyled>;
  }
  const { status } = data;
  const _item = GhCreatorOptions.find((item) => item.value === status);
  if (_item) {
    return <TagStyled color={_item.color}>{_item.label}</TagStyled>;
  }
  return <TagStyled color={'default'}>{status}</TagStyled>;
}
export function formatNumber(num: number) {
  if (num >= 1e9) {
    return (num / 1e9).toFixed(2) + 'B'; // 十亿
  } else if (num >= 1e6) {
    return (num / 1e6).toFixed(2) + 'M'; // 百万
  } else if (num >= 1e3) {
    return (num / 1e3).toFixed(2) + 'K'; // 千
  } else {
    return num?.toLocaleString(); // 小于千的数字直接返回
  }
}
export const Common_Filter_Ignore_keys = [
  'tagLc',
  'limit',
  'sortField',
  'general',
  'anchor',
  'sortOrder',
  'containsTag',
  'deleting',
];

export function transformFilter(vals: Record<any, any>) {
  const values: any = trimValues(vals);
  const { importTimeSearchMode, lastSyncTimeSearchMode, createTimeSearchMode, ...rest } = values;
  if (importTimeSearchMode) {
    if (importTimeSearchMode === 'range') {
      delete rest.importTimeLastDays;
    } else {
      delete rest.importTimeFrom;
      delete rest.importTimeTo;
    }
  }
  if (createTimeSearchMode) {
    if (createTimeSearchMode === 'range') {
      delete rest.createTimeLastDays;
    } else {
      delete rest.createTimeFrom;
      delete rest.createTimeTo;
    }
  }
  if (lastSyncTimeSearchMode) {
    if (lastSyncTimeSearchMode === 'range') {
      delete rest.lastSyncTimeLastDays;
    } else {
      delete rest.lastSyncTimeFrom;
      delete rest.lastSyncTimeTo;
    }
  }

  return rest;
}
export function getSearchFieldsSize(
  vals: any,
  refer: TikTokCreatorRefer | API.GhJobPlanVo['planType'],
) {
  const values: any = transformFilter(_.cloneDeep(trimValues(vals)));
  const ignore_keys = [...Common_Filter_Ignore_keys];
  if (refer === 'mine' || refer === 'User') {
    ignore_keys.push(...['responsibleUserId', 'filterByFavorite']);
  } else if (refer === 'sent') {
    ignore_keys.push('statusList');
  } else if (refer === 'favor') {
    ignore_keys.push('filterByFavorite');
  }
  const size = _.size(
    _.omitBy(values, (value, key) => {
      if (ignore_keys.includes(key)) {
        return true;
      }
      if (key === 'filterByFavorite' && value === false) {
        return true;
      }
      if (key === 'hasNoRegion' && value === false) {
        return true;
      }
      return _.isNil(value) || value === '' || (_.isArray(value) && value.length === 0);
    }),
  );
  return size;
}

export async function manualTriggerMobileTask(options: {
  flow: API.RpaFlowVo;
  target: API.MobileAccountVo[];
  transform?: (
    options: Record<string, { accounts: string[]; [key: string]: any }>,
  ) => Record<string, any>;
}) {
  const { flow, target, transform } = options;
  const list: any[] = [];
  const mobiles_offline = [];
  const mobiles_running = [];
  const mobileIds = Object.keys(_.groupBy(target, 'mobileId'));
  for (let i = 0; i < mobileIds.length; i++) {
    const mobile = await shopMobileGetSimpleInfoGet({
      id: mobileIds[i] as any,
    }).then((res) => {
      return res.data!;
    });
    if (!!mobile.rpaTaskId) {
      mobiles_running.push(mobile);
    }
    if (mobile.status === 'OFFLINE') {
      mobiles_offline.push(mobile);
    }
  }
  if (mobiles_offline.length) {
    DMConfirm({
      type: 'info',
      title: I18N.t('您选择的手机已离线'),
      content: `${I18N.t('手机')}${mobiles_offline
        .map((i) => {
          return i.name;
        })
        .join('、')}${I18N.t('已离线，请确保该手机已正常连接至花漾客户端')}`,
      width: 540,
    });
    throw new Error(I18N.t('您选择的手机已离线'));
  }
  if (mobiles_running.length) {
    DMConfirm({
      type: 'info',
      title: I18N.t('您选择的手机正在执行其它流程'),
      content: `${I18N.t('手机')}${mobiles_running
        .map((i) => {
          return i.name;
        })
        .join('、')}${I18N.t(
        `${I18N.t(
          '正在执行其它流程，这会导致检测账号可用性流程执行失败，请确保在该手机中结束其它所有流程后再重新执行＂',
        )}${flow.name}＂`,
      )}`,
      width: 540,
    });
    throw new Error(I18N.t('您选择的手机正在执行其它流程'));
  }

  Object.entries(_.groupBy(target, 'deviceId')).forEach(([deviceId, arr], index) => {
    const params: Record<number, any> = {};
    arr.forEach((account) => {
      if (!params[account.mobileId!]) {
        params[account.mobileId!] = {
          accounts: [],
        };
      }
      params[account.mobileId!].accounts.push(account.username);
    });
    list.push(
      rpaTaskManualTriggerTaskPost({
        deviceId,
        shopIds: Object.keys(params) as unknown as number[],
        rpaFlowId: flow.id!,
        params: transform?.(params) || params,
        name: `${flow.name}_${dateFormat(new Date(), 'YYYYMMDDHHmmss')}_${index}`,
        forceRecord: false,
        snapshot: 'Not',
        concurrent: 20,
      }),
    );
  });
  await Promise.all(list);
}

<?xml version="1.0" encoding="UTF-8"?>
<svg width="164px" height="80px" preserveAspectRatio="none" viewBox="0 0 164 80" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>形状结合</title>
    <defs>
        <linearGradient x1="1.00200093%" y1="38.3407498%" x2="100%" y2="61.89768%" id="linearGradient-1">
            <stop stop-color="#2F314C" offset="0%"></stop>
            <stop stop-color="#5E688E" offset="100%"></stop>
        </linearGradient>
        <path d="M5,0 L159,0 C161.761424,-5.07265313e-16 164,2.23857625 164,5 L164,80 L164,80 L0,80 L0,5 C-3.38176876e-16,2.23857625 2.23857625,5.07265313e-16 5,0 Z" id="path-2"></path>
        <linearGradient x1="8.49399567%" y1="21.2750435%" x2="100%" y2="86.3073826%" id="linearGradient-4">
            <stop stop-color="#959DD6" offset="0.0999610741%"></stop>
            <stop stop-color="#FFFFFF" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="01-价格备份" transform="translate(-1422.000000, -88.000000)">
            <g id="编组-5" transform="translate(260.000000, 88.000000)">
                <g id="形状结合" transform="translate(1162.000000, 0.000000)">
                    <mask id="mask-3" fill="white">
                        <use xlink:href="#path-2"></use>
                    </mask>
                    <use id="蒙版" fill="url(#linearGradient-1)" xlink:href="#path-2"></use>
                    <path d="M134,28 C151.673112,28 166,42.326888 166,60 C166,77.673112 151.673112,92 134,92 C116.326888,92 102,77.673112 102,60 C102,42.326888 116.326888,28 134,28 Z M143.290323,70.4242424 L124.709677,70.4242424 L124.709677,73 L143.290323,73 L143.290323,70.4242424 Z M134.228588,39.0452501 C133.92168,38.9192433 133.570539,39.0654217 133.444293,39.3717487 L133.444293,39.3717487 L132.606717,41.4040695 C132.545704,41.5521145 132.427925,41.6696702 132.279599,41.7305681 L132.279599,41.7305681 L130.243422,42.566558 C130.095096,42.6274559 129.977317,42.7450116 129.916304,42.8930566 C129.790058,43.1993836 129.936514,43.5498594 130.243422,43.6758662 L130.243422,43.6758662 L132.279599,44.5118561 C132.427925,44.572754 132.545704,44.6903097 132.606717,44.8383547 L132.606717,44.8383547 L132.73217,45.1423075 C132.979811,46.3305177 132.742808,47.2328448 132.020589,47.8489795 C127.746036,45.9271492 124.290712,45.6976302 121.654619,47.1604224 C116.539893,50.752269 117.710489,55.0435115 119.66627,58.4322762 L119.66627,58.4322762 L119.848873,58.7424754 L120.034976,59.047268 C120.066231,59.0976007 120.097592,59.1476964 120.129039,59.1975494 L120.129039,59.1975494 L120.318587,59.4937074 L120.509231,59.7837589 L120.795148,60.2067742 L121.078122,60.6144751 L121.79366,61.6196209 L122.191192,62.1811609 L122.403677,62.4912261 L122.532483,62.6861618 L122.649887,62.8713514 C123.887477,64.8738302 124.633007,66.876309 124.886476,68.8787879 L124.886476,68.8787879 L143.113524,68.8787879 L143.151138,68.6057226 C143.435785,66.6942655 144.168777,64.7828084 145.350113,62.8713514 L145.350113,62.8713514 L145.467517,62.6861618 L145.596323,62.4912261 L145.73557,62.2868242 L145.884296,62.073236 L146.465605,61.2573781 L146.921878,60.6144751 L147.204852,60.2067742 L147.395337,59.9264073 L147.586168,59.639514 L147.776384,59.3463742 L147.965024,59.047268 L148.151127,58.7424754 L148.33373,58.4322762 C150.289511,55.0435115 151.460107,50.752269 146.345381,47.1604224 C143.71998,45.7035632 140.28198,45.9253152 136.030816,47.8261831 C135.213798,47.089098 134.967766,46.1739073 135.29272,45.0806109 L135.29272,45.0806109 L135.393283,44.8383547 C135.454296,44.6903097 135.572075,44.572754 135.720401,44.5118561 L135.720401,44.5118561 L137.756578,43.6758662 C137.904904,43.6149683 138.022683,43.4974126 138.083696,43.3493676 C138.209942,43.0430406 138.063486,42.6925648 137.756578,42.566558 L137.756578,42.566558 L135.720401,41.7305681 C135.572075,41.6696702 135.454296,41.5521145 135.393283,41.4040695 L135.393283,41.4040695 L134.555707,39.3717487 C134.494693,39.2237037 134.376914,39.1061481 134.228588,39.0452501 Z M123.771612,52.2847753 C124.202992,50.6258513 125.98706,49.9481248 129.123817,50.251596 C128.113945,51.0129195 127.609009,52.2804467 127.609009,54.0541776 C127.609009,55.3441637 128.140891,56.3750301 128.68889,57.2991624 L128.68889,57.2991624 L129.094852,57.97463 C129.520458,58.689865 129.871583,59.3597884 129.871583,60.0661363 C129.871583,61.2614942 129.254376,62.2987253 128.019964,63.1778297 C128.09943,61.8510157 127.597049,60.4362598 126.51282,58.9335621 L126.51282,58.9335621 L126.300128,58.6414514 L125.45908,57.5032748 L125.257522,57.2240048 L125.061954,56.9459275 L124.873633,56.6685844 L124.693812,56.391517 C123.876436,55.098466 123.377744,53.7994452 123.771612,52.2847753 Z M138.876183,50.251596 C142.01294,49.9481248 143.797008,50.6258513 144.228388,52.2847753 C144.622256,53.7994452 144.123564,55.098466 143.306188,56.391517 L143.306188,56.391517 L143.126367,56.6685844 L142.938046,56.9459275 L142.742478,57.2240048 L142.438286,57.6435003 L141.806402,58.4967316 L141.48718,58.9335621 C140.402951,60.4362598 139.90057,61.8510157 139.980036,63.1778297 C138.745624,62.2987253 138.128417,61.2614942 138.128417,60.0661363 C138.128417,59.3597884 138.479542,58.689865 138.905148,57.97463 L138.905148,57.97463 L139.31111,57.2991624 C139.859109,56.3750301 140.390991,55.3441637 140.390991,54.0541776 C140.390991,52.2804467 139.886055,51.0129195 138.876183,50.251596 Z" fill="url(#linearGradient-4)" opacity="0.294320243" mask="url(#mask-3)"></path>
                </g>
            </g>
        </g>
    </g>
</svg>

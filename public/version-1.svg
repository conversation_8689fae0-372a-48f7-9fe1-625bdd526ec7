<?xml version="1.0" encoding="UTF-8"?>
<svg width="164px" height="80px" preserveAspectRatio="none" viewBox="0 0 164 80" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>形状结合</title>
    <defs>
        <linearGradient x1="0.34922353%" y1="38.185419%" x2="100%" y2="61.89768%" id="linearGradient-1">
            <stop stop-color="#00CF64" offset="0%"></stop>
            <stop stop-color="#66D13B" offset="100%"></stop>
        </linearGradient>
        <path d="M5,0 L159,0 C161.761424,-5.07265313e-16 164,2.23857625 164,5 L164,80 L164,80 L0,80 L0,5 C-3.38176876e-16,2.23857625 2.23857625,5.07265313e-16 5,0 Z" id="path-2"></path>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="01-价格" transform="translate(-552.000000, -88.000000)">
            <g id="编组-5" transform="translate(260.000000, 88.000000)">
                <g id="形状结合" transform="translate(292.000000, 0.000000)">
                    <mask id="mask-3" fill="white">
                        <use xlink:href="#path-2"></use>
                    </mask>
                    <use id="蒙版" fill="url(#linearGradient-1)" xlink:href="#path-2"></use>
                    <path d="M138,28 C155.673112,28 170,42.326888 170,60 C170,77.673112 155.673112,92 138,92 C120.326888,92 106,77.673112 106,60 C106,42.326888 120.326888,28 138,28 Z M147.05536,47.0715591 C143.931663,45.765409 140.350729,46.5386546 137.999989,49.0269223 C135.649249,46.5386546 132.068316,45.765409 128.944619,47.0715591 C125.820922,48.3777093 123.777778,51.5026509 123.777778,54.9738301 C123.775585,57.4692575 124.83728,59.8399364 126.681956,61.4586148 L126.681956,61.4586148 L135.812614,69.7161482 C136.86091,70.6641918 138.422343,70.689414 139.49901,69.7756953 L139.49901,69.7756953 L148.873341,61.8227312 C150.985028,60.2188776 152.228634,57.6755208 152.222222,54.9738301 C152.222222,51.5026509 150.179057,48.3777093 147.05536,47.0715591 Z" fill="#FFFFFF" opacity="0.200000003" mask="url(#mask-3)"></path>
                </g>
            </g>
        </g>
    </g>
</svg>

<?xml version="1.0" encoding="UTF-8"?>
<svg width="164px" height="80px" preserveAspectRatio="none" viewBox="0 0 164 80" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>形状结合</title>
    <defs>
        <linearGradient x1="0%" y1="38.10232%" x2="100%" y2="61.89768%" id="linearGradient-1">
            <stop stop-color="#0B3FEB" offset="0%"></stop>
            <stop stop-color="#1779FF" offset="100%"></stop>
        </linearGradient>
        <path d="M5,0 L159,0 C161.761424,-5.07265313e-16 164,2.23857625 164,5 L164,80 L164,80 L0,80 L0,5 C-3.38176876e-16,2.23857625 2.23857625,5.07265313e-16 5,0 Z" id="path-2"></path>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="01-价格" transform="translate(-1074.000000, -88.000000)">
            <g id="编组-5" transform="translate(260.000000, 88.000000)">
                <g id="形状结合" transform="translate(814.000000, 0.000000)">
                    <mask id="mask-3" fill="white">
                        <use xlink:href="#path-2"></use>
                    </mask>
                    <use id="蒙版" fill="url(#linearGradient-1)" xlink:href="#path-2"></use>
                    <path d="M138,28 C155.673112,28 170,42.326888 170,60 C170,77.673112 155.673112,92 138,92 C120.326888,92 106,77.673112 106,60 C106,42.326888 120.326888,28 138,28 Z M149.634652,67.0633157 L126.365272,67.0633157 C125.998807,67.0633157 125.698973,67.3590669 125.698973,67.7297973 L125.698973,69.3335185 C125.698973,69.7000833 125.994643,70 126.365272,70 L149.634652,70 C150.001117,70 150.300951,69.7042488 150.300951,69.3335185 L150.300951,67.7297973 C150.300951,67.3590669 150.001117,67.0633157 149.634652,67.0633157 Z M137.999962,40 C136.738159,40 135.713724,41.0247154 135.713724,42.2868648 C135.713724,43.3782283 136.479968,44.2904749 137.504402,44.5154124 C137.475252,44.544571 137.437772,44.561233 137.416951,44.5987226 L131.491056,54.1835601 C131.303659,54.4876423 130.908045,54.5959456 130.591553,54.4334907 L123.004075,50.5012497 C122.496023,50.2388225 121.904682,50.6762011 122.012956,51.2427104 L125.594864,64.6139961 C125.657329,64.9389059 125.940506,65.1721744 126.269491,65.1721744 L149.734597,65.1721744 C150.063582,65.1721744 150.346759,64.9389059 150.409225,64.6139961 L153.986968,51.2385448 C154.095242,50.6762011 153.508066,50.234657 152.995849,50.5012497 L145.408371,54.4334907 C145.087715,54.6001111 144.696265,54.4918078 144.508868,54.1835601 L138.582973,44.5987226 C138.557987,44.561233 138.520508,44.544571 138.495522,44.5154124 C139.519956,44.2904749 140.2862,43.3782283 140.2862,42.2868648 C140.2862,41.0247154 139.261765,40 137.999962,40 Z" fill="#FFFFFF" opacity="0.200000003" mask="url(#mask-3)"></path>
                </g>
            </g>
        </g>
    </g>
</svg>
